using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddProductionTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BatchProductions",
                schema: "data",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MovementId = table.Column<int>(type: "int", nullable: false),
                    DestinationId = table.Column<int>(type: "int", nullable: false),
                    MarketId = table.Column<int>(type: "int", nullable: false),
                    DefectId = table.Column<int>(type: "int", nullable: false),
                    BathId = table.Column<int>(type: "int", nullable: false),
                    StrapId = table.Column<int>(type: "int", nullable: false),
                    IsStacking = table.Column<bool>(type: "bit", nullable: false),
                    IsHt = table.Column<bool>(type: "bit", nullable: false),
                    IsPainting = table.Column<bool>(type: "bit", nullable: false),
                    IsSeparating = table.Column<bool>(type: "bit", nullable: false),
                    IsTrimmed = table.Column<bool>(type: "bit", nullable: false),
                    IsWoodenBlock = table.Column<bool>(type: "bit", nullable: false),
                    StatusId = table.Column<int>(type: "int", nullable: false),
                    MaterialId = table.Column<int>(type: "int", nullable: false),
                    BatchCode = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BatchProductions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Baths_BathId",
                        column: x => x.BathId,
                        principalSchema: "features",
                        principalTable: "Baths",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Data_MaterialId",
                        column: x => x.MaterialId,
                        principalSchema: "material",
                        principalTable: "Data",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Defects_DefectId",
                        column: x => x.DefectId,
                        principalSchema: "features",
                        principalTable: "Defects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Destinations_DestinationId",
                        column: x => x.DestinationId,
                        principalSchema: "features",
                        principalTable: "Destinations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Markets_MarketId",
                        column: x => x.MarketId,
                        principalSchema: "features",
                        principalTable: "Markets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Movements_MovementId",
                        column: x => x.MovementId,
                        principalTable: "Movements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Status_StatusId",
                        column: x => x.StatusId,
                        principalSchema: "features",
                        principalTable: "Status",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BatchProductions_Straps_StrapId",
                        column: x => x.StrapId,
                        principalSchema: "features",
                        principalTable: "Straps",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_BathId",
                schema: "data",
                table: "BatchProductions",
                column: "BathId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_DefectId",
                schema: "data",
                table: "BatchProductions",
                column: "DefectId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_DestinationId",
                schema: "data",
                table: "BatchProductions",
                column: "DestinationId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_MarketId",
                schema: "data",
                table: "BatchProductions",
                column: "MarketId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_MaterialId",
                schema: "data",
                table: "BatchProductions",
                column: "MaterialId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_MovementId",
                schema: "data",
                table: "BatchProductions",
                column: "MovementId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_StatusId",
                schema: "data",
                table: "BatchProductions",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_BatchProductions_StrapId",
                schema: "data",
                table: "BatchProductions",
                column: "StrapId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BatchProductions",
                schema: "data");
        }
    }
}
