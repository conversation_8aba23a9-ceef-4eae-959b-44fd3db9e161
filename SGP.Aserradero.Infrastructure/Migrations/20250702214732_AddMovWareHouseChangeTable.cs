using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMovWareHouseChangeTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Mov_WareHouseChange",
                schema: "movement",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EntryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MovementId = table.Column<int>(type: "int", nullable: false),
                    BatchId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mov_WareHouseChange", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Mov_WareHouseChange_Batches_BatchId",
                        column: x => x.BatchId,
                        principalSchema: "batch",
                        principalTable: "Batches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Mov_WareHouseChange_Movements_MovementId",
                        column: x => x.MovementId,
                        principalSchema: "movement",
                        principalTable: "Movements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Mov_WareHouseChange_BatchId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_Mov_WareHouseChange_MovementId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                column: "MovementId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Mov_WareHouseChange",
                schema: "movement");
        }
    }
}
