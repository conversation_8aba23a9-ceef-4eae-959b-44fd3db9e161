using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class movementEntitesFixes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BatchAuditLogs_Movements_MovementId",
                schema: "batch",
                table: "BatchAuditLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_BatchMovements_Movements_MovementId",
                schema: "batch",
                table: "BatchMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycle_Cameras_CamerasId",
                schema: "movements",
                table: "DryingCycle");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycle_OrderClasses_OrderClassId",
                schema: "movements",
                table: "DryingCycle");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycleDetails_DryingCycle_DryingCycleId",
                schema: "movements",
                table: "DryingCycleDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycleDetails_Movements_MovementId",
                schema: "movements",
                table: "DryingCycleDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_ChemicalBaths_Batches_BatchId",
                schema: "movement",
                table: "Mov_ChemicalBaths");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_ChemicalBaths_Baths_BathId",
                schema: "movement",
                table: "Mov_ChemicalBaths");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_ChemicalBaths_Movements_MovementId",
                schema: "movement",
                table: "Mov_ChemicalBaths");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_LogConsumptions_Movements_MovementId",
                schema: "movement",
                table: "Mov_LogConsumptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_Productions_BatchMovements_BatchMovementId",
                schema: "movement",
                table: "Mov_Productions");

            migrationBuilder.DropForeignKey(
                name: "FK_mov_stacking_Batches_BatchId",
                schema: "movement",
                table: "mov_stacking");

            migrationBuilder.DropForeignKey(
                name: "FK_mov_stacking_Movements_MovementId",
                schema: "movement",
                table: "mov_stacking");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_WareHouseChange_Batches_BatchId",
                schema: "movement",
                table: "Mov_WareHouseChange");

            migrationBuilder.DropForeignKey(
                name: "FK_Mov_WareHouseChange_Movements_MovementId",
                schema: "movement",
                table: "Mov_WareHouseChange");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_Capturers_CapturerId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_Document_DocumentId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_Machines_MachineId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_ManufacturingOrders_ManufacturingOrderId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_MovementTypes_MovementTypeId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropForeignKey(
                name: "FK_Movements_Warehouses_WarehouseId",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Movements",
                schema: "movement",
                table: "Movements");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Mov_WareHouseChange",
                schema: "movement",
                table: "Mov_WareHouseChange");

            migrationBuilder.DropPrimaryKey(
                name: "PK_mov_stacking",
                schema: "movement",
                table: "mov_stacking");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Mov_Productions",
                schema: "movement",
                table: "Mov_Productions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Mov_LogConsumptions",
                schema: "movement",
                table: "Mov_LogConsumptions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Mov_ChemicalBaths",
                schema: "movement",
                table: "Mov_ChemicalBaths");

            migrationBuilder.DropPrimaryKey(
                name: "PK_DryingCycle",
                schema: "movements",
                table: "DryingCycle");

            migrationBuilder.RenameTable(
                name: "MovementTypes",
                schema: "movement",
                newName: "MovementTypes",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "MovementTypeOrderClasses",
                schema: "movement",
                newName: "MovementTypeOrderClasses",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "BatchReclassificationData",
                schema: "movement",
                newName: "BatchReclassificationData",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "BatchReclassification",
                schema: "movement",
                newName: "BatchReclassification",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "Movements",
                schema: "movement",
                newName: "BaseMovements",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "Mov_WareHouseChange",
                schema: "movement",
                newName: "WarehouseChanges",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "mov_stacking",
                schema: "movement",
                newName: "Stackings",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "Mov_Productions",
                schema: "movement",
                newName: "BatchProductions",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "Mov_LogConsumptions",
                schema: "movement",
                newName: "LogConsumptions",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "Mov_ChemicalBaths",
                schema: "movement",
                newName: "ChemicalBathMovements",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "DryingCycle",
                schema: "movements",
                newName: "DryingCycles",
                newSchema: "movements");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_WarehouseId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_WarehouseId");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_MovementTypeId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_MovementTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_ManufacturingOrderId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_ManufacturingOrderId");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_MachineId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_MachineId");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_Id",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_Id");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_DocumentId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_DocumentId");

            migrationBuilder.RenameIndex(
                name: "IX_Movements_CapturerId",
                schema: "movements",
                table: "BaseMovements",
                newName: "IX_BaseMovements_CapturerId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_WareHouseChange_MovementId",
                schema: "movements",
                table: "WarehouseChanges",
                newName: "IX_WarehouseChanges_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_WareHouseChange_BatchId",
                schema: "movements",
                table: "WarehouseChanges",
                newName: "IX_WarehouseChanges_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_mov_stacking_MovementId",
                schema: "movements",
                table: "Stackings",
                newName: "IX_Stackings_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_mov_stacking_BatchId",
                schema: "movements",
                table: "Stackings",
                newName: "IX_Stackings_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_Productions_BatchMovementId",
                schema: "movements",
                table: "BatchProductions",
                newName: "IX_BatchProductions_BatchMovementId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_LogConsumptions_MovementId",
                schema: "movements",
                table: "LogConsumptions",
                newName: "IX_LogConsumptions_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_ChemicalBaths_MovementId",
                schema: "movements",
                table: "ChemicalBathMovements",
                newName: "IX_ChemicalBathMovements_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_ChemicalBaths_BathId",
                schema: "movements",
                table: "ChemicalBathMovements",
                newName: "IX_ChemicalBathMovements_BathId");

            migrationBuilder.RenameIndex(
                name: "IX_Mov_ChemicalBaths_BatchId",
                schema: "movements",
                table: "ChemicalBathMovements",
                newName: "IX_ChemicalBathMovements_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_DryingCycle_OrderClassId",
                schema: "movements",
                table: "DryingCycles",
                newName: "IX_DryingCycles_OrderClassId");

            migrationBuilder.RenameIndex(
                name: "IX_DryingCycle_CamerasId",
                schema: "movements",
                table: "DryingCycles",
                newName: "IX_DryingCycles_CamerasId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_BaseMovements",
                schema: "movements",
                table: "BaseMovements",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_WarehouseChanges",
                schema: "movements",
                table: "WarehouseChanges",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Stackings",
                schema: "movements",
                table: "Stackings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_BatchProductions",
                schema: "movements",
                table: "BatchProductions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_LogConsumptions",
                schema: "movements",
                table: "LogConsumptions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ChemicalBathMovements",
                schema: "movements",
                table: "ChemicalBathMovements",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_DryingCycles",
                schema: "movements",
                table: "DryingCycles",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_Capturers_CapturerId",
                schema: "movements",
                table: "BaseMovements",
                column: "CapturerId",
                principalTable: "Capturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_Document_DocumentId",
                schema: "movements",
                table: "BaseMovements",
                column: "DocumentId",
                principalTable: "Document",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_Machines_MachineId",
                schema: "movements",
                table: "BaseMovements",
                column: "MachineId",
                principalTable: "Machines",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_ManufacturingOrders_ManufacturingOrderId",
                schema: "movements",
                table: "BaseMovements",
                column: "ManufacturingOrderId",
                principalTable: "ManufacturingOrders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_MovementTypes_MovementTypeId",
                schema: "movements",
                table: "BaseMovements",
                column: "MovementTypeId",
                principalSchema: "movements",
                principalTable: "MovementTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BaseMovements_Warehouses_WarehouseId",
                schema: "movements",
                table: "BaseMovements",
                column: "WarehouseId",
                principalTable: "Warehouses",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BatchAuditLogs_BaseMovements_MovementId",
                schema: "batch",
                table: "BatchAuditLogs",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BatchMovements_BaseMovements_MovementId",
                schema: "batch",
                table: "BatchMovements",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BatchProductions_BatchMovements_BatchMovementId",
                schema: "movements",
                table: "BatchProductions",
                column: "BatchMovementId",
                principalSchema: "batch",
                principalTable: "BatchMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ChemicalBathMovements_BaseMovements_MovementId",
                schema: "movements",
                table: "ChemicalBathMovements",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ChemicalBathMovements_Batches_BatchId",
                schema: "movements",
                table: "ChemicalBathMovements",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ChemicalBathMovements_Baths_BathId",
                schema: "movements",
                table: "ChemicalBathMovements",
                column: "BathId",
                principalSchema: "features",
                principalTable: "Baths",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycleDetails_BaseMovements_MovementId",
                schema: "movements",
                table: "DryingCycleDetails",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycleDetails_DryingCycles_DryingCycleId",
                schema: "movements",
                table: "DryingCycleDetails",
                column: "DryingCycleId",
                principalSchema: "movements",
                principalTable: "DryingCycles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycles_Cameras_CamerasId",
                schema: "movements",
                table: "DryingCycles",
                column: "CamerasId",
                principalTable: "Cameras",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycles_OrderClasses_OrderClassId",
                schema: "movements",
                table: "DryingCycles",
                column: "OrderClassId",
                principalTable: "OrderClasses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_LogConsumptions_BaseMovements_MovementId",
                schema: "movements",
                table: "LogConsumptions",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Stackings_BaseMovements_MovementId",
                schema: "movements",
                table: "Stackings",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Stackings_Batches_BatchId",
                schema: "movements",
                table: "Stackings",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WarehouseChanges_BaseMovements_MovementId",
                schema: "movements",
                table: "WarehouseChanges",
                column: "MovementId",
                principalSchema: "movements",
                principalTable: "BaseMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_WarehouseChanges_Batches_BatchId",
                schema: "movements",
                table: "WarehouseChanges",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_Capturers_CapturerId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_Document_DocumentId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_Machines_MachineId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_ManufacturingOrders_ManufacturingOrderId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_MovementTypes_MovementTypeId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseMovements_Warehouses_WarehouseId",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BatchAuditLogs_BaseMovements_MovementId",
                schema: "batch",
                table: "BatchAuditLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_BatchMovements_BaseMovements_MovementId",
                schema: "batch",
                table: "BatchMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_BatchProductions_BatchMovements_BatchMovementId",
                schema: "movements",
                table: "BatchProductions");

            migrationBuilder.DropForeignKey(
                name: "FK_ChemicalBathMovements_BaseMovements_MovementId",
                schema: "movements",
                table: "ChemicalBathMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_ChemicalBathMovements_Batches_BatchId",
                schema: "movements",
                table: "ChemicalBathMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_ChemicalBathMovements_Baths_BathId",
                schema: "movements",
                table: "ChemicalBathMovements");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycleDetails_BaseMovements_MovementId",
                schema: "movements",
                table: "DryingCycleDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycleDetails_DryingCycles_DryingCycleId",
                schema: "movements",
                table: "DryingCycleDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycles_Cameras_CamerasId",
                schema: "movements",
                table: "DryingCycles");

            migrationBuilder.DropForeignKey(
                name: "FK_DryingCycles_OrderClasses_OrderClassId",
                schema: "movements",
                table: "DryingCycles");

            migrationBuilder.DropForeignKey(
                name: "FK_LogConsumptions_BaseMovements_MovementId",
                schema: "movements",
                table: "LogConsumptions");

            migrationBuilder.DropForeignKey(
                name: "FK_Stackings_BaseMovements_MovementId",
                schema: "movements",
                table: "Stackings");

            migrationBuilder.DropForeignKey(
                name: "FK_Stackings_Batches_BatchId",
                schema: "movements",
                table: "Stackings");

            migrationBuilder.DropForeignKey(
                name: "FK_WarehouseChanges_BaseMovements_MovementId",
                schema: "movements",
                table: "WarehouseChanges");

            migrationBuilder.DropForeignKey(
                name: "FK_WarehouseChanges_Batches_BatchId",
                schema: "movements",
                table: "WarehouseChanges");

            migrationBuilder.DropPrimaryKey(
                name: "PK_WarehouseChanges",
                schema: "movements",
                table: "WarehouseChanges");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Stackings",
                schema: "movements",
                table: "Stackings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LogConsumptions",
                schema: "movements",
                table: "LogConsumptions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_DryingCycles",
                schema: "movements",
                table: "DryingCycles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ChemicalBathMovements",
                schema: "movements",
                table: "ChemicalBathMovements");

            migrationBuilder.DropPrimaryKey(
                name: "PK_BatchProductions",
                schema: "movements",
                table: "BatchProductions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_BaseMovements",
                schema: "movements",
                table: "BaseMovements");

            migrationBuilder.EnsureSchema(
                name: "movement");

            migrationBuilder.RenameTable(
                name: "MovementTypes",
                schema: "movements",
                newName: "MovementTypes",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "MovementTypeOrderClasses",
                schema: "movements",
                newName: "MovementTypeOrderClasses",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "BatchReclassificationData",
                schema: "movements",
                newName: "BatchReclassificationData",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "BatchReclassification",
                schema: "movements",
                newName: "BatchReclassification",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "WarehouseChanges",
                schema: "movements",
                newName: "Mov_WareHouseChange",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "Stackings",
                schema: "movements",
                newName: "mov_stacking",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "LogConsumptions",
                schema: "movements",
                newName: "Mov_LogConsumptions",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "DryingCycles",
                schema: "movements",
                newName: "DryingCycle",
                newSchema: "movements");

            migrationBuilder.RenameTable(
                name: "ChemicalBathMovements",
                schema: "movements",
                newName: "Mov_ChemicalBaths",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "BatchProductions",
                schema: "movements",
                newName: "Mov_Productions",
                newSchema: "movement");

            migrationBuilder.RenameTable(
                name: "BaseMovements",
                schema: "movements",
                newName: "Movements",
                newSchema: "movement");

            migrationBuilder.RenameIndex(
                name: "IX_WarehouseChanges_MovementId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                newName: "IX_Mov_WareHouseChange_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_WarehouseChanges_BatchId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                newName: "IX_Mov_WareHouseChange_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_Stackings_MovementId",
                schema: "movement",
                table: "mov_stacking",
                newName: "IX_mov_stacking_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_Stackings_BatchId",
                schema: "movement",
                table: "mov_stacking",
                newName: "IX_mov_stacking_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_LogConsumptions_MovementId",
                schema: "movement",
                table: "Mov_LogConsumptions",
                newName: "IX_Mov_LogConsumptions_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_DryingCycles_OrderClassId",
                schema: "movements",
                table: "DryingCycle",
                newName: "IX_DryingCycle_OrderClassId");

            migrationBuilder.RenameIndex(
                name: "IX_DryingCycles_CamerasId",
                schema: "movements",
                table: "DryingCycle",
                newName: "IX_DryingCycle_CamerasId");

            migrationBuilder.RenameIndex(
                name: "IX_ChemicalBathMovements_MovementId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                newName: "IX_Mov_ChemicalBaths_MovementId");

            migrationBuilder.RenameIndex(
                name: "IX_ChemicalBathMovements_BathId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                newName: "IX_Mov_ChemicalBaths_BathId");

            migrationBuilder.RenameIndex(
                name: "IX_ChemicalBathMovements_BatchId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                newName: "IX_Mov_ChemicalBaths_BatchId");

            migrationBuilder.RenameIndex(
                name: "IX_BatchProductions_BatchMovementId",
                schema: "movement",
                table: "Mov_Productions",
                newName: "IX_Mov_Productions_BatchMovementId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_WarehouseId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_WarehouseId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_MovementTypeId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_MovementTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_ManufacturingOrderId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_ManufacturingOrderId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_MachineId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_MachineId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_Id",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_Id");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_DocumentId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_DocumentId");

            migrationBuilder.RenameIndex(
                name: "IX_BaseMovements_CapturerId",
                schema: "movement",
                table: "Movements",
                newName: "IX_Movements_CapturerId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Mov_WareHouseChange",
                schema: "movement",
                table: "Mov_WareHouseChange",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_mov_stacking",
                schema: "movement",
                table: "mov_stacking",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Mov_LogConsumptions",
                schema: "movement",
                table: "Mov_LogConsumptions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_DryingCycle",
                schema: "movements",
                table: "DryingCycle",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Mov_ChemicalBaths",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Mov_Productions",
                schema: "movement",
                table: "Mov_Productions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Movements",
                schema: "movement",
                table: "Movements",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BatchAuditLogs_Movements_MovementId",
                schema: "batch",
                table: "BatchAuditLogs",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BatchMovements_Movements_MovementId",
                schema: "batch",
                table: "BatchMovements",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycle_Cameras_CamerasId",
                schema: "movements",
                table: "DryingCycle",
                column: "CamerasId",
                principalTable: "Cameras",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycle_OrderClasses_OrderClassId",
                schema: "movements",
                table: "DryingCycle",
                column: "OrderClassId",
                principalTable: "OrderClasses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycleDetails_DryingCycle_DryingCycleId",
                schema: "movements",
                table: "DryingCycleDetails",
                column: "DryingCycleId",
                principalSchema: "movements",
                principalTable: "DryingCycle",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_DryingCycleDetails_Movements_MovementId",
                schema: "movements",
                table: "DryingCycleDetails",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_ChemicalBaths_Batches_BatchId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_ChemicalBaths_Baths_BathId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                column: "BathId",
                principalSchema: "features",
                principalTable: "Baths",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_ChemicalBaths_Movements_MovementId",
                schema: "movement",
                table: "Mov_ChemicalBaths",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_LogConsumptions_Movements_MovementId",
                schema: "movement",
                table: "Mov_LogConsumptions",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_Productions_BatchMovements_BatchMovementId",
                schema: "movement",
                table: "Mov_Productions",
                column: "BatchMovementId",
                principalSchema: "batch",
                principalTable: "BatchMovements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_mov_stacking_Batches_BatchId",
                schema: "movement",
                table: "mov_stacking",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_mov_stacking_Movements_MovementId",
                schema: "movement",
                table: "mov_stacking",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_WareHouseChange_Batches_BatchId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                column: "BatchId",
                principalSchema: "batch",
                principalTable: "Batches",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Mov_WareHouseChange_Movements_MovementId",
                schema: "movement",
                table: "Mov_WareHouseChange",
                column: "MovementId",
                principalSchema: "movement",
                principalTable: "Movements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_Capturers_CapturerId",
                schema: "movement",
                table: "Movements",
                column: "CapturerId",
                principalTable: "Capturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_Document_DocumentId",
                schema: "movement",
                table: "Movements",
                column: "DocumentId",
                principalTable: "Document",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_Machines_MachineId",
                schema: "movement",
                table: "Movements",
                column: "MachineId",
                principalTable: "Machines",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_ManufacturingOrders_ManufacturingOrderId",
                schema: "movement",
                table: "Movements",
                column: "ManufacturingOrderId",
                principalTable: "ManufacturingOrders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_MovementTypes_MovementTypeId",
                schema: "movement",
                table: "Movements",
                column: "MovementTypeId",
                principalSchema: "movement",
                principalTable: "MovementTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Movements_Warehouses_WarehouseId",
                schema: "movement",
                table: "Movements",
                column: "WarehouseId",
                principalTable: "Warehouses",
                principalColumn: "Id");
        }
    }
}
