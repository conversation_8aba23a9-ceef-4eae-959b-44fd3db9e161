using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Material_Fixes_003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Data_Grades_MaterialGradeId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropForeignKey(
                name: "FK_Data_Qualities_MaterialQualityId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropTable(
                name: "Qualities",
                schema: "material");

            migrationBuilder.DropColumn(
                name: "Code",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Diameter",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Inches",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Long",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Name",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Thickness",
                schema: "material",
                table: "Data");

            migrationBuilder.RenameColumn(
                name: "MaterialQualityId",
                schema: "material",
                table: "Data",
                newName: "MaterialTypeId");

            migrationBuilder.RenameColumn(
                name: "MaterialGradeId",
                schema: "material",
                table: "Data",
                newName: "MaterialSubFamilyId");

            migrationBuilder.RenameIndex(
                name: "IX_Data_MaterialQualityId",
                schema: "material",
                table: "Data",
                newName: "IX_Data_MaterialTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Data_MaterialGradeId",
                schema: "material",
                table: "Data",
                newName: "IX_Data_MaterialSubFamilyId");

            migrationBuilder.AlterColumn<double>(
                name: "Width",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: true,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<string>(
                name: "Pieces",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<string>(
                name: "Dimension",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "DescriptionAlt",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Height",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsBlocked",
                schema: "material",
                table: "Data",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "MaterialFinishId",
                schema: "material",
                table: "Data",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "NameLength",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NameThickness",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NameWidth",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Price",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SapCode",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Weight",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Data_MaterialFinishId",
                schema: "material",
                table: "Data",
                column: "MaterialFinishId");

            migrationBuilder.AddForeignKey(
                name: "FK_Data_Finish_MaterialFinishId",
                schema: "material",
                table: "Data",
                column: "MaterialFinishId",
                principalSchema: "material",
                principalTable: "Finish",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Data_SubFamilies_MaterialSubFamilyId",
                schema: "material",
                table: "Data",
                column: "MaterialSubFamilyId",
                principalSchema: "material",
                principalTable: "SubFamilies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Data_Types_MaterialTypeId",
                schema: "material",
                table: "Data",
                column: "MaterialTypeId",
                principalSchema: "material",
                principalTable: "Types",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Data_Finish_MaterialFinishId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropForeignKey(
                name: "FK_Data_SubFamilies_MaterialSubFamilyId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropForeignKey(
                name: "FK_Data_Types_MaterialTypeId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropIndex(
                name: "IX_Data_MaterialFinishId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "DescriptionAlt",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Height",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "IsBlocked",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "MaterialFinishId",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "NameLength",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "NameThickness",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "NameWidth",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Price",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "SapCode",
                schema: "material",
                table: "Data");

            migrationBuilder.DropColumn(
                name: "Weight",
                schema: "material",
                table: "Data");

            migrationBuilder.RenameColumn(
                name: "MaterialTypeId",
                schema: "material",
                table: "Data",
                newName: "MaterialQualityId");

            migrationBuilder.RenameColumn(
                name: "MaterialSubFamilyId",
                schema: "material",
                table: "Data",
                newName: "MaterialGradeId");

            migrationBuilder.RenameIndex(
                name: "IX_Data_MaterialTypeId",
                schema: "material",
                table: "Data",
                newName: "IX_Data_MaterialQualityId");

            migrationBuilder.RenameIndex(
                name: "IX_Data_MaterialSubFamilyId",
                schema: "material",
                table: "Data",
                newName: "IX_Data_MaterialGradeId");

            migrationBuilder.AlterColumn<double>(
                name: "Width",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0,
                oldClrType: typeof(double),
                oldType: "float",
                oldNullable: true);

            migrationBuilder.AlterColumn<double>(
                name: "Pieces",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Dimension",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "Diameter",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Inches",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Long",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "Thickness",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.CreateTable(
                name: "Qualities",
                schema: "material",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MaterialClassId = table.Column<int>(type: "int", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<int>(type: "int", nullable: true),
                    Deleted = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<int>(type: "int", nullable: true),
                    LastModified = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastModifiedBy = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Qualities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Qualities_MaterialClasses_MaterialClassId",
                        column: x => x.MaterialClassId,
                        principalTable: "MaterialClasses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Qualities_MaterialClassId",
                schema: "material",
                table: "Qualities",
                column: "MaterialClassId");

            migrationBuilder.AddForeignKey(
                name: "FK_Data_Grades_MaterialGradeId",
                schema: "material",
                table: "Data",
                column: "MaterialGradeId",
                principalSchema: "material",
                principalTable: "Grades",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Data_Qualities_MaterialQualityId",
                schema: "material",
                table: "Data",
                column: "MaterialQualityId",
                principalSchema: "material",
                principalTable: "Qualities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
