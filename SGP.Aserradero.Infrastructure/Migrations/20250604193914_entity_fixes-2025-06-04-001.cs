using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class entity_fixes20250604001 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<double>(
                name: "Pieces",
                schema: "material",
                table: "Data",
                type: "float",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WarehouseId",
                schema: "batch",
                table: "Batches",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Batches_WarehouseId",
                schema: "batch",
                table: "Batches",
                column: "WarehouseId");

            migrationBuilder.AddForeignKey(
                name: "FK_Batches_Warehouses_WarehouseId",
                schema: "batch",
                table: "Batches",
                column: "WarehouseId",
                principalTable: "Warehouses",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Batches_Warehouses_WarehouseId",
                schema: "batch",
                table: "Batches");

            migrationBuilder.DropIndex(
                name: "IX_Batches_WarehouseId",
                schema: "batch",
                table: "Batches");

            migrationBuilder.DropColumn(
                name: "WarehouseId",
                schema: "batch",
                table: "Batches");

            migrationBuilder.AlterColumn<string>(
                name: "Pieces",
                schema: "material",
                table: "Data",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(double),
                oldType: "float",
                oldNullable: true);
        }
    }
}
