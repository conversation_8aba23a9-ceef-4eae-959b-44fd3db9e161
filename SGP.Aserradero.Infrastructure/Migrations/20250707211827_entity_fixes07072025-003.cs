using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SGP.Aserradero.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class entity_fixes07072025003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Mov_DryingOutput",
                schema: "movement",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EntryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MovementId = table.Column<int>(type: "int", nullable: false),
                    BatchId = table.Column<int>(type: "int", nullable: false),
                    DryingCycleId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mov_DryingOutput", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Mov_DryingOutput_Batches_BatchId",
                        column: x => x.BatchId,
                        principalSchema: "batch",
                        principalTable: "Batches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Mov_DryingOutput_DryingCycle_DryingCycleId",
                        column: x => x.DryingCycleId,
                        principalSchema: "main",
                        principalTable: "DryingCycle",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Mov_DryingOutput_Movements_MovementId",
                        column: x => x.MovementId,
                        principalSchema: "movement",
                        principalTable: "Movements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Mov_DryingOutput_BatchId",
                schema: "movement",
                table: "Mov_DryingOutput",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_Mov_DryingOutput_DryingCycleId",
                schema: "movement",
                table: "Mov_DryingOutput",
                column: "DryingCycleId");

            migrationBuilder.CreateIndex(
                name: "IX_Mov_DryingOutput_MovementId",
                schema: "movement",
                table: "Mov_DryingOutput",
                column: "MovementId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Mov_DryingOutput",
                schema: "movement");
        }
    }
}
