using System.Data.Common;
using Ext.SAP.Queries.Models;
using Ext.SAP.Queries.Modules.Base;
using MediatR;
using Serilog;

namespace Ext.SAP.Queries.Modules.GetInvoices;

public sealed class GetInvoicesHandler : BaseSapQuery<PERSON>and<PERSON>, IRequestHandler<GetInvoicesCommand, GetInvoicesResponse>
{
    public GetInvoicesHandler(IServiceProvider serviceProvider)
        : base(serviceProvider) { }

    public async Task<GetInvoicesResponse> Handle(GetInvoicesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            string queryName = "GetInvoices";
            object[] queryParams =
            [
                request.companyName.Remove(0, 5),
                request.SapDatabaseName,
                request.StartDate.ToString("yyyyMMdd"),
                request.EndDate.ToString("yyyyMMdd"),
            ];

            var result = await ExecuteQueryAsync(queryName, MapResults, cancellationToken, queryParams);
            return new GetInvoicesResponse("OK", result);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error al obtener el archivo de invoices");
            return new GetInvoicesResponse("ERROR", null);
        }
    }

    private static async Task<List<InvoicesData>> MapResults(DbDataReader reader)
    {
        var itemsList = new List<InvoicesData>();

        while (await reader.ReadAsync())
        {
            var item = new InvoicesData
            {
                DocEntry = reader["DocEntry"].ToString(),
                DocNum = reader["DocNum"].ToString(),
                FolioPref = reader["FolioPref"].ToString(),
                FolioNum = reader["FolioNum"].ToString(),
                TransId = reader["TransId"].ToString(),
                ObjType = reader["ObjType"].ToString(),
                DocDate = reader["DocDate"].ToString(),
                TaxDate = reader["TaxDate"].ToString(),
                Canceled = reader["Canceled"].ToString(),
                CardCode = reader["CardCode"].ToString(),
                SocioDeNegocio = reader["SocioDeNegocio"].ToString(),
                Comentarios = reader["Comentarios"].ToString(),
                ItemCode = reader["ItemCode"].ToString(),
                NumAtCard = reader["NumAtCard"].ToString(),
                VatSum = decimal.TryParse(reader["VatSum"]?.ToString(), out decimal vatSum)
                    ? vatSum.ToString("G0")
                    : null,
                Currency = reader["Currency"].ToString(),
                Total = decimal.TryParse(reader["Total"]?.ToString(), out decimal total) ? total.ToString("G0") : null,
                DocTotal = decimal.TryParse(reader["DocTotal"]?.ToString(), out decimal docTotal)
                    ? docTotal.ToString("G0")
                    : null,
                Dscription = reader["Dscription"].ToString(),
                ActId = reader["ActId"].ToString(),
                AcctName = reader["AcctName"].ToString(),
                WhsCode = reader["WhsCode"].ToString(),
                WhsName = reader["WhsName"].ToString(),
                NombreArticulo = reader["NombreArticulo"].ToString(),
                ItmsGrpNam = reader["ItmsGrpNam"].ToString(),
                ItemDeGasto = reader["ItemDeGasto"].ToString(),
                Ejecutivo = reader["Ejecutivo"].ToString(),
                NoInvtryMv = reader["NoInvtryMv"].ToString(),
                Empresa = reader["Empresa"].ToString(),
            };
            itemsList.Add(item);
        }

        return itemsList;
    }
}
