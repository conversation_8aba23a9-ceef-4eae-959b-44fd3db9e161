using System.Reflection;
using System.Text.RegularExpressions;
using Serilog;

namespace Ext.SAP.Queries.Helpers;

public static class SqlQueryLoader
{
    public static async Task<string> LoadQueryAsync(string queryName)
    {
        Log.Information("Loading SQL query: {QueryName}", queryName);
        var assembly = Assembly.GetExecutingAssembly();
        string resourceName = $"Ext.SAP.Queries.src.Queries.{queryName}.sql";

        await using var stream = assembly.GetManifestResourceStream(resourceName);
        if (stream == null)
        {
            Log.Error("Query file not found: {QueryName}.sql", queryName);
            throw new FileNotFoundException($"Query file {queryName}.sql not found");
        }

        using var reader = new StreamReader(stream);
        var query = await reader.ReadToEndAsync();
        Log.Information("Successfully loaded SQL query: {QueryName}", queryName);
        return query;
    }

    /// <summary>
    /// Formatea una consulta SQL con parámetros aplicando el formato adecuado según el tipo de dato.
    /// Esta implementación permite evitar el uso explícito de comillas en los archivos SQL.
    /// </summary>
    /// <param name="query">La consulta SQL con marcadores de posición</param>
    /// <param name="parameters">Los parámetros para reemplazar</param>
    /// <returns>Consulta SQL formateada</returns>
    public static string FormatSqlQuery(string query, params object[] parameters)
    {
        if (parameters == null || parameters.Length == 0)
        {
            return query;
        }

        // Preparar los parámetros formateados
        var formattedParams = new object[parameters.Length];

        for (int i = 0; i < parameters.Length; i++)
        {
            var param = parameters[i];

            // Verificar si necesita comillas
            if (param is string or DateTime or Guid)
            {
                // Ya tiene comillas en el SQL, mantenemos el parámetro tal cual
                // El string.Format añadirá el valor en esas comillas
                formattedParams[i] = param;
            }
            else if (param is int or long or double or decimal or float or bool)
            {
                // Para valores numéricos y booleanos
                // Necesitamos convertirlos a string para poder manipular las comillas
                formattedParams[i] = param.ToString()!;
            }
            else if (param == null)
            {
                formattedParams[i] = "NULL";
            }
            else
            {
                // Otros tipos, mantenemos tal cual
                formattedParams[i] = param;
            }
        }

        // Realizar el reemplazo estándar con string.Format
        return string.Format(query, formattedParams);
    }

    /// <summary>
    /// Método avanzado que preprocesa la consulta SQL para usar parámetros sin comillas
    /// cuando se trata de tipos numéricos o booleanos. Mantiene compatibilidad
    /// con el esquema existente.
    /// </summary>
    public static string PreprocessAndFormatSql(string query, params object[] parameters)
    {
        if (parameters == null || parameters.Length == 0)
        {
            return query;
        }

        // Primero, identificamos qué parámetros son numéricos o booleanos
        var numericBoolParams = new HashSet<int>();
        for (int i = 0; i < parameters.Length; i++)
        {
            if (parameters[i] is int or long or double or decimal or float or bool)
            {
                numericBoolParams.Add(i);
            }
        }

        // Si hay parámetros numéricos o booleanos, transformamos sus ocurrencias en el SQL
        if (numericBoolParams.Count > 0)
        {
            // Patrón para encontrar parámetros entrecomillados: '{n}'
            var pattern = @"'{\d+}'";
            query = Regex.Replace(
                query,
                pattern,
                match =>
                {
                    // Extraer el índice del parámetro
                    var indexMatch = Regex.Match(match.Value, @"\d+");
                    if (indexMatch.Success && int.TryParse(indexMatch.Value, out int index))
                    {
                        // Si el parámetro es numérico o booleano, quitamos las comillas
                        if (index < parameters.Length && numericBoolParams.Contains(index))
                        {
                            return "{" + index + "}";
                        }
                    }
                    // Mantener inalterado si no es numérico/booleano o si está fuera de rango
                    return match.Value;
                }
            );
        }

        // Aplicar el formato estándar
        return string.Format(query, parameters);
    }
}
