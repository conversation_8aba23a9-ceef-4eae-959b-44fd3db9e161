using Ext.SAP.Queries.Config;
using Ext.SAP.Queries.Modules.GetClassification;
using Ext.SAP.Queries.Modules.GetItems;
using Ext.SAP.Queries.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Ext.SAP.Queries;

public static class SapQueriesModule
{
    public static void ConfigureServices(IServiceCollection services, IConfiguration config)
    {
        // Load the SAP configuration from the application configuration.
        services.Configure<SapQueriesConfig>(options => config.GetSection(nameof(SapQueriesConfig)).Bind(options));

        services.AddScoped<ISapQueriesService, SapQueriesService>();

        // Add MediaTR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssemblies(typeof(GetClassificationHandler).Assembly);
        });
    }
}
