using System.Net;
using Leonera_API_ExternalServices.Modules.Core;

namespace Leonera_API_ExternalServices.Modules.InventoryGenExits;

public class InventoryGenExitsService(CoreService coreService)
{
    private const string UriPath = "InventoryGenExits";

    public async Task<(HttpStatusCode StatusCode, string? Content)> MakeInventoryExit(object payload, string? auth)
    {
        return await coreService.ExecutePostApiCall(UriPath, payload, auth);
    }
}
