using Newtonsoft.Json;

namespace Leonera_API_ExternalServices.Modules.InventoryGenExits.Dtos;

public class InventoryGenExitsDto
{
    [JsonProperty("DocDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime DocDate { get; set; }

    [JsonProperty("DocDueDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime? DocDueDate { get; set; }

    [JsonProperty("DocCurrency", NullValueHandling = NullValueHandling.Ignore)]
    public string? DocCurrency { get; set; }

    [JsonProperty("Comments", NullValueHandling = NullValueHandling.Ignore)]
    public string? Comments { get; set; }

    [JsonProperty("DocumentLines", NullValueHandling = NullValueHandling.Ignore)]
    public List<ExitsDocumentLine>? DocumentLines { get; set; }

    public class ExitsDocumentLine
    {
        [JsonProperty("ItemCode", NullValueHandling = NullValueHandling.Ignore)]
        public string? ItemCode { get; set; }

        [JsonProperty("Quantity", NullValueHandling = NullValueHandling.Ignore)]
        public double Quantity { get; set; }

        [JsonProperty("WarehouseCode", NullValueHandling = NullValueHandling.Ignore)]
        public string? WarehouseCode { get; set; }

        [JsonProperty("U_movin", NullValueHandling = NullValueHandling.Ignore)]
        public string? Movement { get; set; }

        [JsonProperty("BatchNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<BatchNumbersLine>? BatchNumbers { get; set; }

        [JsonProperty("CostingCode", NullValueHandling = NullValueHandling.Ignore)]
        public string? CostingCode { get; set; }

        [JsonProperty("CostingCode2", NullValueHandling = NullValueHandling.Ignore)]
        public string? CostingCode2 { get; set; }

        [JsonProperty("CostingCode3", NullValueHandling = NullValueHandling.Ignore)]
        public string? CostingCode3 { get; set; }

        [JsonProperty("CostingCode4", NullValueHandling = NullValueHandling.Ignore)]
        public string? CostingCode4 { get; set; }
    }

    public class BatchNumbersLine
    {
        [JsonProperty("ItemCode", NullValueHandling = NullValueHandling.Ignore)]
        public string? ItemCode { get; set; }

        [JsonProperty("BatchNumber", NullValueHandling = NullValueHandling.Ignore)]
        public string? BatchNumber { get; set; }

        [JsonProperty("Quantity", NullValueHandling = NullValueHandling.Ignore)]
        public double Quantity { get; set; }
    }
}
