using System.Net;
using Leonera_API_ExternalServices.Modules.Core;

namespace Leonera_API_ExternalServices.Modules.U_DeudaProgramada;

/// <summary>
///     Service for managing U_DeudaProgramada in the SAP Service Layer.
/// </summary>
public class ProgrammedDebtService(CoreService coreService)
{
    private const string Uri = "U_DEUDAPROGRAMADA_FC";
    private const string DateFormat = "yyyy-MM-dd";

    /// <summary>
    ///     Retrieves all programmed debts from a specified database that meet certain criteria.
    /// </summary>
    /// <param name="auth">Authentication token for the database.</param>
    /// <param name="dateStart">Start date for the debts.</param>
    /// <param name="dateFinish">End date for the debts.</param>
    /// <returns>A string representation of the content retrieved from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetAllDeudaprogramada(
        string? auth,
        DateTime dateStart,
        DateTime dateFinish
    )
    {
        // Formateando las fechas al formato requerido
        string formattedDateStart = dateStart.ToString(DateFormat);
        string formattedDateFinish = dateFinish.ToString(DateFormat);
        string url =
            $"{Uri}?$filter=U_FechaVcto ge '{formattedDateStart}'" + $" and U_FechaVcto le '{formattedDateFinish}'";
        return await coreService.ExecuteGetApiCall(url, auth);
    }

    /// <summary>
    ///     Retrieves all programmed debts from a specified database that meet certain criteria.
    /// </summary>
    /// <param name="auth">Authentication token for the database.</param>
    /// <param name="bplId">Business Partner ID to filter the debts by.</param>
    /// <param name="dateStart">Start date for the debts.</param>
    /// <param name="dateFinish">End date for the debts.</param>
    /// <returns>A string representation of the content retrieved from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetAllDeudaprogramada(
        string? auth,
        int? bplId,
        DateTime dateStart,
        DateTime dateFinish
    )
    {
        // Formateando las fechas al formato requerido
        string formattedDateStart = dateStart.ToString(DateFormat);
        string formattedDateFinish = dateFinish.ToString(DateFormat);
        string todayFormattedDate = DateTime.Now.ToString(DateFormat);

        string url =
            $"{Uri}?$filter=U_FechaVcto ge '{formattedDateStart}' and U_FechaVcto le '{formattedDateFinish}'"
            + $" and U_FechaVcto ge '{todayFormattedDate}' and U_BPLId eq '{bplId}'";
        return await coreService.ExecuteGetApiCall(url, auth);
    }

    /// <summary>
    ///     Retrieves a programmed debt by its ID.
    /// </summary>
    /// <param name="auth">Authentication token for the database.</param>
    /// <param name="id">ID of the debt to retrieve.</param>
    /// <returns>A string representation of the content retrieved from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetDeudaprogramadaById(string? auth, int id)
    {
        string url = $"{Uri}({id})";
        return await coreService.ExecuteGetApiCall(url, auth);
    }

    /// <summary>
    ///     Retrieves a programmed debt by its code.
    /// </summary>
    /// <param name="auth">Authentication token for the database.</param>
    /// <param name="code">Code of the debt to retrieve.</param>
    /// <returns>A string representation of the content retrieved from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetDeudaprogramadaByCode(string? auth, int code)
    {
        string url = $"{Uri}?$filter=startswith(Code,'{code}')";
        return await coreService.ExecuteGetApiCall(url, auth);
    }

    /// <summary>
    ///     Retrieves a programmed debt by its code and business place ID.
    /// </summary>
    /// <param name="auth">Authentication token for the database.</param>
    /// <param name="code">Code of the debt to retrieve.</param>
    /// <param name="bplId">Business place ID to filter the debts by.</param>
    /// <returns>A string representation of the content retrieved from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetDeudaprogramadaByCode(
        string? auth,
        int code,
        int? bplId
    )
    {
        string url = $"{Uri}?$filter=startswith(Code,'{code}') and U_BPLId eq '{bplId}'";
        return await coreService.ExecuteGetApiCall(url, auth);
    }

    /// <summary>
    ///     Creates a new programmed debt.
    /// </summary>
    /// <param name="payload">Data transfer object containing the details of the debt to create.</param>
    /// <param name="auth">Authentication token for the database.</param>
    /// <returns>A tuple containing the HTTP status code and a string representation of the response from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> CreateProgrammedDebt(object payload, string? auth)
    {
        return await coreService.ExecutePostApiCall(Uri, payload, auth);
    }

    /// <summary>
    ///     Updates an existing programmed debt.
    /// </summary>
    /// <param name="id">ID of the debt to update.</param>
    /// <param name="payload">Data transfer object containing the updated details of the debt.</param>
    /// <param name="auth">Authentication token for the database.</param>
    /// <returns>A tuple containing the HTTP status code and a string representation of the response from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> UpdateProgrammedDebt(
        int id,
        object payload,
        string? auth
    )
    {
        string url = $"{Uri}({id})";
        return await coreService.ExecutePatchApiCall(url, payload, auth);
    }

    /// <summary>
    ///     Deletes a programmed debt.
    /// </summary>
    /// <param name="id">ID of the debt to delete.</param>
    /// <param name="auth">Authentication token for the database.</param>
    /// <returns>A tuple containing the HTTP status code and a string representation of the response from the database.</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> DeleteProgrammedDebt(int id, string? auth)
    {
        string url = $"{Uri}({id})";
        return await coreService.ExecuteDeleteApiCall(url, auth);
    }
}
