using Newtonsoft.Json;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Leonera_API_ExternalServices.Modules.PurchaseInvoices.Dtos;

public class PurchaseInvoicesResponse
{
    public class AddressExtension
    {
        [JsonProperty("ShipToStreet", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToStreet { get; set; }

        [JsonProperty("ShipToStreetNo", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToStreetNo { get; set; }

        [JsonProperty("ShipToBlock", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToBlock { get; set; }

        [JsonProperty("ShipToBuilding", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToBuilding { get; set; }

        [JsonProperty("ShipToCity", NullValueHandling = NullValueHandling.Ignore)]
        public string ShipToCity { get; set; }

        [JsonProperty("ShipToZipCode", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToZipCode { get; set; }

        [JsonProperty("ShipToCounty", NullValueHandling = NullValueHandling.Ignore)]
        public string ShipToCounty { get; set; }

        [JsonProperty("ShipToState", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToState { get; set; }

        [JsonProperty("ShipToCountry", NullValueHandling = NullValueHandling.Ignore)]
        public string ShipToCountry { get; set; }

        [JsonProperty("ShipToAddressType", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToAddressType { get; set; }

        [JsonProperty("BillToStreet", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToStreet { get; set; }

        [JsonProperty("BillToStreetNo", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToStreetNo { get; set; }

        [JsonProperty("BillToBlock", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToBlock { get; set; }

        [JsonProperty("BillToBuilding", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToBuilding { get; set; }

        [JsonProperty("BillToCity", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToCity { get; set; }

        [JsonProperty("BillToZipCode", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToZipCode { get; set; }

        [JsonProperty("BillToCounty", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToCounty { get; set; }

        [JsonProperty("BillToState", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToState { get; set; }

        [JsonProperty("BillToCountry", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToCountry { get; set; }

        [JsonProperty("BillToAddressType", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToAddressType { get; set; }

        [JsonProperty("ShipToGlobalLocationNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToGlobalLocationNumber { get; set; }

        [JsonProperty("BillToGlobalLocationNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToGlobalLocationNumber { get; set; }

        [JsonProperty("ShipToAddress2", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToAddress2 { get; set; }

        [JsonProperty("ShipToAddress3", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToAddress3 { get; set; }

        [JsonProperty("BillToAddress2", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToAddress2 { get; set; }

        [JsonProperty("BillToAddress3", NullValueHandling = NullValueHandling.Ignore)]
        public object BillToAddress3 { get; set; }

        [JsonProperty("PlaceOfSupply", NullValueHandling = NullValueHandling.Ignore)]
        public object PlaceOfSupply { get; set; }

        [JsonProperty("PurchasePlaceOfSupply", NullValueHandling = NullValueHandling.Ignore)]
        public object PurchasePlaceOfSupply { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int DocEntry { get; set; }

        [JsonProperty("GoodsIssuePlaceBP", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceBP { get; set; }

        [JsonProperty("GoodsIssuePlaceCNPJ", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceCNPJ { get; set; }

        [JsonProperty("GoodsIssuePlaceCPF", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceCPF { get; set; }

        [JsonProperty("GoodsIssuePlaceStreet", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceStreet { get; set; }

        [JsonProperty("GoodsIssuePlaceStreetNo", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceStreetNo { get; set; }

        [JsonProperty("GoodsIssuePlaceBuilding", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceBuilding { get; set; }

        [JsonProperty("GoodsIssuePlaceZip", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceZip { get; set; }

        [JsonProperty("GoodsIssuePlaceBlock", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceBlock { get; set; }

        [JsonProperty("GoodsIssuePlaceCity", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceCity { get; set; }

        [JsonProperty("GoodsIssuePlaceCounty", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceCounty { get; set; }

        [JsonProperty("GoodsIssuePlaceState", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceState { get; set; }

        [JsonProperty("GoodsIssuePlaceCountry", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceCountry { get; set; }

        [JsonProperty("GoodsIssuePlacePhone", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlacePhone { get; set; }

        [JsonProperty("GoodsIssuePlaceEMail", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceEMail { get; set; }

        [JsonProperty("GoodsIssuePlaceDepartureDate", NullValueHandling = NullValueHandling.Ignore)]
        public object GoodsIssuePlaceDepartureDate { get; set; }

        [JsonProperty("DeliveryPlaceBP", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceBP { get; set; }

        [JsonProperty("DeliveryPlaceCNPJ", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceCNPJ { get; set; }

        [JsonProperty("DeliveryPlaceCPF", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceCPF { get; set; }

        [JsonProperty("DeliveryPlaceStreet", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceStreet { get; set; }

        [JsonProperty("DeliveryPlaceStreetNo", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceStreetNo { get; set; }

        [JsonProperty("DeliveryPlaceBuilding", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceBuilding { get; set; }

        [JsonProperty("DeliveryPlaceZip", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceZip { get; set; }

        [JsonProperty("DeliveryPlaceBlock", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceBlock { get; set; }

        [JsonProperty("DeliveryPlaceCity", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceCity { get; set; }

        [JsonProperty("DeliveryPlaceCounty", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceCounty { get; set; }

        [JsonProperty("DeliveryPlaceState", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceState { get; set; }

        [JsonProperty("DeliveryPlaceCountry", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceCountry { get; set; }

        [JsonProperty("DeliveryPlacePhone", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlacePhone { get; set; }

        [JsonProperty("DeliveryPlaceEMail", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceEMail { get; set; }

        [JsonProperty("DeliveryPlaceDepartureDate", NullValueHandling = NullValueHandling.Ignore)]
        public object DeliveryPlaceDepartureDate { get; set; }

        [JsonProperty("U_zonaS", NullValueHandling = NullValueHandling.Ignore)]
        public object U_zonaS { get; set; }

        [JsonProperty("U_zonaB", NullValueHandling = NullValueHandling.Ignore)]
        public object U_zonaB { get; set; }
    }

    public class DocumentInstallment
    {
        [JsonProperty("DueDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime DueDate { get; set; }

        [JsonProperty("Percentage", NullValueHandling = NullValueHandling.Ignore)]
        public double Percentage { get; set; }

        [JsonProperty("Total", NullValueHandling = NullValueHandling.Ignore)]
        public double Total { get; set; }

        [JsonProperty("LastDunningDate", NullValueHandling = NullValueHandling.Ignore)]
        public object LastDunningDate { get; set; }

        [JsonProperty("DunningLevel", NullValueHandling = NullValueHandling.Ignore)]
        public int DunningLevel { get; set; }

        [JsonProperty("TotalFC", NullValueHandling = NullValueHandling.Ignore)]
        public object TotalFC { get; set; }

        [JsonProperty("InstallmentId", NullValueHandling = NullValueHandling.Ignore)]
        public int InstallmentId { get; set; }

        [JsonProperty("PaymentOrdered", NullValueHandling = NullValueHandling.Ignore)]
        public string PaymentOrdered { get; set; }
    }

    public class DocumentLine
    {
        [JsonProperty("LineNum", NullValueHandling = NullValueHandling.Ignore)]
        public int LineNum { get; set; }

        [JsonProperty("ItemCode", NullValueHandling = NullValueHandling.Ignore)]
        public string ItemCode { get; set; }

        [JsonProperty("ItemDescription", NullValueHandling = NullValueHandling.Ignore)]
        public string ItemDescription { get; set; }

        [JsonProperty("Quantity", NullValueHandling = NullValueHandling.Ignore)]
        public double Quantity { get; set; }

        [JsonProperty("ShipDate", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipDate { get; set; }

        [JsonProperty("Price", NullValueHandling = NullValueHandling.Ignore)]
        public double Price { get; set; }

        [JsonProperty("PriceAfterVAT", NullValueHandling = NullValueHandling.Ignore)]
        public double PriceAfterVAT { get; set; }

        [JsonProperty("Currency", NullValueHandling = NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [JsonProperty("Rate", NullValueHandling = NullValueHandling.Ignore)]
        public double Rate { get; set; }

        [JsonProperty("DiscountPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double DiscountPercent { get; set; }

        [JsonProperty("VendorNum", NullValueHandling = NullValueHandling.Ignore)]
        public string VendorNum { get; set; }

        [JsonProperty("SerialNum", NullValueHandling = NullValueHandling.Ignore)]
        public string SerialNum { get; set; }

        [JsonProperty("WarehouseCode", NullValueHandling = NullValueHandling.Ignore)]
        public string WarehouseCode { get; set; }

        [JsonProperty("SalesPersonCode", NullValueHandling = NullValueHandling.Ignore)]
        public int SalesPersonCode { get; set; }

        [JsonProperty("CommisionPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double CommisionPercent { get; set; }

        [JsonProperty("TreeType", NullValueHandling = NullValueHandling.Ignore)]
        public string TreeType { get; set; }

        [JsonProperty("AccountCode", NullValueHandling = NullValueHandling.Ignore)]
        public string AccountCode { get; set; }

        [JsonProperty("UseBaseUnits", NullValueHandling = NullValueHandling.Ignore)]
        public string UseBaseUnits { get; set; }

        [JsonProperty("SupplierCatNum", NullValueHandling = NullValueHandling.Ignore)]
        public string SupplierCatNum { get; set; }

        [JsonProperty("CostingCode", NullValueHandling = NullValueHandling.Ignore)]
        public string CostingCode { get; set; }

        [JsonProperty("ProjectCode", NullValueHandling = NullValueHandling.Ignore)]
        public string ProjectCode { get; set; }

        [JsonProperty("BarCode", NullValueHandling = NullValueHandling.Ignore)]
        public object BarCode { get; set; }

        [JsonProperty("VatGroup", NullValueHandling = NullValueHandling.Ignore)]
        public string VatGroup { get; set; }

        [JsonProperty("Height1", NullValueHandling = NullValueHandling.Ignore)]
        public double Height1 { get; set; }

        [JsonProperty("Hight1Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Hight1Unit { get; set; }

        [JsonProperty("Height2", NullValueHandling = NullValueHandling.Ignore)]
        public double Height2 { get; set; }

        [JsonProperty("Height2Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Height2Unit { get; set; }

        [JsonProperty("Lengh1", NullValueHandling = NullValueHandling.Ignore)]
        public double Lengh1 { get; set; }

        [JsonProperty("Lengh1Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Lengh1Unit { get; set; }

        [JsonProperty("Lengh2", NullValueHandling = NullValueHandling.Ignore)]
        public double Lengh2 { get; set; }

        [JsonProperty("Lengh2Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Lengh2Unit { get; set; }

        [JsonProperty("Weight1", NullValueHandling = NullValueHandling.Ignore)]
        public double Weight1 { get; set; }

        [JsonProperty("Weight1Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Weight1Unit { get; set; }

        [JsonProperty("Weight2", NullValueHandling = NullValueHandling.Ignore)]
        public double Weight2 { get; set; }

        [JsonProperty("Weight2Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Weight2Unit { get; set; }

        [JsonProperty("Factor1", NullValueHandling = NullValueHandling.Ignore)]
        public double Factor1 { get; set; }

        [JsonProperty("Factor2", NullValueHandling = NullValueHandling.Ignore)]
        public double Factor2 { get; set; }

        [JsonProperty("Factor3", NullValueHandling = NullValueHandling.Ignore)]
        public double Factor3 { get; set; }

        [JsonProperty("Factor4", NullValueHandling = NullValueHandling.Ignore)]
        public double Factor4 { get; set; }

        [JsonProperty("BaseType", NullValueHandling = NullValueHandling.Ignore)]
        public int BaseType { get; set; }

        [JsonProperty("BaseEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object BaseEntry { get; set; }

        [JsonProperty("BaseLine", NullValueHandling = NullValueHandling.Ignore)]
        public object BaseLine { get; set; }

        [JsonProperty("Volume", NullValueHandling = NullValueHandling.Ignore)]
        public double Volume { get; set; }

        [JsonProperty("VolumeUnit", NullValueHandling = NullValueHandling.Ignore)]
        public int VolumeUnit { get; set; }

        [JsonProperty("Width1", NullValueHandling = NullValueHandling.Ignore)]
        public double Width1 { get; set; }

        [JsonProperty("Width1Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Width1Unit { get; set; }

        [JsonProperty("Width2", NullValueHandling = NullValueHandling.Ignore)]
        public double Width2 { get; set; }

        [JsonProperty("Width2Unit", NullValueHandling = NullValueHandling.Ignore)]
        public object Width2Unit { get; set; }

        [JsonProperty("Address", NullValueHandling = NullValueHandling.Ignore)]
        public string Address { get; set; }

        [JsonProperty("TaxCode", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxCode { get; set; }

        [JsonProperty("TaxType", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxType { get; set; }

        [JsonProperty("TaxLiable", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxLiable { get; set; }

        [JsonProperty("PickStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string PickStatus { get; set; }

        [JsonProperty("PickQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double PickQuantity { get; set; }

        [JsonProperty("PickListIdNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object PickListIdNumber { get; set; }

        [JsonProperty("OriginalItem", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalItem { get; set; }

        [JsonProperty("BackOrder", NullValueHandling = NullValueHandling.Ignore)]
        public object BackOrder { get; set; }

        [JsonProperty("FreeText", NullValueHandling = NullValueHandling.Ignore)]
        public string FreeText { get; set; }

        [JsonProperty("ShippingMethod", NullValueHandling = NullValueHandling.Ignore)]
        public int ShippingMethod { get; set; }

        [JsonProperty("POTargetNum", NullValueHandling = NullValueHandling.Ignore)]
        public object POTargetNum { get; set; }

        [JsonProperty("POTargetEntry", NullValueHandling = NullValueHandling.Ignore)]
        public string POTargetEntry { get; set; }

        [JsonProperty("POTargetRowNum", NullValueHandling = NullValueHandling.Ignore)]
        public object POTargetRowNum { get; set; }

        [JsonProperty("CorrectionInvoiceItem", NullValueHandling = NullValueHandling.Ignore)]
        public string CorrectionInvoiceItem { get; set; }

        [JsonProperty("CorrInvAmountToStock", NullValueHandling = NullValueHandling.Ignore)]
        public double CorrInvAmountToStock { get; set; }

        [JsonProperty("CorrInvAmountToDiffAcct", NullValueHandling = NullValueHandling.Ignore)]
        public double CorrInvAmountToDiffAcct { get; set; }

        [JsonProperty("AppliedTax", NullValueHandling = NullValueHandling.Ignore)]
        public double AppliedTax { get; set; }

        [JsonProperty("AppliedTaxFC", NullValueHandling = NullValueHandling.Ignore)]
        public double AppliedTaxFC { get; set; }

        [JsonProperty("AppliedTaxSC", NullValueHandling = NullValueHandling.Ignore)]
        public double AppliedTaxSC { get; set; }

        [JsonProperty("WTLiable", NullValueHandling = NullValueHandling.Ignore)]
        public string WTLiable { get; set; }

        [JsonProperty("DeferredTax", NullValueHandling = NullValueHandling.Ignore)]
        public string DeferredTax { get; set; }

        [JsonProperty("EqualizationTaxPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double EqualizationTaxPercent { get; set; }

        [JsonProperty("TotalEqualizationTax", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTax { get; set; }

        [JsonProperty("TotalEqualizationTaxFC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTaxFC { get; set; }

        [JsonProperty("TotalEqualizationTaxSC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTaxSC { get; set; }

        [JsonProperty("NetTaxAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double NetTaxAmount { get; set; }

        [JsonProperty("NetTaxAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double NetTaxAmountFC { get; set; }

        [JsonProperty("NetTaxAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double NetTaxAmountSC { get; set; }

        [JsonProperty("MeasureUnit", NullValueHandling = NullValueHandling.Ignore)]
        public string MeasureUnit { get; set; }

        [JsonProperty("UnitsOfMeasurment", NullValueHandling = NullValueHandling.Ignore)]
        public double UnitsOfMeasurment { get; set; }

        [JsonProperty("LineTotal", NullValueHandling = NullValueHandling.Ignore)]
        public double LineTotal { get; set; }

        [JsonProperty("TaxPercentagePerRow", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxPercentagePerRow { get; set; }

        [JsonProperty("TaxTotal", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxTotal { get; set; }

        [JsonProperty("ConsumerSalesForecast", NullValueHandling = NullValueHandling.Ignore)]
        public string ConsumerSalesForecast { get; set; }

        [JsonProperty("ExciseAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double ExciseAmount { get; set; }

        [JsonProperty("TaxPerUnit", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxPerUnit { get; set; }

        [JsonProperty("TotalInclTax", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalInclTax { get; set; }

        [JsonProperty("CountryOrg", NullValueHandling = NullValueHandling.Ignore)]
        public object CountryOrg { get; set; }

        [JsonProperty("SWW", NullValueHandling = NullValueHandling.Ignore)]
        public string SWW { get; set; }

        [JsonProperty("TransactionType", NullValueHandling = NullValueHandling.Ignore)]
        public object TransactionType { get; set; }

        [JsonProperty("DistributeExpense", NullValueHandling = NullValueHandling.Ignore)]
        public string DistributeExpense { get; set; }

        [JsonProperty("RowTotalFC", NullValueHandling = NullValueHandling.Ignore)]
        public double RowTotalFC { get; set; }

        [JsonProperty("RowTotalSC", NullValueHandling = NullValueHandling.Ignore)]
        public double RowTotalSC { get; set; }

        [JsonProperty("LastBuyInmPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double LastBuyInmPrice { get; set; }

        [JsonProperty("LastBuyDistributeSumFc", NullValueHandling = NullValueHandling.Ignore)]
        public double LastBuyDistributeSumFc { get; set; }

        [JsonProperty("LastBuyDistributeSumSc", NullValueHandling = NullValueHandling.Ignore)]
        public double LastBuyDistributeSumSc { get; set; }

        [JsonProperty("LastBuyDistributeSum", NullValueHandling = NullValueHandling.Ignore)]
        public double LastBuyDistributeSum { get; set; }

        [JsonProperty("StockDistributesumForeign", NullValueHandling = NullValueHandling.Ignore)]
        public double StockDistributesumForeign { get; set; }

        [JsonProperty("StockDistributesumSystem", NullValueHandling = NullValueHandling.Ignore)]
        public double StockDistributesumSystem { get; set; }

        [JsonProperty("StockDistributesum", NullValueHandling = NullValueHandling.Ignore)]
        public double StockDistributesum { get; set; }

        [JsonProperty("StockInmPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double StockInmPrice { get; set; }

        [JsonProperty("PickStatusEx", NullValueHandling = NullValueHandling.Ignore)]
        public string PickStatusEx { get; set; }

        [JsonProperty("TaxBeforeDPM", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxBeforeDPM { get; set; }

        [JsonProperty("TaxBeforeDPMFC", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxBeforeDPMFC { get; set; }

        [JsonProperty("TaxBeforeDPMSC", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxBeforeDPMSC { get; set; }

        [JsonProperty("CFOPCode", NullValueHandling = NullValueHandling.Ignore)]
        public object CFOPCode { get; set; }

        [JsonProperty("CSTCode", NullValueHandling = NullValueHandling.Ignore)]
        public object CSTCode { get; set; }

        [JsonProperty("Usage", NullValueHandling = NullValueHandling.Ignore)]
        public object Usage { get; set; }

        [JsonProperty("TaxOnly", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxOnly { get; set; }

        [JsonProperty("VisualOrder", NullValueHandling = NullValueHandling.Ignore)]
        public int VisualOrder { get; set; }

        [JsonProperty("BaseOpenQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double BaseOpenQuantity { get; set; }

        [JsonProperty("UnitPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double UnitPrice { get; set; }

        [JsonProperty("LineStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string LineStatus { get; set; }

        [JsonProperty("PackageQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double PackageQuantity { get; set; }

        [JsonProperty("Text", NullValueHandling = NullValueHandling.Ignore)]
        public string Text { get; set; }

        [JsonProperty("LineType", NullValueHandling = NullValueHandling.Ignore)]
        public string LineType { get; set; }

        [JsonProperty("COGSCostingCode", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSCostingCode { get; set; }

        [JsonProperty("COGSAccountCode", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSAccountCode { get; set; }

        [JsonProperty("ChangeAssemlyBoMWarehouse", NullValueHandling = NullValueHandling.Ignore)]
        public string ChangeAssemlyBoMWarehouse { get; set; }

        [JsonProperty("GrossBuyPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossBuyPrice { get; set; }

        [JsonProperty("GrossBase", NullValueHandling = NullValueHandling.Ignore)]
        public object GrossBase { get; set; }

        [JsonProperty("GrossProfitTotalBasePrice", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossProfitTotalBasePrice { get; set; }

        [JsonProperty("CostingCode2", NullValueHandling = NullValueHandling.Ignore)]
        public string CostingCode2 { get; set; }

        [JsonProperty("CostingCode3", NullValueHandling = NullValueHandling.Ignore)]
        public string CostingCode3 { get; set; }

        [JsonProperty("CostingCode4", NullValueHandling = NullValueHandling.Ignore)]
        public string CostingCode4 { get; set; }

        [JsonProperty("CostingCode5", NullValueHandling = NullValueHandling.Ignore)]
        public object CostingCode5 { get; set; }

        [JsonProperty("ItemDetails", NullValueHandling = NullValueHandling.Ignore)]
        public object ItemDetails { get; set; }

        [JsonProperty("LocationCode", NullValueHandling = NullValueHandling.Ignore)]
        public object LocationCode { get; set; }

        [JsonProperty("ActualDeliveryDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ActualDeliveryDate { get; set; }

        [JsonProperty("RemainingOpenQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double RemainingOpenQuantity { get; set; }

        [JsonProperty("OpenAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double OpenAmount { get; set; }

        [JsonProperty("OpenAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double OpenAmountFC { get; set; }

        [JsonProperty("OpenAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double OpenAmountSC { get; set; }

        [JsonProperty("ExLineNo", NullValueHandling = NullValueHandling.Ignore)]
        public object ExLineNo { get; set; }

        [JsonProperty("RequiredDate", NullValueHandling = NullValueHandling.Ignore)]
        public object RequiredDate { get; set; }

        [JsonProperty("RequiredQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double RequiredQuantity { get; set; }

        [JsonProperty("COGSCostingCode2", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSCostingCode2 { get; set; }

        [JsonProperty("COGSCostingCode3", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSCostingCode3 { get; set; }

        [JsonProperty("COGSCostingCode4", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSCostingCode4 { get; set; }

        [JsonProperty("COGSCostingCode5", NullValueHandling = NullValueHandling.Ignore)]
        public object COGSCostingCode5 { get; set; }

        [JsonProperty("CSTforIPI", NullValueHandling = NullValueHandling.Ignore)]
        public object CSTforIPI { get; set; }

        [JsonProperty("CSTforPIS", NullValueHandling = NullValueHandling.Ignore)]
        public object CSTforPIS { get; set; }

        [JsonProperty("CSTforCOFINS", NullValueHandling = NullValueHandling.Ignore)]
        public object CSTforCOFINS { get; set; }

        [JsonProperty("CreditOriginCode", NullValueHandling = NullValueHandling.Ignore)]
        public object CreditOriginCode { get; set; }

        [JsonProperty("WithoutInventoryMovement", NullValueHandling = NullValueHandling.Ignore)]
        public string WithoutInventoryMovement { get; set; }

        [JsonProperty("AgreementNo", NullValueHandling = NullValueHandling.Ignore)]
        public object AgreementNo { get; set; }

        [JsonProperty("AgreementRowNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object AgreementRowNumber { get; set; }

        [JsonProperty("ActualBaseEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object ActualBaseEntry { get; set; }

        [JsonProperty("ActualBaseLine", NullValueHandling = NullValueHandling.Ignore)]
        public object ActualBaseLine { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int DocEntry { get; set; }

        [JsonProperty("Surpluses", NullValueHandling = NullValueHandling.Ignore)]
        public double Surpluses { get; set; }

        [JsonProperty("DefectAndBreakup", NullValueHandling = NullValueHandling.Ignore)]
        public double DefectAndBreakup { get; set; }

        [JsonProperty("Shortages", NullValueHandling = NullValueHandling.Ignore)]
        public double Shortages { get; set; }

        [JsonProperty("ConsiderQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public string ConsiderQuantity { get; set; }

        [JsonProperty("PartialRetirement", NullValueHandling = NullValueHandling.Ignore)]
        public string PartialRetirement { get; set; }

        [JsonProperty("RetirementQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double RetirementQuantity { get; set; }

        [JsonProperty("RetirementAPC", NullValueHandling = NullValueHandling.Ignore)]
        public double RetirementAPC { get; set; }

        [JsonProperty("ThirdParty", NullValueHandling = NullValueHandling.Ignore)]
        public string ThirdParty { get; set; }

        [JsonProperty("PoNum", NullValueHandling = NullValueHandling.Ignore)]
        public object PoNum { get; set; }

        [JsonProperty("PoItmNum", NullValueHandling = NullValueHandling.Ignore)]
        public object PoItmNum { get; set; }

        [JsonProperty("ExpenseType", NullValueHandling = NullValueHandling.Ignore)]
        public object ExpenseType { get; set; }

        [JsonProperty("ReceiptNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object ReceiptNumber { get; set; }

        [JsonProperty("ExpenseOperationType", NullValueHandling = NullValueHandling.Ignore)]
        public object ExpenseOperationType { get; set; }

        [JsonProperty("FederalTaxID", NullValueHandling = NullValueHandling.Ignore)]
        public object FederalTaxID { get; set; }

        [JsonProperty("GrossProfit", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossProfit { get; set; }

        [JsonProperty("GrossProfitFC", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossProfitFC { get; set; }

        [JsonProperty("GrossProfitSC", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossProfitSC { get; set; }

        [JsonProperty("PriceSource", NullValueHandling = NullValueHandling.Ignore)]
        public string PriceSource { get; set; }

        [JsonProperty("StgSeqNum", NullValueHandling = NullValueHandling.Ignore)]
        public object StgSeqNum { get; set; }

        [JsonProperty("StgEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object StgEntry { get; set; }

        [JsonProperty("StgDesc", NullValueHandling = NullValueHandling.Ignore)]
        public object StgDesc { get; set; }

        [JsonProperty("UoMEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int UoMEntry { get; set; }

        [JsonProperty("UoMCode", NullValueHandling = NullValueHandling.Ignore)]
        public string UoMCode { get; set; }

        [JsonProperty("InventoryQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double InventoryQuantity { get; set; }

        [JsonProperty("RemainingOpenInventoryQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double RemainingOpenInventoryQuantity { get; set; }

        [JsonProperty("ParentLineNum", NullValueHandling = NullValueHandling.Ignore)]
        public object ParentLineNum { get; set; }

        [JsonProperty("Incoterms", NullValueHandling = NullValueHandling.Ignore)]
        public int Incoterms { get; set; }

        [JsonProperty("TransportMode", NullValueHandling = NullValueHandling.Ignore)]
        public int TransportMode { get; set; }

        [JsonProperty("NatureOfTransaction", NullValueHandling = NullValueHandling.Ignore)]
        public object NatureOfTransaction { get; set; }

        [JsonProperty("DestinationCountryForImport", NullValueHandling = NullValueHandling.Ignore)]
        public object DestinationCountryForImport { get; set; }

        [JsonProperty("DestinationRegionForImport", NullValueHandling = NullValueHandling.Ignore)]
        public object DestinationRegionForImport { get; set; }

        [JsonProperty("OriginCountryForExport", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginCountryForExport { get; set; }

        [JsonProperty("OriginRegionForExport", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginRegionForExport { get; set; }

        [JsonProperty("ItemType", NullValueHandling = NullValueHandling.Ignore)]
        public string ItemType { get; set; }

        [JsonProperty("ChangeInventoryQuantityIndependently", NullValueHandling = NullValueHandling.Ignore)]
        public string ChangeInventoryQuantityIndependently { get; set; }

        [JsonProperty("FreeOfChargeBP", NullValueHandling = NullValueHandling.Ignore)]
        public string FreeOfChargeBP { get; set; }

        [JsonProperty("SACEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object SACEntry { get; set; }

        [JsonProperty("HSNEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object HSNEntry { get; set; }

        [JsonProperty("GrossPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossPrice { get; set; }

        [JsonProperty("GrossTotal", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossTotal { get; set; }

        [JsonProperty("GrossTotalFC", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossTotalFC { get; set; }

        [JsonProperty("GrossTotalSC", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossTotalSC { get; set; }

        [JsonProperty("NCMCode", NullValueHandling = NullValueHandling.Ignore)]
        public int NCMCode { get; set; }

        [JsonProperty("NVECode", NullValueHandling = NullValueHandling.Ignore)]
        public object NVECode { get; set; }

        [JsonProperty("IndEscala", NullValueHandling = NullValueHandling.Ignore)]
        public string IndEscala { get; set; }

        [JsonProperty("CtrSealQty", NullValueHandling = NullValueHandling.Ignore)]
        public object CtrSealQty { get; set; }

        [JsonProperty("CNJPMan", NullValueHandling = NullValueHandling.Ignore)]
        public object CNJPMan { get; set; }

        [JsonProperty("CESTCode", NullValueHandling = NullValueHandling.Ignore)]
        public object CESTCode { get; set; }

        [JsonProperty("UFFiscalBenefitCode", NullValueHandling = NullValueHandling.Ignore)]
        public object UFFiscalBenefitCode { get; set; }

        [JsonProperty("ReverseCharge", NullValueHandling = NullValueHandling.Ignore)]
        public string ReverseCharge { get; set; }

        [JsonProperty("ShipFromCode", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipFromCode { get; set; }

        [JsonProperty("ShipFromDescription", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipFromDescription { get; set; }

        [JsonProperty("OwnerCode", NullValueHandling = NullValueHandling.Ignore)]
        public object OwnerCode { get; set; }

        [JsonProperty("StandardItemIdentification", NullValueHandling = NullValueHandling.Ignore)]
        public object StandardItemIdentification { get; set; }

        [JsonProperty("CommodityClassification", NullValueHandling = NullValueHandling.Ignore)]
        public object CommodityClassification { get; set; }

        [JsonProperty("WeightOfRecycledPlastic", NullValueHandling = NullValueHandling.Ignore)]
        public object WeightOfRecycledPlastic { get; set; }

        [JsonProperty("PlasticPackageExemptionReason", NullValueHandling = NullValueHandling.Ignore)]
        public object PlasticPackageExemptionReason { get; set; }

        [JsonProperty("LegalText", NullValueHandling = NullValueHandling.Ignore)]
        public object LegalText { get; set; }

        [JsonProperty("UnencumberedReason", NullValueHandling = NullValueHandling.Ignore)]
        public object UnencumberedReason { get; set; }

        [JsonProperty("CUSplit", NullValueHandling = NullValueHandling.Ignore)]
        public string CUSplit { get; set; }

        [JsonProperty("ListNum", NullValueHandling = NullValueHandling.Ignore)]
        public object ListNum { get; set; }

        [JsonProperty("RecognizedTaxCode", NullValueHandling = NullValueHandling.Ignore)]
        public object RecognizedTaxCode { get; set; }

        [JsonProperty("U_cantidadm3", NullValueHandling = NullValueHandling.Ignore)]
        public object U_cantidadm3 { get; set; }

        [JsonProperty("U_cantidadpulgada", NullValueHandling = NullValueHandling.Ignore)]
        public object U_cantidadpulgada { get; set; }

        [JsonProperty("U_FEX_NOMCOMERCIAL", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_NOMCOMERCIAL { get; set; }

        [JsonProperty("U_FEX_NOMCOMERCIAL2", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_NOMCOMERCIAL2 { get; set; }

        [JsonProperty("U_FEX_COM", NullValueHandling = NullValueHandling.Ignore)]
        public string U_FEX_COM { get; set; }

        [JsonProperty("U_volumenm3", NullValueHandling = NullValueHandling.Ignore)]
        public object U_volumenm3 { get; set; }

        [JsonProperty("U_EXX_FE_Descripcion", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_Descripcion { get; set; }

        [JsonProperty("LineTaxJurisdictions", NullValueHandling = NullValueHandling.Ignore)]
        public List<LineTaxJurisdiction> LineTaxJurisdictions { get; set; }

        [JsonProperty("ImportProcesses", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> ImportProcesses { get; set; }

        [JsonProperty("GeneratedAssets", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> GeneratedAssets { get; set; }

        [JsonProperty("EBooksDetails", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> EBooksDetails { get; set; }

        [JsonProperty("DocumentLineAdditionalExpenses", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentLineAdditionalExpenses { get; set; }

        [JsonProperty("WithholdingTaxLines", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> WithholdingTaxLines { get; set; }

        [JsonProperty("SerialNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> SerialNumbers { get; set; }

        [JsonProperty("BatchNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> BatchNumbers { get; set; }

        [JsonProperty("CCDNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> CCDNumbers { get; set; }

        [JsonProperty("DocumentLinesBinAllocations", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentLinesBinAllocations { get; set; }
    }

    public class EWayBillDetails { }

    public class LineTaxJurisdiction
    {
        [JsonProperty("JurisdictionCode", NullValueHandling = NullValueHandling.Ignore)]
        public string JurisdictionCode { get; set; }

        [JsonProperty("JurisdictionType", NullValueHandling = NullValueHandling.Ignore)]
        public int JurisdictionType { get; set; }

        [JsonProperty("TaxAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxAmount { get; set; }

        [JsonProperty("TaxAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxAmountSC { get; set; }

        [JsonProperty("TaxAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxAmountFC { get; set; }

        [JsonProperty("TaxRate", NullValueHandling = NullValueHandling.Ignore)]
        public double TaxRate { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int DocEntry { get; set; }

        [JsonProperty("LineNumber", NullValueHandling = NullValueHandling.Ignore)]
        public int LineNumber { get; set; }

        [JsonProperty("RowSequence", NullValueHandling = NullValueHandling.Ignore)]
        public int RowSequence { get; set; }

        [JsonProperty("ExternalCalcTaxRate", NullValueHandling = NullValueHandling.Ignore)]
        public object ExternalCalcTaxRate { get; set; }

        [JsonProperty("ExternalCalcTaxAmount", NullValueHandling = NullValueHandling.Ignore)]
        public object ExternalCalcTaxAmount { get; set; }

        [JsonProperty("ExternalCalcTaxAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public object ExternalCalcTaxAmountFC { get; set; }

        [JsonProperty("ExternalCalcTaxAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public object ExternalCalcTaxAmountSC { get; set; }

        [JsonProperty("BaseSum", NullValueHandling = NullValueHandling.Ignore)]
        public double BaseSum { get; set; }

        [JsonProperty("TaxInPrice", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxInPrice { get; set; }

        [JsonProperty("NonDeductiblePercent", NullValueHandling = NullValueHandling.Ignore)]
        public double NonDeductiblePercent { get; set; }

        [JsonProperty("TaxOnReserveInvoice", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxOnReserveInvoice { get; set; }

        [JsonProperty("Exempt", NullValueHandling = NullValueHandling.Ignore)]
        public string Exempt { get; set; }

        [JsonProperty("Unencumbered", NullValueHandling = NullValueHandling.Ignore)]
        public string Unencumbered { get; set; }
    }

    public class Root
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<Value> value { get; set; }

        [JsonProperty("@odata.nextLink", NullValueHandling = NullValueHandling.Ignore)]
        public string odatanextLink { get; set; }
    }

    public class TaxExtension
    {
        [JsonProperty("TaxId0", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId0 { get; set; }

        [JsonProperty("TaxId1", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId1 { get; set; }

        [JsonProperty("TaxId2", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId2 { get; set; }

        [JsonProperty("TaxId3", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId3 { get; set; }

        [JsonProperty("TaxId4", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId4 { get; set; }

        [JsonProperty("TaxId5", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId5 { get; set; }

        [JsonProperty("TaxId6", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId6 { get; set; }

        [JsonProperty("TaxId7", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId7 { get; set; }

        [JsonProperty("TaxId8", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId8 { get; set; }

        [JsonProperty("TaxId9", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId9 { get; set; }

        [JsonProperty("State", NullValueHandling = NullValueHandling.Ignore)]
        public object State { get; set; }

        [JsonProperty("County", NullValueHandling = NullValueHandling.Ignore)]
        public object County { get; set; }

        [JsonProperty("Incoterms", NullValueHandling = NullValueHandling.Ignore)]
        public object Incoterms { get; set; }

        [JsonProperty("Vehicle", NullValueHandling = NullValueHandling.Ignore)]
        public object Vehicle { get; set; }

        [JsonProperty("VehicleState", NullValueHandling = NullValueHandling.Ignore)]
        public object VehicleState { get; set; }

        [JsonProperty("NFRef", NullValueHandling = NullValueHandling.Ignore)]
        public object NFRef { get; set; }

        [JsonProperty("Carrier", NullValueHandling = NullValueHandling.Ignore)]
        public object Carrier { get; set; }

        [JsonProperty("PackQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public object PackQuantity { get; set; }

        [JsonProperty("PackDescription", NullValueHandling = NullValueHandling.Ignore)]
        public object PackDescription { get; set; }

        [JsonProperty("Brand", NullValueHandling = NullValueHandling.Ignore)]
        public object Brand { get; set; }

        [JsonProperty("ShipUnitNo", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipUnitNo { get; set; }

        [JsonProperty("NetWeight", NullValueHandling = NullValueHandling.Ignore)]
        public double NetWeight { get; set; }

        [JsonProperty("GrossWeight", NullValueHandling = NullValueHandling.Ignore)]
        public double GrossWeight { get; set; }

        [JsonProperty("StreetS", NullValueHandling = NullValueHandling.Ignore)]
        public object StreetS { get; set; }

        [JsonProperty("BlockS", NullValueHandling = NullValueHandling.Ignore)]
        public object BlockS { get; set; }

        [JsonProperty("BuildingS", NullValueHandling = NullValueHandling.Ignore)]
        public object BuildingS { get; set; }

        [JsonProperty("CityS", NullValueHandling = NullValueHandling.Ignore)]
        public string CityS { get; set; }

        [JsonProperty("ZipCodeS", NullValueHandling = NullValueHandling.Ignore)]
        public object ZipCodeS { get; set; }

        [JsonProperty("CountyS", NullValueHandling = NullValueHandling.Ignore)]
        public string CountyS { get; set; }

        [JsonProperty("StateS", NullValueHandling = NullValueHandling.Ignore)]
        public object StateS { get; set; }

        [JsonProperty("CountryS", NullValueHandling = NullValueHandling.Ignore)]
        public string CountryS { get; set; }

        [JsonProperty("StreetB", NullValueHandling = NullValueHandling.Ignore)]
        public object StreetB { get; set; }

        [JsonProperty("BlockB", NullValueHandling = NullValueHandling.Ignore)]
        public object BlockB { get; set; }

        [JsonProperty("BuildingB", NullValueHandling = NullValueHandling.Ignore)]
        public object BuildingB { get; set; }

        [JsonProperty("CityB", NullValueHandling = NullValueHandling.Ignore)]
        public object CityB { get; set; }

        [JsonProperty("ZipCodeB", NullValueHandling = NullValueHandling.Ignore)]
        public object ZipCodeB { get; set; }

        [JsonProperty("CountyB", NullValueHandling = NullValueHandling.Ignore)]
        public object CountyB { get; set; }

        [JsonProperty("StateB", NullValueHandling = NullValueHandling.Ignore)]
        public object StateB { get; set; }

        [JsonProperty("CountryB", NullValueHandling = NullValueHandling.Ignore)]
        public object CountryB { get; set; }

        [JsonProperty("ImportOrExport", NullValueHandling = NullValueHandling.Ignore)]
        public object ImportOrExport { get; set; }

        [JsonProperty("MainUsage", NullValueHandling = NullValueHandling.Ignore)]
        public object MainUsage { get; set; }

        [JsonProperty("GlobalLocationNumberS", NullValueHandling = NullValueHandling.Ignore)]
        public object GlobalLocationNumberS { get; set; }

        [JsonProperty("GlobalLocationNumberB", NullValueHandling = NullValueHandling.Ignore)]
        public object GlobalLocationNumberB { get; set; }

        [JsonProperty("TaxId12", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId12 { get; set; }

        [JsonProperty("TaxId13", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId13 { get; set; }

        [JsonProperty("BillOfEntryNo", NullValueHandling = NullValueHandling.Ignore)]
        public object BillOfEntryNo { get; set; }

        [JsonProperty("BillOfEntryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object BillOfEntryDate { get; set; }

        [JsonProperty("OriginalBillOfEntryNo", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalBillOfEntryNo { get; set; }

        [JsonProperty("OriginalBillOfEntryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalBillOfEntryDate { get; set; }

        [JsonProperty("ImportOrExportType", NullValueHandling = NullValueHandling.Ignore)]
        public string ImportOrExportType { get; set; }

        [JsonProperty("PortCode", NullValueHandling = NullValueHandling.Ignore)]
        public object PortCode { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int DocEntry { get; set; }

        [JsonProperty("BoEValue", NullValueHandling = NullValueHandling.Ignore)]
        public object BoEValue { get; set; }

        [JsonProperty("ClaimRefund", NullValueHandling = NullValueHandling.Ignore)]
        public object ClaimRefund { get; set; }

        [JsonProperty("DifferentialOfTaxRate", NullValueHandling = NullValueHandling.Ignore)]
        public object DifferentialOfTaxRate { get; set; }

        [JsonProperty("IsIGSTAccount", NullValueHandling = NullValueHandling.Ignore)]
        public object IsIGSTAccount { get; set; }

        [JsonProperty("TaxId14", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxId14 { get; set; }
    }

    public class Value
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int DocEntry { get; set; }

        [JsonProperty("DocNum", NullValueHandling = NullValueHandling.Ignore)]
        public int DocNum { get; set; }

        [JsonProperty("DocType", NullValueHandling = NullValueHandling.Ignore)]
        public string DocType { get; set; }

        [JsonProperty("HandWritten", NullValueHandling = NullValueHandling.Ignore)]
        public string HandWritten { get; set; }

        [JsonProperty("Printed", NullValueHandling = NullValueHandling.Ignore)]
        public string Printed { get; set; }

        [JsonProperty("DocDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime DocDate { get; set; }

        [JsonProperty("DocDueDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime DocDueDate { get; set; }

        [JsonProperty("CardCode", NullValueHandling = NullValueHandling.Ignore)]
        public string CardCode { get; set; }

        [JsonProperty("CardName", NullValueHandling = NullValueHandling.Ignore)]
        public string CardName { get; set; }

        [JsonProperty("Address", NullValueHandling = NullValueHandling.Ignore)]
        public object Address { get; set; }

        [JsonProperty("NumAtCard", NullValueHandling = NullValueHandling.Ignore)]
        public string NumAtCard { get; set; }

        [JsonProperty("DocTotal", NullValueHandling = NullValueHandling.Ignore)]
        public double DocTotal { get; set; }

        [JsonProperty("AttachmentEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object AttachmentEntry { get; set; }

        [JsonProperty("DocCurrency", NullValueHandling = NullValueHandling.Ignore)]
        public string DocCurrency { get; set; }

        [JsonProperty("DocRate", NullValueHandling = NullValueHandling.Ignore)]
        public double DocRate { get; set; }

        [JsonProperty("Reference1", NullValueHandling = NullValueHandling.Ignore)]
        public string Reference1 { get; set; }

        [JsonProperty("Reference2", NullValueHandling = NullValueHandling.Ignore)]
        public object Reference2 { get; set; }

        [JsonProperty("Comments", NullValueHandling = NullValueHandling.Ignore)]
        public string Comments { get; set; }

        [JsonProperty("JournalMemo", NullValueHandling = NullValueHandling.Ignore)]
        public string JournalMemo { get; set; }

        [JsonProperty("PaymentGroupCode", NullValueHandling = NullValueHandling.Ignore)]
        public int PaymentGroupCode { get; set; }

        [JsonProperty("DocTime", NullValueHandling = NullValueHandling.Ignore)]
        public string DocTime { get; set; }

        [JsonProperty("SalesPersonCode", NullValueHandling = NullValueHandling.Ignore)]
        public int SalesPersonCode { get; set; }

        [JsonProperty("TransportationCode", NullValueHandling = NullValueHandling.Ignore)]
        public int TransportationCode { get; set; }

        [JsonProperty("Confirmed", NullValueHandling = NullValueHandling.Ignore)]
        public string Confirmed { get; set; }

        [JsonProperty("ImportFileNum", NullValueHandling = NullValueHandling.Ignore)]
        public int ImportFileNum { get; set; }

        [JsonProperty("SummeryType", NullValueHandling = NullValueHandling.Ignore)]
        public string SummeryType { get; set; }

        [JsonProperty("ContactPersonCode", NullValueHandling = NullValueHandling.Ignore)]
        public int ContactPersonCode { get; set; }

        [JsonProperty("ShowSCN", NullValueHandling = NullValueHandling.Ignore)]
        public string ShowSCN { get; set; }

        [JsonProperty("Series", NullValueHandling = NullValueHandling.Ignore)]
        public int Series { get; set; }

        [JsonProperty("TaxDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime TaxDate { get; set; }

        [JsonProperty("PartialSupply", NullValueHandling = NullValueHandling.Ignore)]
        public string PartialSupply { get; set; }

        [JsonProperty("DocObjectCode", NullValueHandling = NullValueHandling.Ignore)]
        public string DocObjectCode { get; set; }

        [JsonProperty("ShipToCode", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToCode { get; set; }

        [JsonProperty("Indicator", NullValueHandling = NullValueHandling.Ignore)]
        public string Indicator { get; set; }

        [JsonProperty("FederalTaxID", NullValueHandling = NullValueHandling.Ignore)]
        public string FederalTaxID { get; set; }

        [JsonProperty("DiscountPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double DiscountPercent { get; set; }

        [JsonProperty("PaymentReference", NullValueHandling = NullValueHandling.Ignore)]
        public string PaymentReference { get; set; }

        [JsonProperty("CreationDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime CreationDate { get; set; }

        [JsonProperty("UpdateDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime UpdateDate { get; set; }

        [JsonProperty("FinancialPeriod", NullValueHandling = NullValueHandling.Ignore)]
        public int FinancialPeriod { get; set; }

        [JsonProperty("UserSign", NullValueHandling = NullValueHandling.Ignore)]
        public int UserSign { get; set; }

        [JsonProperty("TransNum", NullValueHandling = NullValueHandling.Ignore)]
        public int TransNum { get; set; }

        [JsonProperty("VatSum", NullValueHandling = NullValueHandling.Ignore)]
        public double VatSum { get; set; }

        [JsonProperty("VatSumSys", NullValueHandling = NullValueHandling.Ignore)]
        public double VatSumSys { get; set; }

        [JsonProperty("VatSumFc", NullValueHandling = NullValueHandling.Ignore)]
        public double VatSumFc { get; set; }

        [JsonProperty("NetProcedure", NullValueHandling = NullValueHandling.Ignore)]
        public string NetProcedure { get; set; }

        [JsonProperty("DocTotalFc", NullValueHandling = NullValueHandling.Ignore)]
        public double DocTotalFc { get; set; }

        [JsonProperty("DocTotalSys", NullValueHandling = NullValueHandling.Ignore)]
        public double DocTotalSys { get; set; }

        [JsonProperty("Form1099", NullValueHandling = NullValueHandling.Ignore)]
        public object Form1099 { get; set; }

        [JsonProperty("Box1099", NullValueHandling = NullValueHandling.Ignore)]
        public object Box1099 { get; set; }

        [JsonProperty("RevisionPo", NullValueHandling = NullValueHandling.Ignore)]
        public string RevisionPo { get; set; }

        [JsonProperty("RequriedDate", NullValueHandling = NullValueHandling.Ignore)]
        public object RequriedDate { get; set; }

        [JsonProperty("CancelDate", NullValueHandling = NullValueHandling.Ignore)]
        public object CancelDate { get; set; }

        [JsonProperty("BlockDunning", NullValueHandling = NullValueHandling.Ignore)]
        public string BlockDunning { get; set; }

        [JsonProperty("Submitted", NullValueHandling = NullValueHandling.Ignore)]
        public string Submitted { get; set; }

        [JsonProperty("Segment", NullValueHandling = NullValueHandling.Ignore)]
        public int Segment { get; set; }

        [JsonProperty("PickStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string PickStatus { get; set; }

        [JsonProperty("Pick", NullValueHandling = NullValueHandling.Ignore)]
        public string Pick { get; set; }

        [JsonProperty("PaymentMethod", NullValueHandling = NullValueHandling.Ignore)]
        public string PaymentMethod { get; set; }

        [JsonProperty("PaymentBlock", NullValueHandling = NullValueHandling.Ignore)]
        public string PaymentBlock { get; set; }

        [JsonProperty("PaymentBlockEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object PaymentBlockEntry { get; set; }

        [JsonProperty("CentralBankIndicator", NullValueHandling = NullValueHandling.Ignore)]
        public object CentralBankIndicator { get; set; }

        [JsonProperty("MaximumCashDiscount", NullValueHandling = NullValueHandling.Ignore)]
        public string MaximumCashDiscount { get; set; }

        [JsonProperty("Reserve", NullValueHandling = NullValueHandling.Ignore)]
        public string Reserve { get; set; }

        [JsonProperty("Project", NullValueHandling = NullValueHandling.Ignore)]
        public object Project { get; set; }

        [JsonProperty("ExemptionValidityDateFrom", NullValueHandling = NullValueHandling.Ignore)]
        public object ExemptionValidityDateFrom { get; set; }

        [JsonProperty("ExemptionValidityDateTo", NullValueHandling = NullValueHandling.Ignore)]
        public object ExemptionValidityDateTo { get; set; }

        [JsonProperty("WareHouseUpdateType", NullValueHandling = NullValueHandling.Ignore)]
        public string WareHouseUpdateType { get; set; }

        [JsonProperty("Rounding", NullValueHandling = NullValueHandling.Ignore)]
        public string Rounding { get; set; }

        [JsonProperty("ExternalCorrectedDocNum", NullValueHandling = NullValueHandling.Ignore)]
        public object ExternalCorrectedDocNum { get; set; }

        [JsonProperty("InternalCorrectedDocNum", NullValueHandling = NullValueHandling.Ignore)]
        public object InternalCorrectedDocNum { get; set; }

        [JsonProperty("NextCorrectingDocument", NullValueHandling = NullValueHandling.Ignore)]
        public object NextCorrectingDocument { get; set; }

        [JsonProperty("DeferredTax", NullValueHandling = NullValueHandling.Ignore)]
        public string DeferredTax { get; set; }

        [JsonProperty("TaxExemptionLetterNum", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxExemptionLetterNum { get; set; }

        [JsonProperty("WTApplied", NullValueHandling = NullValueHandling.Ignore)]
        public double WTApplied { get; set; }

        [JsonProperty("WTAppliedFC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTAppliedFC { get; set; }

        [JsonProperty("BillOfExchangeReserved", NullValueHandling = NullValueHandling.Ignore)]
        public string BillOfExchangeReserved { get; set; }

        [JsonProperty("AgentCode", NullValueHandling = NullValueHandling.Ignore)]
        public object AgentCode { get; set; }

        [JsonProperty("WTAppliedSC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTAppliedSC { get; set; }

        [JsonProperty("TotalEqualizationTax", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTax { get; set; }

        [JsonProperty("TotalEqualizationTaxFC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTaxFC { get; set; }

        [JsonProperty("TotalEqualizationTaxSC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalEqualizationTaxSC { get; set; }

        [JsonProperty("NumberOfInstallments", NullValueHandling = NullValueHandling.Ignore)]
        public int NumberOfInstallments { get; set; }

        [JsonProperty("ApplyTaxOnFirstInstallment", NullValueHandling = NullValueHandling.Ignore)]
        public string ApplyTaxOnFirstInstallment { get; set; }

        [JsonProperty("TaxOnInstallments", NullValueHandling = NullValueHandling.Ignore)]
        public string TaxOnInstallments { get; set; }

        [JsonProperty("WTNonSubjectAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double WTNonSubjectAmount { get; set; }

        [JsonProperty("WTNonSubjectAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTNonSubjectAmountSC { get; set; }

        [JsonProperty("WTNonSubjectAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTNonSubjectAmountFC { get; set; }

        [JsonProperty("WTExemptedAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double WTExemptedAmount { get; set; }

        [JsonProperty("WTExemptedAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTExemptedAmountSC { get; set; }

        [JsonProperty("WTExemptedAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTExemptedAmountFC { get; set; }

        [JsonProperty("BaseAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double BaseAmount { get; set; }

        [JsonProperty("BaseAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double BaseAmountSC { get; set; }

        [JsonProperty("BaseAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double BaseAmountFC { get; set; }

        [JsonProperty("WTAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double WTAmount { get; set; }

        [JsonProperty("WTAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTAmountSC { get; set; }

        [JsonProperty("WTAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double WTAmountFC { get; set; }

        [JsonProperty("VatDate", NullValueHandling = NullValueHandling.Ignore)]
        public object VatDate { get; set; }

        [JsonProperty("DocumentsOwner", NullValueHandling = NullValueHandling.Ignore)]
        public object DocumentsOwner { get; set; }

        [JsonProperty("FolioPrefixString", NullValueHandling = NullValueHandling.Ignore)]
        public string FolioPrefixString { get; set; }

        [JsonProperty("FolioNumber", NullValueHandling = NullValueHandling.Ignore)]
        public int FolioNumber { get; set; }

        [JsonProperty("DocumentSubType", NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentSubType { get; set; }

        [JsonProperty("BPChannelCode", NullValueHandling = NullValueHandling.Ignore)]
        public object BPChannelCode { get; set; }

        [JsonProperty("BPChannelContact", NullValueHandling = NullValueHandling.Ignore)]
        public int BPChannelContact { get; set; }

        [JsonProperty("Address2", NullValueHandling = NullValueHandling.Ignore)]
        public string Address2 { get; set; }

        [JsonProperty("DocumentStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentStatus { get; set; }

        [JsonProperty("PeriodIndicator", NullValueHandling = NullValueHandling.Ignore)]
        public string PeriodIndicator { get; set; }

        [JsonProperty("PayToCode", NullValueHandling = NullValueHandling.Ignore)]
        public object PayToCode { get; set; }

        [JsonProperty("ManualNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object ManualNumber { get; set; }

        [JsonProperty("UseShpdGoodsAct", NullValueHandling = NullValueHandling.Ignore)]
        public string UseShpdGoodsAct { get; set; }

        [JsonProperty("IsPayToBank", NullValueHandling = NullValueHandling.Ignore)]
        public string IsPayToBank { get; set; }

        [JsonProperty("PayToBankCountry", NullValueHandling = NullValueHandling.Ignore)]
        public object PayToBankCountry { get; set; }

        [JsonProperty("PayToBankCode", NullValueHandling = NullValueHandling.Ignore)]
        public object PayToBankCode { get; set; }

        [JsonProperty("PayToBankAccountNo", NullValueHandling = NullValueHandling.Ignore)]
        public object PayToBankAccountNo { get; set; }

        [JsonProperty("PayToBankBranch", NullValueHandling = NullValueHandling.Ignore)]
        public object PayToBankBranch { get; set; }

        [JsonProperty("BPL_IDAssignedToInvoice", NullValueHandling = NullValueHandling.Ignore)]
        public object BPL_IDAssignedToInvoice { get; set; }

        [JsonProperty("DownPayment", NullValueHandling = NullValueHandling.Ignore)]
        public double DownPayment { get; set; }

        [JsonProperty("ReserveInvoice", NullValueHandling = NullValueHandling.Ignore)]
        public string ReserveInvoice { get; set; }

        [JsonProperty("LanguageCode", NullValueHandling = NullValueHandling.Ignore)]
        public int LanguageCode { get; set; }

        [JsonProperty("TrackingNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object TrackingNumber { get; set; }

        [JsonProperty("PickRemark", NullValueHandling = NullValueHandling.Ignore)]
        public object PickRemark { get; set; }

        [JsonProperty("ClosingDate", NullValueHandling = NullValueHandling.Ignore)]
        public object ClosingDate { get; set; }

        [JsonProperty("SequenceCode", NullValueHandling = NullValueHandling.Ignore)]
        public object SequenceCode { get; set; }

        [JsonProperty("SequenceSerial", NullValueHandling = NullValueHandling.Ignore)]
        public object SequenceSerial { get; set; }

        [JsonProperty("SeriesString", NullValueHandling = NullValueHandling.Ignore)]
        public object SeriesString { get; set; }

        [JsonProperty("SubSeriesString", NullValueHandling = NullValueHandling.Ignore)]
        public object SubSeriesString { get; set; }

        [JsonProperty("SequenceModel", NullValueHandling = NullValueHandling.Ignore)]
        public string SequenceModel { get; set; }

        [JsonProperty("UseCorrectionVATGroup", NullValueHandling = NullValueHandling.Ignore)]
        public string UseCorrectionVATGroup { get; set; }

        [JsonProperty("TotalDiscount", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalDiscount { get; set; }

        [JsonProperty("DownPaymentAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double DownPaymentAmount { get; set; }

        [JsonProperty("DownPaymentPercentage", NullValueHandling = NullValueHandling.Ignore)]
        public double DownPaymentPercentage { get; set; }

        [JsonProperty("DownPaymentType", NullValueHandling = NullValueHandling.Ignore)]
        public string DownPaymentType { get; set; }

        [JsonProperty("DownPaymentAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double DownPaymentAmountSC { get; set; }

        [JsonProperty("DownPaymentAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double DownPaymentAmountFC { get; set; }

        [JsonProperty("VatPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double VatPercent { get; set; }

        [JsonProperty("ServiceGrossProfitPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double ServiceGrossProfitPercent { get; set; }

        [JsonProperty("OpeningRemarks", NullValueHandling = NullValueHandling.Ignore)]
        public object OpeningRemarks { get; set; }

        [JsonProperty("ClosingRemarks", NullValueHandling = NullValueHandling.Ignore)]
        public object ClosingRemarks { get; set; }

        [JsonProperty("RoundingDiffAmount", NullValueHandling = NullValueHandling.Ignore)]
        public double RoundingDiffAmount { get; set; }

        [JsonProperty("RoundingDiffAmountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double RoundingDiffAmountFC { get; set; }

        [JsonProperty("RoundingDiffAmountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double RoundingDiffAmountSC { get; set; }

        [JsonProperty("Cancelled", NullValueHandling = NullValueHandling.Ignore)]
        public string Cancelled { get; set; }

        [JsonProperty("SignatureInputMessage", NullValueHandling = NullValueHandling.Ignore)]
        public object SignatureInputMessage { get; set; }

        [JsonProperty("SignatureDigest", NullValueHandling = NullValueHandling.Ignore)]
        public object SignatureDigest { get; set; }

        [JsonProperty("CertificationNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object CertificationNumber { get; set; }

        [JsonProperty("PrivateKeyVersion", NullValueHandling = NullValueHandling.Ignore)]
        public object PrivateKeyVersion { get; set; }

        [JsonProperty("ControlAccount", NullValueHandling = NullValueHandling.Ignore)]
        public string ControlAccount { get; set; }

        [JsonProperty("InsuranceOperation347", NullValueHandling = NullValueHandling.Ignore)]
        public string InsuranceOperation347 { get; set; }

        [JsonProperty("ArchiveNonremovableSalesQuotation", NullValueHandling = NullValueHandling.Ignore)]
        public string ArchiveNonremovableSalesQuotation { get; set; }

        [JsonProperty("GTSChecker", NullValueHandling = NullValueHandling.Ignore)]
        public object GTSChecker { get; set; }

        [JsonProperty("GTSPayee", NullValueHandling = NullValueHandling.Ignore)]
        public object GTSPayee { get; set; }

        [JsonProperty("ExtraMonth", NullValueHandling = NullValueHandling.Ignore)]
        public int ExtraMonth { get; set; }

        [JsonProperty("ExtraDays", NullValueHandling = NullValueHandling.Ignore)]
        public int ExtraDays { get; set; }

        [JsonProperty("CashDiscountDateOffset", NullValueHandling = NullValueHandling.Ignore)]
        public int CashDiscountDateOffset { get; set; }

        [JsonProperty("StartFrom", NullValueHandling = NullValueHandling.Ignore)]
        public string StartFrom { get; set; }

        [JsonProperty("NTSApproved", NullValueHandling = NullValueHandling.Ignore)]
        public string NTSApproved { get; set; }

        [JsonProperty("ETaxWebSite", NullValueHandling = NullValueHandling.Ignore)]
        public object ETaxWebSite { get; set; }

        [JsonProperty("ETaxNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object ETaxNumber { get; set; }

        [JsonProperty("NTSApprovedNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object NTSApprovedNumber { get; set; }

        [JsonProperty("EDocGenerationType", NullValueHandling = NullValueHandling.Ignore)]
        public string EDocGenerationType { get; set; }

        [JsonProperty("EDocSeries", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocSeries { get; set; }

        [JsonProperty("EDocNum", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocNum { get; set; }

        [JsonProperty("EDocExportFormat", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocExportFormat { get; set; }

        [JsonProperty("EDocStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string EDocStatus { get; set; }

        [JsonProperty("EDocErrorCode", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocErrorCode { get; set; }

        [JsonProperty("EDocErrorMessage", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocErrorMessage { get; set; }

        [JsonProperty("DownPaymentStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string DownPaymentStatus { get; set; }

        [JsonProperty("GroupSeries", NullValueHandling = NullValueHandling.Ignore)]
        public object GroupSeries { get; set; }

        [JsonProperty("GroupNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object GroupNumber { get; set; }

        [JsonProperty("GroupHandWritten", NullValueHandling = NullValueHandling.Ignore)]
        public string GroupHandWritten { get; set; }

        [JsonProperty("ReopenOriginalDocument", NullValueHandling = NullValueHandling.Ignore)]
        public object ReopenOriginalDocument { get; set; }

        [JsonProperty("ReopenManuallyClosedOrCanceledDocument", NullValueHandling = NullValueHandling.Ignore)]
        public object ReopenManuallyClosedOrCanceledDocument { get; set; }

        [JsonProperty("CreateOnlineQuotation", NullValueHandling = NullValueHandling.Ignore)]
        public string CreateOnlineQuotation { get; set; }

        [JsonProperty("POSEquipmentNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object POSEquipmentNumber { get; set; }

        [JsonProperty("POSManufacturerSerialNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object POSManufacturerSerialNumber { get; set; }

        [JsonProperty("POSCashierNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object POSCashierNumber { get; set; }

        [JsonProperty("ApplyCurrentVATRatesForDownPaymentsToDraw", NullValueHandling = NullValueHandling.Ignore)]
        public string ApplyCurrentVATRatesForDownPaymentsToDraw { get; set; }

        [JsonProperty("ClosingOption", NullValueHandling = NullValueHandling.Ignore)]
        public string ClosingOption { get; set; }

        [JsonProperty("SpecifiedClosingDate", NullValueHandling = NullValueHandling.Ignore)]
        public object SpecifiedClosingDate { get; set; }

        [JsonProperty("OpenForLandedCosts", NullValueHandling = NullValueHandling.Ignore)]
        public string OpenForLandedCosts { get; set; }

        [JsonProperty("AuthorizationStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string AuthorizationStatus { get; set; }

        [JsonProperty("TotalDiscountFC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalDiscountFC { get; set; }

        [JsonProperty("TotalDiscountSC", NullValueHandling = NullValueHandling.Ignore)]
        public double TotalDiscountSC { get; set; }

        [JsonProperty("RelevantToGTS", NullValueHandling = NullValueHandling.Ignore)]
        public string RelevantToGTS { get; set; }

        [JsonProperty("BPLName", NullValueHandling = NullValueHandling.Ignore)]
        public object BPLName { get; set; }

        [JsonProperty("VATRegNum", NullValueHandling = NullValueHandling.Ignore)]
        public object VATRegNum { get; set; }

        [JsonProperty("AnnualInvoiceDeclarationReference", NullValueHandling = NullValueHandling.Ignore)]
        public object AnnualInvoiceDeclarationReference { get; set; }

        [JsonProperty("Supplier", NullValueHandling = NullValueHandling.Ignore)]
        public object Supplier { get; set; }

        [JsonProperty("Releaser", NullValueHandling = NullValueHandling.Ignore)]
        public object Releaser { get; set; }

        [JsonProperty("Receiver", NullValueHandling = NullValueHandling.Ignore)]
        public object Receiver { get; set; }

        [JsonProperty("BlanketAgreementNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object BlanketAgreementNumber { get; set; }

        [JsonProperty("IsAlteration", NullValueHandling = NullValueHandling.Ignore)]
        public string IsAlteration { get; set; }

        [JsonProperty("CancelStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string CancelStatus { get; set; }

        [JsonProperty("AssetValueDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime AssetValueDate { get; set; }

        [JsonProperty("InvoicePayment", NullValueHandling = NullValueHandling.Ignore)]
        public string InvoicePayment { get; set; }

        [JsonProperty("DocumentDelivery", NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentDelivery { get; set; }

        [JsonProperty("AuthorizationCode", NullValueHandling = NullValueHandling.Ignore)]
        public object AuthorizationCode { get; set; }

        [JsonProperty("StartDeliveryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object StartDeliveryDate { get; set; }

        [JsonProperty("StartDeliveryTime", NullValueHandling = NullValueHandling.Ignore)]
        public object StartDeliveryTime { get; set; }

        [JsonProperty("EndDeliveryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object EndDeliveryDate { get; set; }

        [JsonProperty("EndDeliveryTime", NullValueHandling = NullValueHandling.Ignore)]
        public object EndDeliveryTime { get; set; }

        [JsonProperty("VehiclePlate", NullValueHandling = NullValueHandling.Ignore)]
        public object VehiclePlate { get; set; }

        [JsonProperty("ATDocumentType", NullValueHandling = NullValueHandling.Ignore)]
        public object ATDocumentType { get; set; }

        [JsonProperty("ElecCommStatus", NullValueHandling = NullValueHandling.Ignore)]
        public object ElecCommStatus { get; set; }

        [JsonProperty("ElecCommMessage", NullValueHandling = NullValueHandling.Ignore)]
        public object ElecCommMessage { get; set; }

        [JsonProperty("ReuseDocumentNum", NullValueHandling = NullValueHandling.Ignore)]
        public string ReuseDocumentNum { get; set; }

        [JsonProperty("ReuseNotaFiscalNum", NullValueHandling = NullValueHandling.Ignore)]
        public string ReuseNotaFiscalNum { get; set; }

        [JsonProperty("PrintSEPADirect", NullValueHandling = NullValueHandling.Ignore)]
        public string PrintSEPADirect { get; set; }

        [JsonProperty("FiscalDocNum", NullValueHandling = NullValueHandling.Ignore)]
        public object FiscalDocNum { get; set; }

        [JsonProperty("POSDailySummaryNo", NullValueHandling = NullValueHandling.Ignore)]
        public object POSDailySummaryNo { get; set; }

        [JsonProperty("POSReceiptNo", NullValueHandling = NullValueHandling.Ignore)]
        public object POSReceiptNo { get; set; }

        [JsonProperty("PointOfIssueCode", NullValueHandling = NullValueHandling.Ignore)]
        public object PointOfIssueCode { get; set; }

        [JsonProperty("Letter", NullValueHandling = NullValueHandling.Ignore)]
        public object Letter { get; set; }

        [JsonProperty("FolioNumberFrom", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioNumberFrom { get; set; }

        [JsonProperty("FolioNumberTo", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioNumberTo { get; set; }

        [JsonProperty("InterimType", NullValueHandling = NullValueHandling.Ignore)]
        public string InterimType { get; set; }

        [JsonProperty("RelatedType", NullValueHandling = NullValueHandling.Ignore)]
        public int RelatedType { get; set; }

        [JsonProperty("RelatedEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object RelatedEntry { get; set; }

        [JsonProperty("SAPPassport", NullValueHandling = NullValueHandling.Ignore)]
        public object SAPPassport { get; set; }

        [JsonProperty("DocumentTaxID", NullValueHandling = NullValueHandling.Ignore)]
        public object DocumentTaxID { get; set; }

        [JsonProperty("DateOfReportingControlStatementVAT", NullValueHandling = NullValueHandling.Ignore)]
        public object DateOfReportingControlStatementVAT { get; set; }

        [JsonProperty("ReportingSectionControlStatementVAT", NullValueHandling = NullValueHandling.Ignore)]
        public object ReportingSectionControlStatementVAT { get; set; }

        [JsonProperty("ExcludeFromTaxReportControlStatementVAT", NullValueHandling = NullValueHandling.Ignore)]
        public string ExcludeFromTaxReportControlStatementVAT { get; set; }

        [JsonProperty("POS_CashRegister", NullValueHandling = NullValueHandling.Ignore)]
        public object POS_CashRegister { get; set; }

        [JsonProperty("UpdateTime", NullValueHandling = NullValueHandling.Ignore)]
        public string UpdateTime { get; set; }

        [JsonProperty("CreateQRCodeFrom", NullValueHandling = NullValueHandling.Ignore)]
        public object CreateQRCodeFrom { get; set; }

        [JsonProperty("PriceMode", NullValueHandling = NullValueHandling.Ignore)]
        public object PriceMode { get; set; }

        [JsonProperty("OriginalRefNo", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalRefNo { get; set; }

        [JsonProperty("OriginalRefDate", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalRefDate { get; set; }

        [JsonProperty("Revision", NullValueHandling = NullValueHandling.Ignore)]
        public string Revision { get; set; }

        [JsonProperty("GSTTransactionType", NullValueHandling = NullValueHandling.Ignore)]
        public object GSTTransactionType { get; set; }

        [JsonProperty("OriginalCreditOrDebitNo", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalCreditOrDebitNo { get; set; }

        [JsonProperty("OriginalCreditOrDebitDate", NullValueHandling = NullValueHandling.Ignore)]
        public object OriginalCreditOrDebitDate { get; set; }

        [JsonProperty("TaxInvoiceNo", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxInvoiceNo { get; set; }

        [JsonProperty("TaxInvoiceDate", NullValueHandling = NullValueHandling.Ignore)]
        public object TaxInvoiceDate { get; set; }

        [JsonProperty("ShipFrom", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipFrom { get; set; }

        [JsonProperty("CommissionTrade", NullValueHandling = NullValueHandling.Ignore)]
        public string CommissionTrade { get; set; }

        [JsonProperty("CommissionTradeReturn", NullValueHandling = NullValueHandling.Ignore)]
        public string CommissionTradeReturn { get; set; }

        [JsonProperty("UseBillToAddrToDetermineTax", NullValueHandling = NullValueHandling.Ignore)]
        public string UseBillToAddrToDetermineTax { get; set; }

        [JsonProperty("IssuingReason", NullValueHandling = NullValueHandling.Ignore)]
        public int IssuingReason { get; set; }

        [JsonProperty("SOIWizardId", NullValueHandling = NullValueHandling.Ignore)]
        public object SOIWizardId { get; set; }

        [JsonProperty("Cig", NullValueHandling = NullValueHandling.Ignore)]
        public object Cig { get; set; }

        [JsonProperty("Cup", NullValueHandling = NullValueHandling.Ignore)]
        public object Cup { get; set; }

        [JsonProperty("PaidToDate", NullValueHandling = NullValueHandling.Ignore)]
        public double PaidToDate { get; set; }

        [JsonProperty("PaidToDateFC", NullValueHandling = NullValueHandling.Ignore)]
        public double PaidToDateFC { get; set; }

        [JsonProperty("PaidToDateSys", NullValueHandling = NullValueHandling.Ignore)]
        public double PaidToDateSys { get; set; }

        [JsonProperty("BaseType", NullValueHandling = NullValueHandling.Ignore)]
        public int BaseType { get; set; }

        [JsonProperty("BaseEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object BaseEntry { get; set; }

        [JsonProperty("FatherCard", NullValueHandling = NullValueHandling.Ignore)]
        public object FatherCard { get; set; }

        [JsonProperty("FatherType", NullValueHandling = NullValueHandling.Ignore)]
        public string FatherType { get; set; }

        [JsonProperty("ShipState", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipState { get; set; }

        [JsonProperty("ShipPlace", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipPlace { get; set; }

        [JsonProperty("CustOffice", NullValueHandling = NullValueHandling.Ignore)]
        public object CustOffice { get; set; }

        [JsonProperty("FCI", NullValueHandling = NullValueHandling.Ignore)]
        public object FCI { get; set; }

        [JsonProperty("AddLegIn", NullValueHandling = NullValueHandling.Ignore)]
        public object AddLegIn { get; set; }

        [JsonProperty("LegTextF", NullValueHandling = NullValueHandling.Ignore)]
        public object LegTextF { get; set; }

        [JsonProperty("DANFELgTxt", NullValueHandling = NullValueHandling.Ignore)]
        public object DANFELgTxt { get; set; }

        [JsonProperty("DataVersion", NullValueHandling = NullValueHandling.Ignore)]
        public int DataVersion { get; set; }

        [JsonProperty("LastPageFolioNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object LastPageFolioNumber { get; set; }

        [JsonProperty("InventoryStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string InventoryStatus { get; set; }

        [JsonProperty("PlasticPackagingTaxRelevant", NullValueHandling = NullValueHandling.Ignore)]
        public string PlasticPackagingTaxRelevant { get; set; }

        [JsonProperty("NotRelevantForMonthlyInvoice", NullValueHandling = NullValueHandling.Ignore)]
        public string NotRelevantForMonthlyInvoice { get; set; }

        [JsonProperty("AllocationNumberIL", NullValueHandling = NullValueHandling.Ignore)]
        public object AllocationNumberIL { get; set; }

        [JsonProperty("U_codgene", NullValueHandling = NullValueHandling.Ignore)]
        public object U_codgene { get; set; }

        [JsonProperty("U_tipoventa", NullValueHandling = NullValueHandling.Ignore)]
        public object U_tipoventa { get; set; }

        [JsonProperty("U_clientedespacho", NullValueHandling = NullValueHandling.Ignore)]
        public object U_clientedespacho { get; set; }

        [JsonProperty("U_numguia", NullValueHandling = NullValueHandling.Ignore)]
        public object U_numguia { get; set; }

        [JsonProperty("U_kmrecorrido", NullValueHandling = NullValueHandling.Ignore)]
        public double U_kmrecorrido { get; set; }

        [JsonProperty("U_contratistaco", NullValueHandling = NullValueHandling.Ignore)]
        public object U_contratistaco { get; set; }

        [JsonProperty("U_autdespacho", NullValueHandling = NullValueHandling.Ignore)]
        public string U_autdespacho { get; set; }

        [JsonProperty("U_fechrecepcli", NullValueHandling = NullValueHandling.Ignore)]
        public object U_fechrecepcli { get; set; }

        [JsonProperty("U_fechsalidacam", NullValueHandling = NullValueHandling.Ignore)]
        public object U_fechsalidacam { get; set; }

        [JsonProperty("U_tipopedido", NullValueHandling = NullValueHandling.Ignore)]
        public object U_tipopedido { get; set; }

        [JsonProperty("U_subtipopedido", NullValueHandling = NullValueHandling.Ignore)]
        public string U_subtipopedido { get; set; }

        [JsonProperty("U_tipopago", NullValueHandling = NullValueHandling.Ignore)]
        public object U_tipopago { get; set; }

        [JsonProperty("U_disponiblecosecha", NullValueHandling = NullValueHandling.Ignore)]
        public object U_disponiblecosecha { get; set; }

        [JsonProperty("U_fechaflujo", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime U_fechaflujo { get; set; }

        [JsonProperty("U_factura", NullValueHandling = NullValueHandling.Ignore)]
        public object U_factura { get; set; }

        [JsonProperty("U_solicitante", NullValueHandling = NullValueHandling.Ignore)]
        public object U_solicitante { get; set; }

        [JsonProperty("U_zona", NullValueHandling = NullValueHandling.Ignore)]
        public string U_zona { get; set; }

        [JsonProperty("U_subcliente", NullValueHandling = NullValueHandling.Ignore)]
        public object U_subcliente { get; set; }

        [JsonProperty("U_unidadconversion", NullValueHandling = NullValueHandling.Ignore)]
        public string U_unidadconversion { get; set; }

        [JsonProperty("U_codexportacion", NullValueHandling = NullValueHandling.Ignore)]
        public object U_codexportacion { get; set; }

        [JsonProperty("U_EXX_FE_TpoDTE", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_TpoDTE { get; set; }

        [JsonProperty("U_EXX_FE_IndNoRebaja", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_IndNoRebaja { get; set; }

        [JsonProperty("U_EXX_FE_TpoDespacho", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_TpoDespacho { get; set; }

        [JsonProperty("U_EXX_FE_IndTraslado", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_IndTraslado { get; set; }

        [JsonProperty("U_EXX_FE_TpoImprsion", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_TpoImprsion { get; set; }

        [JsonProperty("U_EXX_FE_IndServicio", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_IndServicio { get; set; }

        [JsonProperty("U_EXX_FE_MedioPago", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_MedioPago { get; set; }

        [JsonProperty("U_EXX_FE_TpoCtaPago", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_TpoCtaPago { get; set; }

        [JsonProperty("U_EXX_FE_FmaPagExp", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_FmaPagExp { get; set; }

        [JsonProperty("U_EXX_FE_Estado", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_Estado { get; set; }

        [JsonProperty("U_EXX_FE_FechaEvento", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_FechaEvento { get; set; }

        [JsonProperty("U_EXX_FE_CODERR", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_CODERR { get; set; }

        [JsonProperty("U_EXX_FE_DESERR", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_DESERR { get; set; }

        [JsonProperty("U_EXX_FE_PdfCreado", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_PdfCreado { get; set; }

        [JsonProperty("U_EXX_FE_PdfError", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_PdfError { get; set; }

        [JsonProperty("U_EXX_FE_MailEnviado", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_MailEnviado { get; set; }

        [JsonProperty("U_EXX_FE_MailError", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_MailError { get; set; }

        [JsonProperty("U_EXX_FE_TpoTxVenta", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_TpoTxVenta { get; set; }

        [JsonProperty("U_EXX_FE_TpoTxCompra", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_TpoTxCompra { get; set; }

        [JsonProperty("U_EXX_FE_TrackID", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_TrackID { get; set; }

        [JsonProperty("U_EXX_FE_TrackIDCes", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_TrackIDCes { get; set; }

        [JsonProperty("U_EXX_FE_EstadoCes", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_EstadoCes { get; set; }

        [JsonProperty("U_EXX_FE_FchRecepSII", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_FchRecepSII { get; set; }

        [JsonProperty("U_EXX_FE_AutoAcept", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_AutoAcept { get; set; }

        [JsonProperty("U_EXX_FE_Print", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_Print { get; set; }

        [JsonProperty("U_EXX_FE_EnvioMasivo", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_EnvioMasivo { get; set; }

        [JsonProperty("U_Bol_Ini", NullValueHandling = NullValueHandling.Ignore)]
        public int U_Bol_Ini { get; set; }

        [JsonProperty("U_Bol_Fin", NullValueHandling = NullValueHandling.Ignore)]
        public int U_Bol_Fin { get; set; }

        [JsonProperty("U_NUMFACT", NullValueHandling = NullValueHandling.Ignore)]
        public object U_NUMFACT { get; set; }

        [JsonProperty("U_actividad", NullValueHandling = NullValueHandling.Ignore)]
        public string U_actividad { get; set; }

        [JsonProperty("U_grupoFC", NullValueHandling = NullValueHandling.Ignore)]
        public string U_grupoFC { get; set; }

        [JsonProperty("U_itemflujo", NullValueHandling = NullValueHandling.Ignore)]
        public string U_itemflujo { get; set; }

        [JsonProperty("U_FEX_PATENTECARRO", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_PATENTECARRO { get; set; }

        [JsonProperty("U_FEX_PATENTECAMION", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_PATENTECAMION { get; set; }

        [JsonProperty("U_FEX_CHOFER", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_CHOFER { get; set; }

        [JsonProperty("U_FEX_RUT", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_RUT { get; set; }

        [JsonProperty("U_FEX_INF", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_INF { get; set; }

        [JsonProperty("U_FEX_ORIGEN", NullValueHandling = NullValueHandling.Ignore)]
        public object U_FEX_ORIGEN { get; set; }

        [JsonProperty("U_equipocarguio", NullValueHandling = NullValueHandling.Ignore)]
        public object U_equipocarguio { get; set; }

        [JsonProperty("U_nomopegrua", NullValueHandling = NullValueHandling.Ignore)]
        public object U_nomopegrua { get; set; }

        [JsonProperty("U_rutopegrua", NullValueHandling = NullValueHandling.Ignore)]
        public object U_rutopegrua { get; set; }

        [JsonProperty("U_tipogestion", NullValueHandling = NullValueHandling.Ignore)]
        public object U_tipogestion { get; set; }

        [JsonProperty("U_despachador", NullValueHandling = NullValueHandling.Ignore)]
        public object U_despachador { get; set; }

        [JsonProperty("U_Destino", NullValueHandling = NullValueHandling.Ignore)]
        public object U_Destino { get; set; }

        [JsonProperty("U_tipotransporte", NullValueHandling = NullValueHandling.Ignore)]
        public object U_tipotransporte { get; set; }

        [JsonProperty("U_transportista", NullValueHandling = NullValueHandling.Ignore)]
        public object U_transportista { get; set; }

        [JsonProperty("U_prodtransporte", NullValueHandling = NullValueHandling.Ignore)]
        public object U_prodtransporte { get; set; }

        [JsonProperty("U_Turno", NullValueHandling = NullValueHandling.Ignore)]
        public object U_Turno { get; set; }

        [JsonProperty("U_GuiaAnuladaxFE", NullValueHandling = NullValueHandling.Ignore)]
        public string U_GuiaAnuladaxFE { get; set; }

        [JsonProperty("U_PrioridadDespacho", NullValueHandling = NullValueHandling.Ignore)]
        public string U_PrioridadDespacho { get; set; }

        [JsonProperty("U_PrioridadEntrega", NullValueHandling = NullValueHandling.Ignore)]
        public string U_PrioridadEntrega { get; set; }

        [JsonProperty("U_CertificaMadera", NullValueHandling = NullValueHandling.Ignore)]
        public string U_CertificaMadera { get; set; }

        [JsonProperty("U_EXX_FE_IDRECEP", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_IDRECEP { get; set; }

        [JsonProperty("U_EXX_FE_RutCes", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_RutCes { get; set; }

        [JsonProperty("U_EXX_FE_FechaCes", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_FechaCes { get; set; }

        [JsonProperty("U_EXX_FE_TPOGUIA", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_TPOGUIA { get; set; }

        [JsonProperty("U_EXX_FE_AutoAccion", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_AutoAccion { get; set; }

        [JsonProperty("U_bitem", NullValueHandling = NullValueHandling.Ignore)]
        public object U_bitem { get; set; }

        [JsonProperty("U_EXX_FE_CONT_RECEP", NullValueHandling = NullValueHandling.Ignore)]
        public string U_EXX_FE_CONT_RECEP { get; set; }

        [JsonProperty("U_EXX_FE_CODE_RECEP", NullValueHandling = NullValueHandling.Ignore)]
        public object U_EXX_FE_CODE_RECEP { get; set; }

        [JsonProperty("Document_ApprovalRequests", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> Document_ApprovalRequests { get; set; }

        [JsonProperty("DocumentLines", NullValueHandling = NullValueHandling.Ignore)]
        public List<DocumentLine> DocumentLines { get; set; }

        [JsonProperty("EWayBillDetails", NullValueHandling = NullValueHandling.Ignore)]
        public EWayBillDetails EWayBillDetails { get; set; }

        [JsonProperty("ElectronicProtocols", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> ElectronicProtocols { get; set; }

        [JsonProperty("DocumentAdditionalExpenses", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentAdditionalExpenses { get; set; }

        [JsonProperty("WithholdingTaxDataWTXCollection", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> WithholdingTaxDataWTXCollection { get; set; }

        [JsonProperty("WithholdingTaxDataCollection", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> WithholdingTaxDataCollection { get; set; }

        [JsonProperty("DocumentSpecialLines", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentSpecialLines { get; set; }

        [JsonProperty("DocumentInstallments", NullValueHandling = NullValueHandling.Ignore)]
        public List<DocumentInstallment> DocumentInstallments { get; set; }

        [JsonProperty("DownPaymentsToDraw", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DownPaymentsToDraw { get; set; }

        [JsonProperty("TaxExtension", NullValueHandling = NullValueHandling.Ignore)]
        public TaxExtension TaxExtension { get; set; }

        [JsonProperty("AddressExtension", NullValueHandling = NullValueHandling.Ignore)]
        public AddressExtension AddressExtension { get; set; }

        [JsonProperty("DocumentReferences", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentReferences { get; set; }

        [JsonProperty("U_RUT_CESIONARIO", NullValueHandling = NullValueHandling.Ignore)]
        public string? U_RUT_CESIONARIO { get; set; }

        [JsonProperty("U_RAZON_CESIONARIO", NullValueHandling = NullValueHandling.Ignore)]
        public string? U_RAZON_CESIONARIO { get; set; }

        [JsonProperty("U_MAIL_CESIONARIO", NullValueHandling = NullValueHandling.Ignore)]
        public string? U_MAIL_CESIONARIO { get; set; }

        [JsonProperty("U_FEECHA_EMISION_DTE", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? U_FEECHA_EMISION_DTE { get; set; }

        [JsonProperty("U_FECHA_CESION_FACO", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? U_FECHA_CESION_FACO { get; set; }

        [JsonProperty("U_FECHA_VENC_FACO", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? U_FECHA_VENC_FACO { get; set; }

        [JsonProperty("U_TIPO_SERV_FACO", NullValueHandling = NullValueHandling.Ignore)]
        public string? U_TIPO_SERV_FACO { get; set; }
    }
}
