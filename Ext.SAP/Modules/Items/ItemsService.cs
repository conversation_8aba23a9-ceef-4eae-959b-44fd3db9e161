using System.Net;
using Leonera_API_ExternalServices.Modules.Core;
using Serilog;

namespace Leonera_API_ExternalServices.Modules.Items;

/// <summary>
///     Servicio para gestionar artículos en SAP Business One.
/// </summary>
/// <remarks>
///     Inicializa una nueva instancia de la clase <see cref="ItemsService" />.
/// </remarks>
public class ItemsService(CoreService coreService)
{
    private const string BaseUri = "Items";

    /// <summary>
    /// Obtiene todos los artículos disponibles en SAP Business One.
    /// Realiza una llamada GET al endpoint base de Items sin filtros.
    /// </summary>
    /// <param name="auth">Token de autenticación para el servicio</param>
    /// <returns>Tupla con el código de estado HTTP y el contenido de la respuesta</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> GetItems(string? auth)
    {
        return await coreService.ExecuteGetApiCall(BaseUri, auth);
    }

    /// <summary>
    /// Busca un artículo específico por su código identificador (ItemCode).
    /// Utiliza un filtro exacto para encontrar la coincidencia precisa.
    /// </summary>
    /// <param name="id">Código identificador del artículo</param>
    /// <param name="auth">Token de autenticación para el servicio</param>
    /// <returns>Tupla con el código de estado HTTP y el contenido de la respuesta</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> GetItemById(string id, string? auth)
    {
        string uri = $"{BaseUri}?filter=ItemCode eq '{id}'";
        Log.Information("Executing GET call to: {Uri}", uri);

        return await coreService.ExecuteGetApiCall(uri, auth);
    }

    /// <summary>
    /// Busca artículos que comiencen con un código específico.
    /// Retorna una lista filtrada con información básica del artículo y su información de almacén.
    /// Los resultados se ordenan por código de artículo.
    /// </summary>
    /// <param name="code">Código parcial para buscar artículos</param>
    /// <param name="auth">Token de autenticación para el servicio</param>
    /// <returns>Tupla con el código de estado HTTP y el contenido de la respuesta</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> SearchItemByCode(string code, string? auth)
    {
        string uri =
            $"{BaseUri}?$select=ItemCode,ItemName,ForeignName,ItemWarehouseInfoCollection&"
            + $"$filter=startswith(ItemCode, '{code}')&$orderby=ItemCode";
        Log.Information("Executing GET call to: {Uri}", uri);

        return await coreService.ExecuteGetApiCall(uri, auth);
    }

    /// <summary>
    /// Obtiene todos los artículos pertenecientes a un grupo específico.
    /// Retorna información detallada incluyendo campos personalizados (U_) como factor m3,
    /// medidas y nombres comerciales.
    /// </summary>
    /// <param name="groupCode">Código del grupo de artículos</param>
    /// <param name="auth">Token de autenticación para el servicio</param>
    /// <returns>Tupla con el código de estado HTTP y el contenido de la respuesta</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> GetAllItemsByGroup(string groupCode, string? auth)
    {
        string uri =
            $"{BaseUri}?$select=ItemCode,ItemName,ForeignName,ItemsGroupCode,U_factorm3,U_medidas,U_nombrecomercial,U_nombrecomercial2&"
            + $"$filter=ItemsGroupCode eq {groupCode}";
        return await coreService.ExecuteGetApiCall(uri, auth);
    }
}
