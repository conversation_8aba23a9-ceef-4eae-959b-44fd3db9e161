using Newtonsoft.Json;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Leonera_API_ExternalServices.Modules.StockTransferDrafts.Dtos;

public class GetResponse
{
    public class Root
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<Value> Value { get; set; }

        [JsonProperty("@odata.nextLink", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataNextLink { get; set; }
    }

    public class StockTransferLine
    {
        [JsonProperty("LineNum", NullValueHandling = NullValueHandling.Ignore)]
        public int? LineNum { get; set; }

        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int? DocEntry { get; set; }

        [JsonProperty("ItemCode", NullValueHandling = NullValueHandling.Ignore)]
        public string ItemCode { get; set; }

        [JsonProperty("ItemDescription", NullValueHandling = NullValueHandling.Ignore)]
        public string ItemDescription { get; set; }

        [JsonProperty("Quantity", NullValueHandling = NullValueHandling.Ignore)]
        public double? Quantity { get; set; }

        [JsonProperty("Price", NullValueHandling = NullValueHandling.Ignore)]
        public double? Price { get; set; }

        [JsonProperty("Currency", NullValueHandling = NullValueHandling.Ignore)]
        public string Currency { get; set; }

        [JsonProperty("Rate", NullValueHandling = NullValueHandling.Ignore)]
        public double? Rate { get; set; }

        [JsonProperty("DiscountPercent", NullValueHandling = NullValueHandling.Ignore)]
        public double? DiscountPercent { get; set; }

        [JsonProperty("VendorNum", NullValueHandling = NullValueHandling.Ignore)]
        public string VendorNum { get; set; }

        [JsonProperty("SerialNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object SerialNumber { get; set; }

        [JsonProperty("WarehouseCode", NullValueHandling = NullValueHandling.Ignore)]
        public string WarehouseCode { get; set; }

        [JsonProperty("FromWarehouseCode", NullValueHandling = NullValueHandling.Ignore)]
        public object FromWarehouseCode { get; set; }

        [JsonProperty("ProjectCode", NullValueHandling = NullValueHandling.Ignore)]
        public string ProjectCode { get; set; }

        [JsonProperty("Factor", NullValueHandling = NullValueHandling.Ignore)]
        public double? Factor { get; set; }

        [JsonProperty("Factor2", NullValueHandling = NullValueHandling.Ignore)]
        public double? Factor2 { get; set; }

        [JsonProperty("Factor3", NullValueHandling = NullValueHandling.Ignore)]
        public double? Factor3 { get; set; }

        [JsonProperty("Factor4", NullValueHandling = NullValueHandling.Ignore)]
        public double? Factor4 { get; set; }

        [JsonProperty("DistributionRule", NullValueHandling = NullValueHandling.Ignore)]
        public string DistributionRule { get; set; }

        [JsonProperty("DistributionRule2", NullValueHandling = NullValueHandling.Ignore)]
        public string DistributionRule2 { get; set; }

        [JsonProperty("DistributionRule3", NullValueHandling = NullValueHandling.Ignore)]
        public string DistributionRule3 { get; set; }

        [JsonProperty("DistributionRule4", NullValueHandling = NullValueHandling.Ignore)]
        public string DistributionRule4 { get; set; }

        [JsonProperty("DistributionRule5", NullValueHandling = NullValueHandling.Ignore)]
        public object DistributionRule5 { get; set; }

        [JsonProperty("UseBaseUnits", NullValueHandling = NullValueHandling.Ignore)]
        public string UseBaseUnits { get; set; }

        [JsonProperty("MeasureUnit", NullValueHandling = NullValueHandling.Ignore)]
        public string MeasureUnit { get; set; }

        [JsonProperty("UnitsOfMeasurment", NullValueHandling = NullValueHandling.Ignore)]
        public double? UnitsOfMeasurment { get; set; }

        [JsonProperty("BaseType", NullValueHandling = NullValueHandling.Ignore)]
        public string BaseType { get; set; }

        [JsonProperty("BaseLine", NullValueHandling = NullValueHandling.Ignore)]
        public object BaseLine { get; set; }

        [JsonProperty("BaseEntry", NullValueHandling = NullValueHandling.Ignore)]
        public object BaseEntry { get; set; }

        [JsonProperty("UnitPrice", NullValueHandling = NullValueHandling.Ignore)]
        public double? UnitPrice { get; set; }

        [JsonProperty("UoMEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int? UoMEntry { get; set; }

        [JsonProperty("UoMCode", NullValueHandling = NullValueHandling.Ignore)]
        public string UoMCode { get; set; }

        [JsonProperty("InventoryQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double? InventoryQuantity { get; set; }

        [JsonProperty("RemainingOpenQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double? RemainingOpenQuantity { get; set; }

        [JsonProperty("RemainingOpenInventoryQuantity", NullValueHandling = NullValueHandling.Ignore)]
        public double? RemainingOpenInventoryQuantity { get; set; }

        [JsonProperty("LineStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string LineStatus { get; set; }

        [JsonProperty("WeightOfRecycledPlastic", NullValueHandling = NullValueHandling.Ignore)]
        public object WeightOfRecycledPlastic { get; set; }

        [JsonProperty("PlasticPackageExemptionReason", NullValueHandling = NullValueHandling.Ignore)]
        public object PlasticPackageExemptionReason { get; set; }

        [JsonProperty("U_cantidadm3", NullValueHandling = NullValueHandling.Ignore)]
        public object UCantidadm3 { get; set; }

        [JsonProperty("U_cantidadpulgada", NullValueHandling = NullValueHandling.Ignore)]
        public object UCantidadpulgada { get; set; }

        [JsonProperty("U_FEX_NOMCOMERCIAL", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXNOMCOMERCIAL { get; set; }

        [JsonProperty("U_FEX_NOMCOMERCIAL2", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXNOMCOMERCIAL2 { get; set; }

        [JsonProperty("U_FEX_COM", NullValueHandling = NullValueHandling.Ignore)]
        public string UFEXCOM { get; set; }

        [JsonProperty("U_volumenm3", NullValueHandling = NullValueHandling.Ignore)]
        public object UVolumenm3 { get; set; }

        [JsonProperty("U_EXX_FE_Descripcion", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEDescripcion { get; set; }

        [JsonProperty("SerialNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> SerialNumbers { get; set; }

        [JsonProperty("BatchNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> BatchNumbers { get; set; }

        [JsonProperty("CCDNumbers", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> CCDNumbers { get; set; }

        [JsonProperty("StockTransferLinesBinAllocations", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> StockTransferLinesBinAllocations { get; set; }
    }

    public class StockTransferTaxExtension
    {
        [JsonProperty("SupportVAT", NullValueHandling = NullValueHandling.Ignore)]
        public string SupportVAT { get; set; }

        [JsonProperty("FormNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object FormNumber { get; set; }

        [JsonProperty("TransactionCategory", NullValueHandling = NullValueHandling.Ignore)]
        public object TransactionCategory { get; set; }

        [JsonProperty("U_zonaS", NullValueHandling = NullValueHandling.Ignore)]
        public object UZonaS { get; set; }

        [JsonProperty("U_zonaB", NullValueHandling = NullValueHandling.Ignore)]
        public object UZonaB { get; set; }
    }

    public class Value
    {
        [JsonProperty("DocEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int? DocEntry { get; set; }

        [JsonProperty("Series", NullValueHandling = NullValueHandling.Ignore)]
        public int? Series { get; set; }

        [JsonProperty("Printed", NullValueHandling = NullValueHandling.Ignore)]
        public string Printed { get; set; }

        [JsonProperty("DocDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? DocDate { get; set; }

        [JsonProperty("DueDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? DueDate { get; set; }

        [JsonProperty("CardCode", NullValueHandling = NullValueHandling.Ignore)]
        public string CardCode { get; set; }

        [JsonProperty("CardName", NullValueHandling = NullValueHandling.Ignore)]
        public string CardName { get; set; }

        [JsonProperty("Address", NullValueHandling = NullValueHandling.Ignore)]
        public object Address { get; set; }

        [JsonProperty("Reference1", NullValueHandling = NullValueHandling.Ignore)]
        public object Reference1 { get; set; }

        [JsonProperty("Reference2", NullValueHandling = NullValueHandling.Ignore)]
        public object Reference2 { get; set; }

        [JsonProperty("Comments", NullValueHandling = NullValueHandling.Ignore)]
        public string Comments { get; set; }

        [JsonProperty("JournalMemo", NullValueHandling = NullValueHandling.Ignore)]
        public string JournalMemo { get; set; }

        [JsonProperty("PriceList", NullValueHandling = NullValueHandling.Ignore)]
        public int? PriceList { get; set; }

        [JsonProperty("SalesPersonCode", NullValueHandling = NullValueHandling.Ignore)]
        public int? SalesPersonCode { get; set; }

        [JsonProperty("FromWarehouse", NullValueHandling = NullValueHandling.Ignore)]
        public object FromWarehouse { get; set; }

        [JsonProperty("ToWarehouse", NullValueHandling = NullValueHandling.Ignore)]
        public object ToWarehouse { get; set; }

        [JsonProperty("CreationDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? CreationDate { get; set; }

        [JsonProperty("UpdateDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? UpdateDate { get; set; }

        [JsonProperty("FinancialPeriod", NullValueHandling = NullValueHandling.Ignore)]
        public int? FinancialPeriod { get; set; }

        [JsonProperty("TransNum", NullValueHandling = NullValueHandling.Ignore)]
        public object TransNum { get; set; }

        [JsonProperty("DocNum", NullValueHandling = NullValueHandling.Ignore)]
        public int? DocNum { get; set; }

        [JsonProperty("TaxDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? TaxDate { get; set; }

        [JsonProperty("ContactPerson", NullValueHandling = NullValueHandling.Ignore)]
        public int? ContactPerson { get; set; }

        [JsonProperty("FolioPrefixString", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioPrefixString { get; set; }

        [JsonProperty("FolioNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioNumber { get; set; }

        [JsonProperty("DocObjectCode", NullValueHandling = NullValueHandling.Ignore)]
        public string DocObjectCode { get; set; }

        [JsonProperty("AuthorizationStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string AuthorizationStatus { get; set; }

        [JsonProperty("BPLID", NullValueHandling = NullValueHandling.Ignore)]
        public object BPLID { get; set; }

        [JsonProperty("BPLName", NullValueHandling = NullValueHandling.Ignore)]
        public object BPLName { get; set; }

        [JsonProperty("VATRegNum", NullValueHandling = NullValueHandling.Ignore)]
        public object VATRegNum { get; set; }

        [JsonProperty("AuthorizationCode", NullValueHandling = NullValueHandling.Ignore)]
        public object AuthorizationCode { get; set; }

        [JsonProperty("StartDeliveryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object StartDeliveryDate { get; set; }

        [JsonProperty("StartDeliveryTime", NullValueHandling = NullValueHandling.Ignore)]
        public object StartDeliveryTime { get; set; }

        [JsonProperty("EndDeliveryDate", NullValueHandling = NullValueHandling.Ignore)]
        public object EndDeliveryDate { get; set; }

        [JsonProperty("EndDeliveryTime", NullValueHandling = NullValueHandling.Ignore)]
        public object EndDeliveryTime { get; set; }

        [JsonProperty("VehiclePlate", NullValueHandling = NullValueHandling.Ignore)]
        public object VehiclePlate { get; set; }

        [JsonProperty("ATDocumentType", NullValueHandling = NullValueHandling.Ignore)]
        public object ATDocumentType { get; set; }

        [JsonProperty("EDocExportFormat", NullValueHandling = NullValueHandling.Ignore)]
        public object EDocExportFormat { get; set; }

        [JsonProperty("ElecCommStatus", NullValueHandling = NullValueHandling.Ignore)]
        public object ElecCommStatus { get; set; }

        [JsonProperty("ElecCommMessage", NullValueHandling = NullValueHandling.Ignore)]
        public object ElecCommMessage { get; set; }

        [JsonProperty("PointOfIssueCode", NullValueHandling = NullValueHandling.Ignore)]
        public object PointOfIssueCode { get; set; }

        [JsonProperty("Letter", NullValueHandling = NullValueHandling.Ignore)]
        public object Letter { get; set; }

        [JsonProperty("FolioNumberFrom", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioNumberFrom { get; set; }

        [JsonProperty("FolioNumberTo", NullValueHandling = NullValueHandling.Ignore)]
        public object FolioNumberTo { get; set; }

        [JsonProperty("AttachmentEntry", NullValueHandling = NullValueHandling.Ignore)]
        public int? AttachmentEntry { get; set; }

        [JsonProperty("DocumentStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentStatus { get; set; }

        [JsonProperty("ShipToCode", NullValueHandling = NullValueHandling.Ignore)]
        public object ShipToCode { get; set; }

        [JsonProperty("SAPPassport", NullValueHandling = NullValueHandling.Ignore)]
        public object SAPPassport { get; set; }

        [JsonProperty("LastPageFolioNumber", NullValueHandling = NullValueHandling.Ignore)]
        public object LastPageFolioNumber { get; set; }

        [JsonProperty("DutyStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string DutyStatus { get; set; }

        [JsonProperty("U_codgene", NullValueHandling = NullValueHandling.Ignore)]
        public object UCodgene { get; set; }

        [JsonProperty("U_tipoventa", NullValueHandling = NullValueHandling.Ignore)]
        public object UTipoventa { get; set; }

        [JsonProperty("U_clientedespacho", NullValueHandling = NullValueHandling.Ignore)]
        public object UClientedespacho { get; set; }

        [JsonProperty("U_numguia", NullValueHandling = NullValueHandling.Ignore)]
        public object UNumguia { get; set; }

        [JsonProperty("U_kmrecorrido", NullValueHandling = NullValueHandling.Ignore)]
        public double? UKmrecorrido { get; set; }

        [JsonProperty("U_contratistaco", NullValueHandling = NullValueHandling.Ignore)]
        public object UContratistaco { get; set; }

        [JsonProperty("U_autdespacho", NullValueHandling = NullValueHandling.Ignore)]
        public string UAutdespacho { get; set; }

        [JsonProperty("U_fechrecepcli", NullValueHandling = NullValueHandling.Ignore)]
        public object UFechrecepcli { get; set; }

        [JsonProperty("U_fechsalidacam", NullValueHandling = NullValueHandling.Ignore)]
        public object UFechsalidacam { get; set; }

        [JsonProperty("U_tipopedido", NullValueHandling = NullValueHandling.Ignore)]
        public string UTipopedido { get; set; }

        [JsonProperty("U_subtipopedido", NullValueHandling = NullValueHandling.Ignore)]
        public string USubtipopedido { get; set; }

        [JsonProperty("U_tipopago", NullValueHandling = NullValueHandling.Ignore)]
        public object UTipopago { get; set; }

        [JsonProperty("U_disponiblecosecha", NullValueHandling = NullValueHandling.Ignore)]
        public object UDisponiblecosecha { get; set; }

        [JsonProperty("U_fechaflujo", NullValueHandling = NullValueHandling.Ignore)]
        public object UFechaflujo { get; set; }

        [JsonProperty("U_factura", NullValueHandling = NullValueHandling.Ignore)]
        public object UFactura { get; set; }

        [JsonProperty("U_solicitante", NullValueHandling = NullValueHandling.Ignore)]
        public string USolicitante { get; set; }

        [JsonProperty("U_zona", NullValueHandling = NullValueHandling.Ignore)]
        public string UZona { get; set; }

        [JsonProperty("U_subcliente", NullValueHandling = NullValueHandling.Ignore)]
        public object USubcliente { get; set; }

        [JsonProperty("U_unidadconversion", NullValueHandling = NullValueHandling.Ignore)]
        public string UUnidadconversion { get; set; }

        [JsonProperty("U_codexportacion", NullValueHandling = NullValueHandling.Ignore)]
        public object UCodexportacion { get; set; }

        [JsonProperty("U_EXX_FE_TpoDTE", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoDTE { get; set; }

        [JsonProperty("U_EXX_FE_IndNoRebaja", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEIndNoRebaja { get; set; }

        [JsonProperty("U_EXX_FE_TpoDespacho", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoDespacho { get; set; }

        [JsonProperty("U_EXX_FE_IndTraslado", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEIndTraslado { get; set; }

        [JsonProperty("U_EXX_FE_TpoImprsion", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoImprsion { get; set; }

        [JsonProperty("U_EXX_FE_IndServicio", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEIndServicio { get; set; }

        [JsonProperty("U_EXX_FE_MedioPago", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEMedioPago { get; set; }

        [JsonProperty("U_EXX_FE_TpoCtaPago", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoCtaPago { get; set; }

        [JsonProperty("U_EXX_FE_FmaPagExp", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEFmaPagExp { get; set; }

        [JsonProperty("U_EXX_FE_Estado", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEEstado { get; set; }

        [JsonProperty("U_EXX_FE_FechaEvento", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEFechaEvento { get; set; }

        [JsonProperty("U_EXX_FE_CODERR", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFECODERR { get; set; }

        [JsonProperty("U_EXX_FE_DESERR", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEDESERR { get; set; }

        [JsonProperty("U_EXX_FE_PdfCreado", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEPdfCreado { get; set; }

        [JsonProperty("U_EXX_FE_PdfError", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEPdfError { get; set; }

        [JsonProperty("U_EXX_FE_MailEnviado", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEMailEnviado { get; set; }

        [JsonProperty("U_EXX_FE_MailError", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEMailError { get; set; }

        [JsonProperty("U_EXX_FE_TpoTxVenta", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoTxVenta { get; set; }

        [JsonProperty("U_EXX_FE_TpoTxCompra", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETpoTxCompra { get; set; }

        [JsonProperty("U_EXX_FE_TrackID", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETrackID { get; set; }

        [JsonProperty("U_EXX_FE_TrackIDCes", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFETrackIDCes { get; set; }

        [JsonProperty("U_EXX_FE_EstadoCes", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEEstadoCes { get; set; }

        [JsonProperty("U_EXX_FE_FchRecepSII", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEFchRecepSII { get; set; }

        [JsonProperty("U_EXX_FE_AutoAcept", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEAutoAcept { get; set; }

        [JsonProperty("U_EXX_FE_Print", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEPrint { get; set; }

        [JsonProperty("U_EXX_FE_EnvioMasivo", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEEnvioMasivo { get; set; }

        [JsonProperty("U_Bol_Ini", NullValueHandling = NullValueHandling.Ignore)]
        public int? UBolIni { get; set; }

        [JsonProperty("U_Bol_Fin", NullValueHandling = NullValueHandling.Ignore)]
        public int? UBolFin { get; set; }

        [JsonProperty("U_NUMFACT", NullValueHandling = NullValueHandling.Ignore)]
        public object UNUMFACT { get; set; }

        [JsonProperty("U_actividad", NullValueHandling = NullValueHandling.Ignore)]
        public object UActividad { get; set; }

        [JsonProperty("U_grupoFC", NullValueHandling = NullValueHandling.Ignore)]
        public object UGrupoFC { get; set; }

        [JsonProperty("U_itemflujo", NullValueHandling = NullValueHandling.Ignore)]
        public object UItemflujo { get; set; }

        [JsonProperty("U_FEX_PATENTECARRO", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXPATENTECARRO { get; set; }

        [JsonProperty("U_FEX_PATENTECAMION", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXPATENTECAMION { get; set; }

        [JsonProperty("U_FEX_CHOFER", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXCHOFER { get; set; }

        [JsonProperty("U_FEX_RUT", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXRUT { get; set; }

        [JsonProperty("U_FEX_INF", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXINF { get; set; }

        [JsonProperty("U_FEX_ORIGEN", NullValueHandling = NullValueHandling.Ignore)]
        public object UFEXORIGEN { get; set; }

        [JsonProperty("U_equipocarguio", NullValueHandling = NullValueHandling.Ignore)]
        public object UEquipocarguio { get; set; }

        [JsonProperty("U_nomopegrua", NullValueHandling = NullValueHandling.Ignore)]
        public object UNomopegrua { get; set; }

        [JsonProperty("U_rutopegrua", NullValueHandling = NullValueHandling.Ignore)]
        public object URutopegrua { get; set; }

        [JsonProperty("U_tipogestion", NullValueHandling = NullValueHandling.Ignore)]
        public object UTipogestion { get; set; }

        [JsonProperty("U_despachador", NullValueHandling = NullValueHandling.Ignore)]
        public object UDespachador { get; set; }

        [JsonProperty("U_Destino", NullValueHandling = NullValueHandling.Ignore)]
        public object UDestino { get; set; }

        [JsonProperty("U_tipotransporte", NullValueHandling = NullValueHandling.Ignore)]
        public object UTipotransporte { get; set; }

        [JsonProperty("U_transportista", NullValueHandling = NullValueHandling.Ignore)]
        public object UTransportista { get; set; }

        [JsonProperty("U_prodtransporte", NullValueHandling = NullValueHandling.Ignore)]
        public object UProdtransporte { get; set; }

        [JsonProperty("U_Turno", NullValueHandling = NullValueHandling.Ignore)]
        public object UTurno { get; set; }

        [JsonProperty("U_GuiaAnuladaxFE", NullValueHandling = NullValueHandling.Ignore)]
        public string UGuiaAnuladaxFE { get; set; }

        [JsonProperty("U_PrioridadDespacho", NullValueHandling = NullValueHandling.Ignore)]
        public string UPrioridadDespacho { get; set; }

        [JsonProperty("U_PrioridadEntrega", NullValueHandling = NullValueHandling.Ignore)]
        public string UPrioridadEntrega { get; set; }

        [JsonProperty("U_CertificaMadera", NullValueHandling = NullValueHandling.Ignore)]
        public string UCertificaMadera { get; set; }

        [JsonProperty("U_EXX_FE_IDRECEP", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEIDRECEP { get; set; }

        [JsonProperty("U_EXX_FE_RutCes", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFERutCes { get; set; }

        [JsonProperty("U_EXX_FE_FechaCes", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFEFechaCes { get; set; }

        [JsonProperty("U_EXX_FE_TPOGUIA", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFETPOGUIA { get; set; }

        [JsonProperty("U_EXX_FE_AutoAccion", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFEAutoAccion { get; set; }

        [JsonProperty("U_bitem", NullValueHandling = NullValueHandling.Ignore)]
        public object UBitem { get; set; }

        [JsonProperty("U_EXX_FE_CONT_RECEP", NullValueHandling = NullValueHandling.Ignore)]
        public string UEXXFECONTRECEP { get; set; }

        [JsonProperty("U_EXX_FE_CODE_RECEP", NullValueHandling = NullValueHandling.Ignore)]
        public object UEXXFECODERECEP { get; set; }

        [JsonProperty("StockTransfer_ApprovalRequests", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> StockTransferApprovalRequests { get; set; }

        [JsonProperty("ElectronicProtocols", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> ElectronicProtocols { get; set; }

        [JsonProperty("StockTransferLines", NullValueHandling = NullValueHandling.Ignore)]
        public List<StockTransferLine> StockTransferLines { get; set; }

        [JsonProperty("StockTransferTaxExtension", NullValueHandling = NullValueHandling.Ignore)]
        public StockTransferTaxExtension StockTransferTaxExtension { get; set; }

        [JsonProperty("DocumentReferences", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> DocumentReferences { get; set; }
    }
}
