using System.Net;
using Leonera_API_ExternalServices.Modules.Core;

namespace Leonera_API_ExternalServices.Modules.ProfitCenters;

public class ProfitCentersService(CoreService coreService)
{
    private const string UriPath = "ProfitCenters";
    private const string LogPrefix = "[ProfitCentersService]";

    public async Task<(HttpStatusCode StatusCode, string Content)> GetAllProfitCenters(string? auth)
    {
        const string url = "?$select=CenterCode,CenterName,U_ceco,U_area,U_grupo&$filter=Active eq 'Y'";
        return await coreService.ExecuteGetApiCall(UriPath + url, auth);
    }
}
