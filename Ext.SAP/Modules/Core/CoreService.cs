using System.Net;
using Leonera_API_ExternalServices.Config;
using Leonera_API_ExternalServices.Modules.Core.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Serilog;

namespace Leonera_API_ExternalServices.Modules.Core;

/// <summary>
///     Servicio principal para comunicar con SAP
/// </summary>
/// <param name="serviceProvider"> Proveedor de servicios </param>
public class CoreService(IServiceProvider serviceProvider)
{
    private const string LogPrefix = "[ServiceLayer][Core]";
    private const string CookieVariableName = "Cookie";

    private readonly SapConfiguration _config = serviceProvider.GetRequiredService<IOptions<SapConfiguration>>().Value;

    // Helper methods for error mapping
    private static bool IsSuccessStatus(HttpStatusCode code) =>
        code is HttpStatusCode.OK or HttpStatusCode.Created or HttpStatusCode.Accepted or HttpStatusCode.NoContent;

    private static string MapError(HttpStatusCode status, string? content) =>
        IsSuccessStatus(status) ? content ?? string.Empty : SapErrorMapper.Map(content);

    /// <summary>
    ///     Initializes a new instance of the RestClient class with the specified configuration.
    /// </summary>
    /// <returns>A configured RestClient instance.</returns>
    public RestClient InitializeRestClient()
    {
        // Ignore SSL certificate validation
        var options = new RestClientOptions
        {
            ClientCertificates = null,
            RemoteCertificateValidationCallback = (_, _, _, _) => true,
            BaseUrl = new Uri(_config.SapUrl),
            Proxy = WebRequest.GetSystemWebProxy(),
            Timeout = TimeSpan.FromMinutes(20),
        };

        // Create a new RestClient instance with the provided options
        var client = new RestClient(options);

        // Set the security protocol to TLS 1.1 and TLS 1.2
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

        // Return the configured RestClient instance
        return client;
    }

    /// <summary>
    ///     Executes the provided RestRequest and returns the ApiResponse.
    /// </summary>
    /// <param name="request">The RestRequest to be executed.</param>
    /// <returns>An ApiResponse object containing the HTTP status code and response content.</returns>
    /// <exception cref="Exception">Re-throws any exceptions that occur during the execution of the request.</exception>
    private async Task<ApiResponse?> ExecuteRequest(RestRequest request)
    {
        // Initialize the RestClient using the InitializeRestClient method
        var client = InitializeRestClient();

        try
        {
            // Execute the RestRequest asynchronously and await the response
            var response = await client.ExecuteAsync(request);

            // Return a new ApiResponse object containing the HTTP status code and response content
            return new ApiResponse { StatusCode = response.StatusCode, Content = response.Content };
        }
        catch (Exception ex)
        {
            // Log the error message and re-throw the exception for the caller to handle
            Log.Error(ex, "Error executing call");
            return null;
        }
    }

    /// <summary>
    ///     Executes a GET API call to the specified URI with the provided authentication token.
    /// </summary>
    /// <param name="uri">The URI of the API endpoint.</param>
    /// <param name="auth">The authentication token.</param>
    /// <returns>
    ///     A tuple containing the HTTP status code and the response content.
    /// </returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> ExecuteGetApiCall(string uri, string? auth)
    {
        // Create a new RestRequest for the specified URI
        var request = new RestRequest(uri);

        // Construct the authentication cookie using the provided authentication token
        string authCookie = $"B1SESSION={auth}";

        // Add the authentication cookie to the request headers
        request.AddHeader(CookieVariableName, authCookie);

        // Execute the request using the ExecuteRequest method and await the response
        var response = await ExecuteRequest(request);

        // Return a tuple containing the HTTP status code and the response content
        return (response.StatusCode, MapError(response.StatusCode, response.Content));
    }

    /// <summary>
    ///     Executes a POST API call to the specified URI with the provided body and authentication token.
    /// </summary>
    /// <param name="uri">The URI of the API endpoint.</param>
    /// <param name="body">The object representing the request body.</param>
    /// <param name="auth">The authentication token.</param>
    /// <returns>A tuple containing the HTTP status code and the response content.</returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> ExecutePostApiCall(
        string uri,
        object body,
        string? auth
    )
    {
        // Serialization settings for JSON payload
        var settings = new JsonSerializerSettings
        {
            DateFormatString = "yyyy-MM-ddTHH:mm:ssZ",
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            ContractResolver = new DefaultContractResolver(),
        };

        var payload = body is not string ? JsonConvert.SerializeObject(body, settings) : body.ToString();

        // Create a new RestRequest for POST method
        var request = new RestRequest(uri, Method.Post);

        // Add JSON payload to the request
        request.AddJsonBody(payload!);

        Log.Information("{Payload}", payload);
        // Add authentication cookie to the request
        string authCookie = $"B1SESSION={auth}";
        request.AddHeader(CookieVariableName, authCookie);

        // Execute the request and get the response
        var response = await ExecuteRequest(request);

        // Log the response status code and content
        Log.Information("{Prefix} Response status code: {StatusCode}", LogPrefix, response.StatusCode);
        if (
            response.StatusCode
            is not (HttpStatusCode.Accepted or HttpStatusCode.OK or HttpStatusCode.Created or HttpStatusCode.NoContent)
        )
        {
            Log.Information("{Prefix} Response content: {Content}", LogPrefix, response.Content);
        }

        // Return the response status code and content
        return (response.StatusCode, MapError(response.StatusCode, response.Content));
    }

    /// <summary>
    ///     Executes a PATCH API call to the specified URL with the provided body and authentication token.
    /// </summary>
    /// <param name="url">The URL of the API endpoint.</param>
    /// <param name="body">The object representing the request body.</param>
    /// <param name="auth">The authentication token.</param>
    /// <returns>
    ///     A tuple containing the HTTP status code and the response content.
    /// </returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> ExecutePatchApiCall(
        string url,
        object body,
        string? auth
    )
    {
        // Serialization settings for JSON payload
        var settings = new JsonSerializerSettings
        {
            DateFormatString = "yyyy-MM-ddTHH:mm:ssZ",
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            ContractResolver = new DefaultContractResolver(),
            Formatting = Formatting.None,
        };

        string? payload = body is not string ? JsonConvert.SerializeObject(body, settings) : body.ToString();

        // Create a new RestRequest for PATCH method
        var request = new RestRequest(url, Method.Patch);

        // Add JSON payload to the request
        request.AddJsonBody(payload!);

        // Add authentication cookie to the request
        string authCookie = $"B1SESSION={auth}";
        request.AddHeader(CookieVariableName, authCookie);

        Log.Information("request: {Payload}", payload);
        // Execute the request and get the response
        var response = await ExecuteRequest(request);
        Log.Information(
            " response status code: {StatusCode} || response content: {Content}",
            response.StatusCode,
            response.Content
        );
        // Return the response status code and content
        return (response.StatusCode, MapError(response.StatusCode, response.Content));
    }

    /// <summary>
    ///     Executes a DELETE API call to the specified URL with the provided authentication token.
    /// </summary>
    /// <param name="url">The URL of the API endpoint.</param>
    /// <param name="authToken">The authentication token.</param>
    /// <returns>
    ///     A tuple containing the HTTP status code and the response content.
    ///     If the request fails, the status code will be set to InternalServerError and the content will be set to "Error al
    ///     realizar la llamada al Service".
    /// </returns>
    public async Task<(HttpStatusCode StatusCode, string? Content)> ExecuteDeleteApiCall(string url, string? authToken)
    {
        // Log the start of the DELETE call
        Log.Information("Executing DELETE call to: {Url}", url);

        // Create a new RestRequest for DELETE method
        var request = new RestRequest(url, Method.Delete);

        // Add authentication cookie to the request
        string authCookie = $"B1SESSION={authToken}";
        request.AddHeader(CookieVariableName, authCookie);

        // Initialize the RestClient
        var client = InitializeRestClient();

        try
        {
            // Execute the DELETE request and get the response
            var response = await client.ExecuteAsync(request);

            // Return the response status code and content
            return (response.StatusCode, MapError(response.StatusCode, response.Content));
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error executing DELETE call to: {Url}", url);
            // In case of an exception, return an InternalServerError status code and a custom error message
            return (HttpStatusCode.InternalServerError, "Error al realizar la llamada al Service");
        }
    }

    /// <summary>
    ///     Executes a GET API call to the specified nextLink URL with the provided authentication token.
    /// </summary>
    /// <param name="nextLink">The URL of the next page in the API response.</param>
    /// <param name="auth">The authentication token.</param>
    /// <returns>A string containing the content of the response, or null if the request fails.</returns>
    public async Task<(HttpStatusCode StatusCode, string Content)> GetnextLinkContent(string nextLink, string? auth)
    {
        // Return the content of the response
        return await ExecuteGetApiCall(nextLink, auth);
    }
}
