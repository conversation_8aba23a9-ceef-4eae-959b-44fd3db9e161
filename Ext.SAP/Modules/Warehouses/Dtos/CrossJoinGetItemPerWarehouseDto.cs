using Newtonsoft.Json;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Leonera_API_ExternalServices.Modules.Warehouses.Dtos;

public class CrossJoinGetItemPerWarehouseDto
{
    [JsonProperty("@odata.context")]
    public string odatacontext { get; set; }
    public List<Content> value { get; set; }
}

public class Content
{
    public Items Items { get; set; }

    [JsonProperty("Items/ItemWarehouseInfoCollection")]
    public ItemWarehouseInfoCollection itemsPerWarehouse { get; set; }
}

public class Items
{
    public string ItemCode { get; set; }
    public string ItemName { get; set; }
    public string ManageBatchNumbers { get; set; }
    public double U_factorm3 { get; set; }
}

public class ItemWarehouseInfoCollection
{
    public double InStock { get; set; }
}
