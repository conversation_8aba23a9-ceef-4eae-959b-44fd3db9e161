using Newtonsoft.Json;

namespace Leonera_API_ExternalServices.Modules.Cuotas.Entities;

public class CreateCuotasPost
{
    [JsonProperty("U_SN", NullValueHandling = NullValueHandling.Ignore)]
    public string? BusinessPartner { get; set; }

    [JsonProperty("U_Tipo", NullValueHandling = NullValueHandling.Ignore)]
    public string? Type { get; set; }

    [JsonProperty("U_actividad", NullValueHandling = NullValueHandling.Ignore)]
    public string? Activity { get; set; }

    [JsonProperty("U_grupoFC", NullValueHandling = NullValueHandling.Ignore)]
    public string? Group { get; set; }

    [JsonProperty("U_itemflujoc", NullValueHandling = NullValueHandling.Ignore)]
    public string? Item { get; set; }

    [JsonProperty("U_SNOM", NullValueHandling = NullValueHandling.Ignore)]
    public string? BusinessPartnerName { get; set; }

    [JsonProperty("U_Num", NullValueHandling = NullValueHandling.Ignore)]
    public string? Number { get; set; }

    [JsonProperty("U_empresa", NullValueHandling = NullValueHandling.Ignore)]
    public string? MultibranchCompany { get; set; }

    [JsonProperty("U_Date", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime? CreationDate { get; set; }

    [JsonProperty("FUT_CUO1Collection", NullValueHandling = NullValueHandling.Ignore)]
    public List<Datum> Data { get; set; }

    public class Datum
    {
        [JsonProperty("U_Cuota", NullValueHandling = NullValueHandling.Ignore)]
        public double? Cuota { get; set; }

        [JsonProperty("U_Monto", NullValueHandling = NullValueHandling.Ignore)]
        public double? Amount { get; set; }

        [JsonProperty("U_Venc", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? DueDate { get; set; }

        [JsonProperty("U_Moneda", NullValueHandling = NullValueHandling.Ignore)]
        public string? Currency { get; set; }
    }
}
