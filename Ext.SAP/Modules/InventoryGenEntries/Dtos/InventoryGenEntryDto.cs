using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Leonera_API_ExternalServices.Modules.InventoryGenEntries.Dtos;

public class InventoryGenEntryDto
{
    public int DocEntry { get; set; }

    [StringLength(100, ErrorMessage = "Los comentarios no pueden exceder los 100 caracteres.")]
    public string Comments { get; set; }

    [Required(ErrorMessage = "La fecha del documento es requerida.")]
    public DateTime DocDate { get; set; }

    [Required(ErrorMessage = "Las líneas del documento son requeridas.")]
    public List<DocumentLineDto> DocumentLines { get; set; }
}

public class DocumentLineDto
{
    [Required(ErrorMessage = "El código del artículo es requerido.")]
    [StringLength(20, ErrorMessage = "El código del artículo no puede exceder los 20 caracteres.")]
    public string ItemCode { get; set; }

    [Required(ErrorMessage = "El código del almacén es requerido.")]
    [StringLength(20, ErrorMessage = "El código del almacén no puede exceder los 20 caracteres.")]
    public string WarehouseCode { get; set; }

    [Required(ErrorMessage = "La cantidad es requerida.")]
    [Range(0, double.MaxValue, ErrorMessage = "La cantidad debe ser un número positivo.")]
    public double Quantity { get; set; }

    [Required(ErrorMessage = "El precio unitario es requerido.")]
    [Range(0, double.MaxValue, ErrorMessage = "El precio unitario debe ser un número positivo.")]
    public double? UnitPrice { get; set; }

    [StringLength(20, ErrorMessage = "El CostingCode no puede exceder los 20 caracteres.")]
    public string? CostingCode { get; set; }

    [StringLength(20, ErrorMessage = "El CostingCode2 no puede exceder los 20 caracteres.")]
    public string? CostingCode2 { get; set; }

    [StringLength(20, ErrorMessage = "El CostingCode3 no puede exceder los 20 caracteres.")]
    public string? CostingCode3 { get; set; }

    [StringLength(20, ErrorMessage = "El CostingCode4 no puede exceder los 20 caracteres.")]
    public string? CostingCode4 { get; set; }

    [JsonProperty("U_movin", NullValueHandling = NullValueHandling.Ignore)]
    public string? Movement { get; set; }

    [Required(ErrorMessage = "La lista de números de lote es requerida.")]
    public List<BatchNumberDto> BatchNumbers { get; set; }

    public double? U_PrecioRefCombus { get; set; }
}

public class BatchNumberDto
{
    [Required(ErrorMessage = "El número de lote es requerido.")]
    [StringLength(50, ErrorMessage = "El número de lote no puede exceder los 50 caracteres.")]
    public string BatchNumber { get; set; }

    [Required(ErrorMessage = "La cantidad es requerida.")]
    public double Quantity { get; set; }

    [Required(ErrorMessage = "El código del artículo es requerido.")]
    [StringLength(20, ErrorMessage = "El código del artículo no puede exceder los 20 caracteres.")]
    public string ItemCode { get; set; }

    public DateTime ManufacturingDate { get; set; }
}
