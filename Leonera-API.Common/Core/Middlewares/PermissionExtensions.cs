using Microsoft.Extensions.DependencyInjection;

namespace Leonera_API.Common.Core.Middlewares;

/// <summary>
/// Extensiones para configurar el sistema de autorización de permisos
/// </summary>
public static class PermissionExtensions
{
    /// <summary>
    /// Registra el servicio de autorización de permisos en el contenedor de dependencias
    /// </summary>
    /// <param name="services">Colección de servicios</param>
    /// <returns>Colección de servicios</returns>
    public static IServiceCollection AddPermissionAuthorization(this IServiceCollection services)
    {
        services.AddScoped<IPermissionAuthorizationService, PermissionAuthorizationService>();
        return services;
    }
}
