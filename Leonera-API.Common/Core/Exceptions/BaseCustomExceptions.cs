using System.Net;

namespace Leonera_API.Common.Core.Exceptions;

public class BaseCustomExceptions : Exception
{
    public BaseCustomExceptions(
        string message,
        IEnumerable<string> errors,
        HttpStatusCode statusCode = HttpStatusCode.InternalServerError
    )
        : base(message)
    {
        ErrorMessages = errors;
        StatusCode = statusCode;
    }

    public BaseCustomExceptions(string message)
        : base(message)
    {
        ErrorMessages = new List<string>();
    }

    public IEnumerable<string> ErrorMessages { get; }

    public HttpStatusCode StatusCode { get; }
}
