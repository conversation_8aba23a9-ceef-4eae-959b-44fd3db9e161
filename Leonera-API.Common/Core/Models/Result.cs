using System.Collections.Generic;
using System.Linq;

namespace Leonera_API.Common.Core.Models;

/// <summary>
/// Represents a result of an operation that can either succeed or fail.
/// This class follows the Result pattern for better error handling in command handlers.
/// </summary>
/// <typeparam name="T">The type of the value returned on success</typeparam>
public class Result<T>
{
    private readonly List<string> _errors;
    private readonly List<string> _warnings;

    /// <summary>
    /// Private constructor to enforce factory method usage
    /// </summary>
    /// <param name="isSuccess">Indicates if the operation was successful</param>
    /// <param name="value">The result value (can be null for failures)</param>
    /// <param name="message">The main result message</param>
    /// <param name="statusCode">HTTP status code for API responses</param>
    /// <param name="errors">Collection of error messages</param>
    /// <param name="warnings">Collection of warning messages</param>
    private Result(
        bool isSuccess,
        T? value,
        string message,
        int statusCode,
        IEnumerable<string>? errors = null,
        IEnumerable<string>? warnings = null
    )
    {
        IsSuccess = isSuccess;
        Value = value;
        Message = message;
        StatusCode = statusCode;
        _errors = errors?.ToList() ?? new List<string>();
        _warnings = warnings?.ToList() ?? new List<string>();
    }

    /// <summary>
    /// Indicates whether the operation was successful
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// Indicates whether the operation failed
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// The main result message
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// The result value (null for failures)
    /// </summary>
    public T? Value { get; }

    /// <summary>
    /// HTTP status code for API responses
    /// </summary>
    public int StatusCode { get; }

    /// <summary>
    /// Collection of error messages
    /// </summary>
    public IReadOnlyCollection<string> Errors => _errors.AsReadOnly();

    /// <summary>
    /// Collection of warning messages
    /// </summary>
    public IReadOnlyCollection<string> Warnings => _warnings.AsReadOnly();

    /// <summary>
    /// Indicates if there are any errors
    /// </summary>
    public bool HasErrors => _errors.Any();

    /// <summary>
    /// Indicates if there are any warnings
    /// </summary>
    public bool HasWarnings => _warnings.Any();

    /// <summary>
    /// Creates a successful result with a value
    /// </summary>
    /// <param name="value">The result value</param>
    /// <param name="message">Optional success message</param>
    /// <param name="statusCode">HTTP status code (default: 200)</param>
    /// <returns>A successful result</returns>
    public static Result<T> Success(T value, string message = "Operation completed successfully", int statusCode = 200)
    {
        return new Result<T>(true, value, message, statusCode);
    }

    /// <summary>
    /// Creates a successful result with warnings
    /// </summary>
    /// <param name="value">The result value</param>
    /// <param name="warnings">Collection of warning messages</param>
    /// <param name="message">Optional success message</param>
    /// <param name="statusCode">HTTP status code (default: 200)</param>
    /// <returns>A successful result with warnings</returns>
    public static Result<T> SuccessWithWarnings(
        T value,
        IEnumerable<string> warnings,
        string message = "Operation completed successfully with warnings",
        int statusCode = 200
    )
    {
        return new Result<T>(true, value, message, statusCode, warnings: warnings);
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="statusCode">HTTP status code (default: 400)</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(string message, int statusCode = 400)
    {
        return new Result<T>(false, default, message, statusCode, new[] { message });
    }

    /// <summary>
    /// Creates a failed result with multiple errors
    /// </summary>
    /// <param name="errors">Collection of error messages</param>
    /// <param name="message">Main error message</param>
    /// <param name="statusCode">HTTP status code (default: 400)</param>
    /// <returns>A failed result with multiple errors</returns>
    public static Result<T> Failure(
        IEnumerable<string> errors,
        string message = "Operation failed",
        int statusCode = 400
    )
    {
        return new Result<T>(false, default, message, statusCode, errors);
    }

    /// <summary>
    /// Creates a failed result with a value (useful for partial failures)
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="value">Partial result value</param>
    /// <param name="statusCode">HTTP status code (default: 400)</param>
    /// <returns>A failed result with a value</returns>
    public static Result<T> Failure(string message, T? value, int statusCode = 400)
    {
        return new Result<T>(false, value, message, statusCode, new[] { message });
    }

    /// <summary>
    /// Creates a validation failure result
    /// </summary>
    /// <param name="validationErrors">Collection of validation error messages</param>
    /// <param name="message">Main validation message</param>
    /// <returns>A validation failure result</returns>
    public static Result<T> ValidationFailure(
        IEnumerable<string> validationErrors,
        string message = "Validation failed"
    )
    {
        return new Result<T>(false, default, message, 422, validationErrors);
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <param name="message">Not found message</param>
    /// <returns>A not found result</returns>
    public static Result<T> NotFound(string message = "Resource not found")
    {
        return new Result<T>(false, default, message, 404, new[] { message });
    }

    /// <summary>
    /// Creates an unauthorized result
    /// </summary>
    /// <param name="message">Unauthorized message</param>
    /// <returns>An unauthorized result</returns>
    public static Result<T> Unauthorized(string message = "Unauthorized access")
    {
        return new Result<T>(false, default, message, 401, new[] { message });
    }

    /// <summary>
    /// Creates a forbidden result
    /// </summary>
    /// <param name="message">Forbidden message</param>
    /// <returns>A forbidden result</returns>
    public static Result<T> Forbidden(string message = "Access forbidden")
    {
        return new Result<T>(false, default, message, 403, new[] { message });
    }

    /// <summary>
    /// Creates a conflict result
    /// </summary>
    /// <param name="message">Conflict message</param>
    /// <returns>A conflict result</returns>
    public static Result<T> Conflict(string message = "Resource conflict")
    {
        return new Result<T>(false, default, message, 409, new[] { message });
    }

    /// <summary>
    /// Creates an internal server error result
    /// </summary>
    /// <param name="message">Error message</param>
    /// <returns>An internal server error result</returns>
    public static Result<T> InternalError(string message = "Internal server error")
    {
        return new Result<T>(false, default, message, 500, new[] { message });
    }

    /// <summary>
    /// Maps the result to a new type using a transformation function
    /// </summary>
    /// <typeparam name="TNew">The new result type</typeparam>
    /// <param name="mapper">Function to transform the value</param>
    /// <returns>A new result with the transformed value</returns>
    public Result<TNew> Map<TNew>(Func<T, TNew> mapper)
    {
        if (IsFailure)
        {
            return Result<TNew>.Failure(Errors, Message, StatusCode);
        }

        try
        {
            var newValue = mapper(Value!);
            return Result<TNew>.Success(newValue, Message, StatusCode);
        }
        catch (Exception ex)
        {
            return Result<TNew>.InternalError($"Mapping failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Binds the result to another result using a transformation function
    /// </summary>
    /// <typeparam name="TNew">The new result type</typeparam>
    /// <param name="binder">Function that returns a new result</param>
    /// <returns>The bound result</returns>
    public Result<TNew> Bind<TNew>(Func<T, Result<TNew>> binder)
    {
        if (IsFailure)
        {
            return Result<TNew>.Failure(Errors, Message, StatusCode);
        }

        try
        {
            return binder(Value!);
        }
        catch (Exception ex)
        {
            return Result<TNew>.InternalError($"Binding failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Executes an action on success, returns the original result
    /// </summary>
    /// <param name="action">Action to execute on success</param>
    /// <returns>The original result</returns>
    public Result<T> OnSuccess(Action<T> action)
    {
        if (IsSuccess)
        {
            action(Value!);
        }
        return this;
    }

    /// <summary>
    /// Executes an action on failure, returns the original result
    /// </summary>
    /// <param name="action">Action to execute on failure</param>
    /// <returns>The original result</returns>
    public Result<T> OnFailure(Action<IReadOnlyCollection<string>> action)
    {
        if (IsFailure)
        {
            action(Errors);
        }
        return this;
    }

    /// <summary>
    /// Ensures a condition is met, otherwise returns a failure
    /// </summary>
    /// <param name="predicate">Condition to check</param>
    /// <param name="errorMessage">Error message if condition fails</param>
    /// <returns>Success if condition is met, failure otherwise</returns>
    public Result<T> Ensure(Func<T, bool> predicate, string errorMessage)
    {
        if (IsFailure)
        {
            return this;
        }

        return predicate(Value!) ? this : Failure(errorMessage, StatusCode);
    }

    /// <summary>
    /// Combines multiple results into a single result
    /// </summary>
    /// <param name="results">Collection of results to combine</param>
    /// <returns>A combined result</returns>
    public static Result<IEnumerable<T>> Combine(IEnumerable<Result<T>> results)
    {
        var resultsList = results.ToList();
        var errors = new List<string>();
        var values = new List<T>();

        foreach (var result in resultsList)
        {
            if (result.IsSuccess)
            {
                values.Add(result.Value!);
            }
            else
            {
                errors.AddRange(result.Errors);
            }
        }

        if (errors.Any())
        {
            return Result<IEnumerable<T>>.Failure(errors, "Multiple operations failed");
        }

        return Result<IEnumerable<T>>.Success(values, "All operations completed successfully");
    }

    /// <summary>
    /// Implicit conversion from value to success result
    /// </summary>
    /// <param name="value">The value to convert</param>
    public static implicit operator Result<T>(T value) => Success(value);

    /// <summary>
    /// String representation of the result
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return IsSuccess ? $"Success: {Message}" : $"Failure: {Message} - Errors: {string.Join(", ", Errors)}";
    }
}

/// <summary>
/// Non-generic result for operations that don't return a value
/// </summary>
public class Result
{
    private readonly List<string> _errors;
    private readonly List<string> _warnings;

    private Result(
        bool isSuccess,
        string message,
        int statusCode,
        IEnumerable<string>? errors = null,
        IEnumerable<string>? warnings = null
    )
    {
        IsSuccess = isSuccess;
        Message = message;
        StatusCode = statusCode;
        _errors = errors?.ToList() ?? new List<string>();
        _warnings = warnings?.ToList() ?? new List<string>();
    }

    /// <summary>
    /// Indicates whether the operation was successful
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// Indicates whether the operation failed
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// The main result message
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// HTTP status code for API responses
    /// </summary>
    public int StatusCode { get; }

    /// <summary>
    /// Collection of error messages
    /// </summary>
    public IReadOnlyCollection<string> Errors => _errors.AsReadOnly();

    /// <summary>
    /// Collection of warning messages
    /// </summary>
    public IReadOnlyCollection<string> Warnings => _warnings.AsReadOnly();

    /// <summary>
    /// Indicates if there are any errors
    /// </summary>
    public bool HasErrors => _errors.Any();

    /// <summary>
    /// Indicates if there are any warnings
    /// </summary>
    public bool HasWarnings => _warnings.Any();

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="message">Success message</param>
    /// <param name="statusCode">HTTP status code (default: 200)</param>
    /// <returns>A successful result</returns>
    public static Result Success(string message = "Operation completed successfully", int statusCode = 200)
    {
        return new Result(true, message, statusCode);
    }

    /// <summary>
    /// Creates a successful result with warnings
    /// </summary>
    /// <param name="warnings">Collection of warning messages</param>
    /// <param name="message">Success message</param>
    /// <param name="statusCode">HTTP status code (default: 200)</param>
    /// <returns>A successful result with warnings</returns>
    public static Result SuccessWithWarnings(
        IEnumerable<string> warnings,
        string message = "Operation completed successfully with warnings",
        int statusCode = 200
    )
    {
        return new Result(true, message, statusCode, warnings: warnings);
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="statusCode">HTTP status code (default: 400)</param>
    /// <returns>A failed result</returns>
    public static Result Failure(string message, int statusCode = 400)
    {
        return new Result(false, message, statusCode, new[] { message });
    }

    /// <summary>
    /// Creates a failed result with multiple errors
    /// </summary>
    /// <param name="errors">Collection of error messages</param>
    /// <param name="message">Main error message</param>
    /// <param name="statusCode">HTTP status code (default: 400)</param>
    /// <returns>A failed result with multiple errors</returns>
    public static Result Failure(IEnumerable<string> errors, string message = "Operation failed", int statusCode = 400)
    {
        return new Result(false, message, statusCode, errors);
    }

    /// <summary>
    /// Creates a validation failure result
    /// </summary>
    /// <param name="validationErrors">Collection of validation error messages</param>
    /// <param name="message">Main validation message</param>
    /// <returns>A validation failure result</returns>
    public static Result ValidationFailure(IEnumerable<string> validationErrors, string message = "Validation failed")
    {
        return new Result(false, message, 422, validationErrors);
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <param name="message">Not found message</param>
    /// <returns>A not found result</returns>
    public static Result NotFound(string message = "Resource not found")
    {
        return new Result(false, message, 404, new[] { message });
    }

    /// <summary>
    /// Creates an unauthorized result
    /// </summary>
    /// <param name="message">Unauthorized message</param>
    /// <returns>An unauthorized result</returns>
    public static Result Unauthorized(string message = "Unauthorized access")
    {
        return new Result(false, message, 401, new[] { message });
    }

    /// <summary>
    /// Creates a forbidden result
    /// </summary>
    /// <param name="message">Forbidden message</param>
    /// <returns>A forbidden result</returns>
    public static Result Forbidden(string message = "Access forbidden")
    {
        return new Result(false, message, 403, new[] { message });
    }

    /// <summary>
    /// Creates a conflict result
    /// </summary>
    /// <param name="message">Conflict message</param>
    /// <returns>A conflict result</returns>
    public static Result Conflict(string message = "Resource conflict")
    {
        return new Result(false, message, 409, new[] { message });
    }

    /// <summary>
    /// Creates an internal server error result
    /// </summary>
    /// <param name="message">Error message</param>
    /// <returns>An internal server error result</returns>
    public static Result InternalError(string message = "Internal server error")
    {
        return new Result(false, message, 500, new[] { message });
    }

    /// <summary>
    /// Executes an action on success, returns the original result
    /// </summary>
    /// <param name="action">Action to execute on success</param>
    /// <returns>The original result</returns>
    public Result OnSuccess(Action action)
    {
        if (IsSuccess)
        {
            action();
        }
        return this;
    }

    /// <summary>
    /// Executes an action on failure, returns the original result
    /// </summary>
    /// <param name="action">Action to execute on failure</param>
    /// <returns>The original result</returns>
    public Result OnFailure(Action<IReadOnlyCollection<string>> action)
    {
        if (IsFailure)
        {
            action(Errors);
        }
        return this;
    }

    /// <summary>
    /// Combines multiple results into a single result
    /// </summary>
    /// <param name="results">Collection of results to combine</param>
    /// <returns>A combined result</returns>
    public static Result Combine(IEnumerable<Result> results)
    {
        var resultsList = results.ToList();
        var errors = new List<string>();

        foreach (var result in resultsList)
        {
            if (result.IsFailure)
            {
                errors.AddRange(result.Errors);
            }
        }

        if (errors.Any())
        {
            return Failure(errors, "Multiple operations failed");
        }

        return Success("All operations completed successfully");
    }

    /// <summary>
    /// String representation of the result
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return IsSuccess ? $"Success: {Message}" : $"Failure: {Message} - Errors: {string.Join(", ", Errors)}";
    }
}
