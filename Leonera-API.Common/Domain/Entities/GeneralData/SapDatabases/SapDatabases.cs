using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;

namespace Leonera_API.Common.Domain.Entities.GeneralData;

[Table("Gd_SapDatabases")]
public class SapDatabases : BaseEntity<int>, IAggregateRoot
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [MaxLength(100)]
    public required string CompanyName { get; set; }

    [MaxLength(30)]
    public required string DatabaseName { get; set; }

    public bool Multibranch { get; set; }

    public bool Active { get; set; }
}
