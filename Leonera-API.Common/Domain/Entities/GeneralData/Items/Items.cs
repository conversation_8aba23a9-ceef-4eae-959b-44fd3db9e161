using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;

namespace Leonera_API.Common.Domain.Entities.GeneralData;

[Table("Gd_Items")]
public class Items : BaseEntity<int>, IAggregateRoot
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [MaxLength(20)]
    public string? ItemCode { get; set; }

    [MaxLength(200)]
    public string? ItemName { get; set; }

    [MaxLength(200)]
    public string? ItemGroup { get; set; }

    public int CompanyId { get; set; }

    public double? ItemPrice { get; set; }

    public double? ItemComission { get; set; }

    [MaxLength(200)]
    public string? ItemCommercialName { get; set; }

    [MaxLength(200)]
    public string? ItemMeasures { get; set; }

    public double? ItemCubicMetricFactor { get; set; }

    public DateTime SyncDate { get; set; }

    [ForeignKey("CompanyId")]
    public SapDatabases? Company { get; set; }

    public double CostPrice { get; set; }
}
