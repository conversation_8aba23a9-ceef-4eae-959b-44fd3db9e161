using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;

namespace Leonera_API.Common.Domain.Entities.Auth;

[Table("Modules", Schema = "auth")]
public class ModuleEntity : BaseEntity<int>, IAggregateRoot
{
    [MaxLength(100)]
    public required string Name { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    public int? AppId { get; set; }
    public bool Active { get; set; } = true;

    public virtual ApplicationEntity? Application { get; set; }

    [JsonIgnore]
    public virtual List<PermissionEntity>? Permissions { get; set; }
}
