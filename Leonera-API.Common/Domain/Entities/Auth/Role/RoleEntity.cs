using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;

namespace Leonera_API.Common.Domain.Entities.Auth;

[Table("Roles", Schema = "auth")]
public class RoleEntity : BaseEntity<int>, IAggregateRoot
{
    [MaxLength(100)]
    public required string Name { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    public int ApplicationId { get; set; }

    public bool Active { get; set; } = true;

    public virtual ApplicationEntity? Application { get; set; }

    [JsonIgnore]
    public virtual List<RolePermission>? RolePermissions { get; set; }

    [JsonIgnore]
    public virtual List<UserRole>? UserRoles { get; set; }
}
