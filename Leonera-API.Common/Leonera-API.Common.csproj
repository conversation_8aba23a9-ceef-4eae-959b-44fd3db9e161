<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>Leonera_API.Common</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" Version="9.2.0" />
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="Mapster" Version="7.4.0"/>
        <PackageReference Include="MediatR" Version="12.5.0"/>
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.6" />
        <PackageReference Include="Serilog" Version="4.3.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1"/>
        <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0"/>
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0"/>
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>
</Project>
