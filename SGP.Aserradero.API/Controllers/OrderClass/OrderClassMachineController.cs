using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Main.OrderClassMachines.Commands.Create;
using SGP.Aserradero.Application.Modules.Main.OrderClassMachines.Queries.GetAll;
using SGP.Aserradero.Application.Modules.Main.OrderClassMachines.Queries.GetMachineByOrder;

namespace SGP_Aserradero.Controllers.OrderClass;

/// <summary>
/// Controlador para la gestión de asociaciones entre clases de orden y máquinas del aserradero.
/// </summary>
/// <remarks>
/// Este controlador maneja las relaciones entre las clases de orden y las máquinas específicas
/// que pueden procesar cada tipo de orden. Permite definir qué máquinas están habilitadas
/// para trabajar con determinadas clases de orden, optimizando la asignación de recursos
/// y garantizando que los trabajos se ejecuten en el equipo adecuado.
/// Las asociaciones son fundamentales para el control de flujo de producción y la
/// planificación eficiente de las operaciones del aserradero.
/// </remarks>
[ApiController]
[Route("[controller]")]
[Tags("Parametros | Clase de Orden - Máquina")]
public class OrderClassMachineController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Obtiene todas las asociaciones entre clases de orden y máquinas con paginación
    /// </summary>
    /// <param name="request">Parámetros de consulta que incluyen criterios de paginación y filtrado</param>
    /// <returns>Lista paginada de todas las asociaciones clase de orden-máquina disponibles en el sistema</returns>
    /// <response code="200">Devuelve la lista paginada de asociaciones clase de orden-máquina</response>
    /// <response code="404">Si no se encuentran asociaciones clase de orden-máquina</response>
    /// <response code="500">Si ocurrió un error interno del servidor</response>
    /// <remarks>
    /// Este endpoint proporciona una vista completa de todas las asociaciones configuradas
    /// entre clases de orden y máquinas del aserradero. La paginación permite manejar
    /// eficientemente grandes volúmenes de datos y mejorar el rendimiento de la consulta.
    /// Los resultados incluyen información detallada sobre cada asociación, facilitando
    /// la administración y el monitoreo de la configuración de máquinas por clase de orden.
    /// Es útil para interfaces de administración y reportes de configuración del sistema.
    /// </remarks>
    [HttpGet]
    [ProducesResponseType(typeof(Result<PagedResult<GetAllOrderClassMachineQueryResponse>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<PagedResult<GetAllOrderClassMachineQueryResponse>>>> GetAll(
        [FromQuery] GetAllOrderClassMachineQuery request
    )
    {
        var result = await mediator.Send(request);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Obtiene las máquinas asociadas a una clase de orden específica
    /// </summary>
    /// <param name="request">Consulta que contiene el identificador de la clase de orden</param>
    /// <returns>Lista de máquinas que están habilitadas para procesar la clase de orden especificada</returns>
    /// <response code="200">Devuelve la lista de máquinas asociadas a la clase de orden</response>
    /// <response code="404">Si no se encuentran máquinas para la clase de orden especificada</response>
    /// <response code="500">Si ocurrió un error interno del servidor</response>
    /// <remarks>
    /// Este endpoint es fundamental para la planificación de producción y asignación de recursos.
    /// Permite identificar qué máquinas específicas pueden procesar una determinada clase de orden,
    /// facilitando la toma de decisiones sobre la programación de trabajos y la optimización
    /// del flujo de producción. La información devuelta incluye detalles de cada máquina
    /// como capacidades, estado actual y especificaciones técnicas relevantes.
    /// Es especialmente útil para sistemas de planificación automática y interfaces
    /// de asignación manual de trabajos.
    /// </remarks>
    [HttpGet("machines")]
    [ProducesResponseType(typeof(Result<List<GetMachineByOrderQueryResponse>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<List<GetMachineByOrderQueryResponse>>>> GetMachinesByOrderId(
        [FromQuery] GetMachineByOrderQuery request
    )
    {
        var result = await mediator.Send(request);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Crea una nueva asociación entre una clase de orden y una máquina en el sistema
    /// </summary>
    /// <param name="request">El comando que contiene los datos para crear la asociación clase de orden-máquina</param>
    /// <returns>Resultado de la operación de creación de la asociación</returns>
    /// <response code="201">Devuelve el resultado de la creación de la nueva asociación clase de orden-máquina</response>
    /// <response code="400">Si la solicitud es inválida o contiene datos incorrectos</response>
    /// <response code="404">Si la clase de orden o la máquina especificada no se encuentra</response>
    /// <response code="409">Si ya existe una asociación con la misma configuración</response>
    /// <response code="500">Si ocurrió un error interno del servidor</response>
    /// <remarks>
    /// Este endpoint permite establecer nuevas relaciones entre clases de orden y máquinas,
    /// definiendo qué equipos están habilitados para procesar determinados tipos de órdenes.
    /// La operación valida la existencia tanto de la clase de orden como de la máquina
    /// antes de crear la asociación. Se verifican restricciones de unicidad para evitar
    /// duplicados y se aplican validaciones de negocio específicas del dominio.
    /// Esta configuración es crítica para el correcto funcionamiento del sistema de
    /// planificación y asignación de recursos productivos del aserradero.
    /// Los cambios pueden afectar inmediatamente la disponibilidad de máquinas
    /// para nuevas órdenes de la clase especificada.
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<bool>>> Create([FromBody] CreateOrderClassMachineCommand request)
    {
        var result = await mediator.Send(request);

        return StatusCode(result.StatusCode, result);
    }
}
