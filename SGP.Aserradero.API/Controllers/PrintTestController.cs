using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace SGP_Aserradero.Controllers;

[ApiController]
[Route("[controller]")]
public class PrintTestController : ControllerBase
{
    private const int DefaultPort = 9000;
    private readonly HttpClient _httpClient;
    private readonly ILogger<PrintTestController> _logger;

    public PrintTestController(IServiceProvider serviceProvider)
    {
        _logger = serviceProvider.GetRequiredService<ILogger<PrintTestController>>();
        _httpClient = serviceProvider.GetRequiredService<HttpClient>();
    }

    /// <summary>
    ///     Limpia y normaliza el código ZPL para que sea válido en JSON
    /// </summary>
    /// <param name="zplCode">Código ZPL original</param>
    /// <returns>Código ZPL limpio y válido para JSON</returns>
    private string CleanZplCode(string zplCode)
    {
        if (string.IsNullOrWhiteSpace(zplCode))
        {
            return string.Empty;
        }

        // Reemplazar caracteres problemáticos
        var cleaned = zplCode
            .Replace("\r\n", "\\n") // Windows line endings
            .Replace("\r", "\\n") // Mac line endings
            .Replace("\n", "\\n") // Unix line endings
            .Replace("\t", "\\t") // Tabs
            .Replace("\"", "\\\"") // Quotes
            .Replace("\\", "\\\\"); // Backslashes

        // Remover caracteres de control que no son válidos en JSON
        var result = new StringBuilder();
        foreach (var c in cleaned)
        {
            if (c >= 32 || c == '\t' || c == '\n' || c == '\r')
            {
                result.Append(c);
            }
            else
            {
                // Convertir caracteres de control a secuencias de escape
                result.Append($"\\u{(int)c:X4}");
            }
        }

        return result.ToString();
    }

    /// <summary>
    ///     Construye la URL completa del ZPL Listener usando la IP del dispositivo
    /// </summary>
    /// <param name="deviceIp">IP del dispositivo (puede incluir puerto opcional)</param>
    /// <returns>URL completa del ZPL Listener</returns>
    private string BuildZplListenerUrl(string deviceIp)
    {
        if (string.IsNullOrWhiteSpace(deviceIp))
        {
            throw new ArgumentException("Device IP cannot be null or empty", nameof(deviceIp));
        }

        // Si ya incluye el protocolo, usarla tal como está
        if (deviceIp.StartsWith("http://") || deviceIp.StartsWith("https://"))
        {
            return deviceIp.TrimEnd('/');
        }

        // Si incluye puerto, usar la IP:puerto tal como está
        if (deviceIp.Contains(':'))
        {
            return $"http://{deviceIp}";
        }

        // Si solo es la IP, agregar el puerto por defecto
        return $"http://{deviceIp}:{DefaultPort}";
    }

    [HttpPost]
    [Route("zebra-print")]
    [SwaggerOperation(
        Summary = "Enviar impresión Zebra al Listener",
        Description = "Envía comandos ZPL a una impresora Zebra mediante el ZPL Listener Service",
        OperationId = "SendZebraPrint",
        Tags = ["PrintTest"]
    )]
    public async Task<IActionResult> SendZebraPrint([FromBody] ZebraPrintRequest request)
    {
        try
        {
            _logger.LogInformation(
                "Received print request for printer: {PrinterName} on device: {DeviceIp}",
                request.PrinterName,
                request.DeviceIp
            );

            // Validar entrada
            if (string.IsNullOrWhiteSpace(request.DeviceIp))
            {
                return BadRequest(new { Error = "DeviceIp is required" });
            }

            if (string.IsNullOrWhiteSpace(request.PrinterName))
            {
                return BadRequest(new { Error = "PrinterName is required" });
            }

            if (string.IsNullOrWhiteSpace(request.ZplCode))
            {
                return BadRequest(new { Error = "ZplCode is required" });
            }

            // Limpiar y validar el código ZPL
            var cleanedZplCode = CleanZplCode(request.ZplCode);

            // Construir URL del ZPL Listener
            var zplListenerUrl = BuildZplListenerUrl(request.DeviceIp);

            // Preparar solicitud para el ZPL Listener
            var printRequest = new { printerName = request.PrinterName, zplCode = cleanedZplCode };

            var jsonContent = JsonSerializer.Serialize(printRequest);
            var httpContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Llamar al ZPL Listener
            _logger.LogInformation("Sending request to ZPL Listener at {Url}", $"{zplListenerUrl}/print");

            var response = await _httpClient.PostAsync($"{zplListenerUrl}/print", httpContent);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Print job sent successfully via ZPL Listener");

                // Deserializar respuesta del listener
                var listenerResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                return Ok(
                    new
                    {
                        Success = true,
                        Message = "Print job sent successfully via ZPL Listener",
                        request.DeviceIp,
                        ZplListenerUrl = zplListenerUrl,
                        request.PrinterName,
                        ListenerResponse = listenerResponse,
                    }
                );
            }

            _logger.LogError(
                "ZPL Listener returned error: {StatusCode} - {Content}",
                response.StatusCode,
                responseContent
            );

            return StatusCode(
                (int)response.StatusCode,
                new
                {
                    Error = "Failed to send print job via ZPL Listener",
                    response.StatusCode,
                    ZplListenerUrl = zplListenerUrl,
                    Details = responseContent,
                }
            );
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error calling ZPL Listener");
            return StatusCode(503, new { Error = "Unable to connect to ZPL Listener Service", Details = ex.Message });
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout calling ZPL Listener");
            return StatusCode(408, new { Error = "Timeout calling ZPL Listener Service", Details = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in print test controller");
            return StatusCode(500, new { Error = "Internal server error", Details = ex.Message });
        }
    }

    [HttpGet]
    [Route("printers")]
    [SwaggerOperation(
        Summary = "Obtener impresoras disponibles",
        Description = "Obtiene la lista de impresoras disponibles desde el ZPL Listener Service",
        OperationId = "GetAvailablePrinters",
        Tags = ["PrintTest"]
    )]
    public async Task<IActionResult> GetAvailablePrinters([FromQuery] string DeviceIp)
    {
        try
        {
            _logger.LogInformation("Requesting available printers from ZPL Listener on device: {DeviceIp}", DeviceIp);

            // Validar entrada
            if (string.IsNullOrWhiteSpace(DeviceIp))
            {
                return BadRequest(new { Error = "DeviceIp is required" });
            }

            // Construir URL del ZPL Listener
            var zplListenerUrl = BuildZplListenerUrl(DeviceIp);

            var response = await _httpClient.GetAsync($"{zplListenerUrl}/printers");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully retrieved printers from ZPL Listener");

                var printersResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                return Ok(
                    new
                    {
                        DeviceIp,
                        ZplListenerUrl = zplListenerUrl,
                        Printers = printersResponse,
                    }
                );
            }

            _logger.LogError(
                "ZPL Listener returned error: {StatusCode} - {Content}",
                response.StatusCode,
                responseContent
            );

            return StatusCode(
                (int)response.StatusCode,
                new
                {
                    Error = "Failed to get printers from ZPL Listener",
                    response.StatusCode,
                    ZplListenerUrl = zplListenerUrl,
                    Details = responseContent,
                }
            );
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error calling ZPL Listener");
            return StatusCode(503, new { Error = "Unable to connect to ZPL Listener Service", Details = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting printers");
            return StatusCode(500, new { Error = "Internal server error", Details = ex.Message });
        }
    }

    [HttpGet]
    [Route("health")]
    [SwaggerOperation(
        Summary = "Verificar estado del ZPL Listener",
        Description = "Verifica si el ZPL Listener Service está activo y respondiendo",
        OperationId = "CheckZplListenerHealth",
        Tags = ["PrintTest"]
    )]
    public async Task<IActionResult> CheckZplListenerHealth([FromQuery] string DeviceIp)
    {
        try
        {
            _logger.LogInformation("Checking ZPL Listener health on device: {DeviceIp}", DeviceIp);

            // Validar entrada
            if (string.IsNullOrWhiteSpace(DeviceIp))
            {
                return BadRequest(new { Error = "DeviceIp is required" });
            }

            // Construir URL del ZPL Listener
            var zplListenerUrl = BuildZplListenerUrl(DeviceIp);

            var response = await _httpClient.GetAsync($"{zplListenerUrl}/");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("ZPL Listener is healthy");

                var healthResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                return Ok(
                    new
                    {
                        ZplListenerStatus = "Healthy",
                        DeviceIp,
                        ZplListenerUrl = zplListenerUrl,
                        Response = healthResponse,
                    }
                );
            }

            _logger.LogWarning("ZPL Listener health check failed: {StatusCode}", response.StatusCode);
            return StatusCode(
                (int)response.StatusCode,
                new
                {
                    ZplListenerStatus = "Unhealthy",
                    response.StatusCode,
                    ZplListenerUrl = zplListenerUrl,
                    Details = responseContent,
                }
            );
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error checking ZPL Listener health");
            return StatusCode(
                503,
                new
                {
                    ZplListenerStatus = "Unreachable",
                    Error = "Unable to connect to ZPL Listener Service",
                    Details = ex.Message,
                }
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error checking ZPL Listener health");
            return StatusCode(500, new { Error = "Internal server error", Details = ex.Message });
        }
    }

    [HttpPost]
    [Route("validate-zpl")]
    [SwaggerOperation(
        Summary = "Validar y limpiar código ZPL",
        Description = "Valida y limpia el código ZPL para asegurar que es válido en JSON",
        OperationId = "ValidateZplCode",
        Tags = ["PrintTest"]
    )]
    public IActionResult ValidateZplCode([FromBody] ZplValidationRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request?.ZplCode))
            {
                return BadRequest(new { Error = "ZplCode is required" });
            }

            var originalCode = request.ZplCode;
            var cleanedCode = CleanZplCode(originalCode);

            // Información de diagnóstico
            var diagnostics = new
            {
                HasNewlines = originalCode.Contains('\n') || originalCode.Contains('\r'),
                HasTabs = originalCode.Contains('\t'),
                HasQuotes = originalCode.Contains('"'),
                HasBackslashes = originalCode.Contains('\\'),
                OriginalLength = originalCode.Length,
                CleanedLength = cleanedCode.Length,
                ControlCharacters = originalCode
                    .Where(c => c < 32 && c != '\t' && c != '\n' && c != '\r')
                    .Select(c => $"0x{(int)c:X2}")
                    .ToArray(),
            };

            return Ok(
                new
                {
                    Success = true,
                    Message = "ZPL code validated and cleaned",
                    Original = originalCode,
                    Cleaned = cleanedCode,
                    Diagnostics = diagnostics,
                    ExampleRequest = new
                    {
                        DeviceIp = "*************",
                        PrinterName = "ZDesigner ZM400 200 dpi (ZPL)",
                        ZplCode = cleanedCode,
                    },
                }
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating ZPL code");
            return StatusCode(500, new { Error = "Error validating ZPL code", Details = ex.Message });
        }
    }
}

public record ZebraPrintRequest(string DeviceIp, string PrinterName, string ZplCode);

public record ZplValidationRequest(string ZplCode);
