using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Main.Batchs.Commands.PrintBatch;
using SGP.Aserradero.Application.Modules.Main.Batchs.Queries.GetAll;
using SGP.Aserradero.Application.Modules.Main.Batchs.Queries.GetBatchDetails;

namespace SGP_Aserradero.Controllers;

/// <summary>
/// Controlador para la gestión integral de lotes en el sistema de aserradero.
/// </summary>
/// <remarks>
/// Este controlador maneja todas las operaciones relacionadas con los lotes de producción,
/// incluyendo consultas, detalles específicos e impresión de etiquetas.
/// Los lotes son unidades fundamentales de trazabilidad que permiten seguir el material
/// desde su ingreso hasta su procesamiento final. Cada lote contiene información crítica
/// como origen, características del material, procesos aplicados y estado actual.
/// La gestión adecuada de lotes es esencial para el control de calidad, trazabilidad
/// y cumplimiento de normativas en la industria maderera.
/// </remarks>
[ApiController]
[Route("[controller]")]
[Tags("General | Lotes")]
public class BatchController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Obtiene todos los lotes del sistema con paginación y filtros opcionales
    /// </summary>
    /// <param name="query">Parámetros de consulta que incluyen criterios de paginación, filtrado y ordenamiento</param>
    /// <returns>Lista paginada de lotes con información básica de cada uno</returns>
    /// <response code="200">Devuelve la lista paginada de lotes obtenidos correctamente</response>
    /// <response code="400">Si los parámetros de consulta son inválidos</response>
    /// <response code="404">Si no se encuentran lotes que coincidan con los criterios</response>
    /// <response code="500">Si ocurrió un error interno del servidor</response>
    /// <remarks>
    /// Este endpoint proporciona una vista general de todos los lotes en el sistema,
    /// permitiendo filtrar por diferentes criterios como estado, fecha de creación,
    /// tipo de material, etc. La paginación mejora el rendimiento al manejar grandes
    /// volúmenes de datos. Los resultados incluyen información esencial para identificar
    /// y seleccionar lotes específicos para operaciones posteriores.
    /// Es útil para interfaces de listado, reportes y selección de lotes para procesamiento.
    /// </remarks>
    [HttpGet]
    [ProducesResponseType(typeof(Result<PagedResult<GetAllBatchQueryResponse>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<PagedResult<GetAllBatchQueryResponse>>> GetAllBatches(
        [FromQuery] GetAllBatchQuery query
    )
    {
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Obtiene información detallada de un lote específico del sistema
    /// </summary>
    /// <param name="query">Consulta que contiene los criterios para identificar el lote específico</param>
    /// <returns>Información completa y detallada del lote solicitado</returns>
    /// <response code="200">Devuelve los detalles completos del lote obtenidos correctamente</response>
    /// <response code="400">Si los parámetros de consulta son inválidos o insuficientes</response>
    /// <response code="404">Si el lote especificado no se encuentra en el sistema</response>
    /// <response code="500">Si ocurrió un error interno del servidor</response>
    /// <remarks>
    /// Este endpoint proporciona información exhaustiva sobre un lote específico,
    /// incluyendo detalles como historial de movimientos, procesos aplicados,
    /// características del material, ubicación actual, estado de calidad y trazabilidad completa.
    /// La información detallada es fundamental para auditorías, control de calidad,
    /// resolución de problemas y toma de decisiones operativas.
    /// Es especialmente útil para interfaces de detalle, reportes específicos
    /// y análisis de trazabilidad de productos.
    /// </remarks>
    [HttpGet]
    [Route("details")]
    [ProducesResponseType(typeof(Result<GetBatchDetailsQueryResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<GetBatchDetailsQueryResponse>> GetBatchDetails(
        [FromQuery] GetBatchDetailsQuery query
    )
    {
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Genera e imprime etiquetas o documentos asociados a un lote específico
    /// </summary>
    /// <param name="command">Comando que contiene la información necesaria para la impresión del lote</param>
    /// <returns>Resultado de la operación de impresión</returns>
    /// <response code="200">La impresión se ejecutó correctamente</response>
    /// <response code="400">Si los datos del comando son inválidos o insuficientes</response>
    /// <response code="404">Si el lote especificado no se encuentra</response>
    /// <response code="500">Si ocurrió un error interno del servidor o en el sistema de impresión</response>
    /// <remarks>
    /// Este endpoint permite generar e imprimir documentos asociados a un lote,
    /// como etiquetas de identificación, códigos de barras, reportes de calidad
    /// o documentos de trazabilidad. La impresión es fundamental para el etiquetado
    /// físico de productos y la documentación requerida para el control de inventarios.
    /// El sistema valida la existencia del lote y la disponibilidad de los datos
    /// necesarios antes de proceder con la impresión.
    /// Esta operación puede integrarse con sistemas de impresión industrial
    /// y generar diferentes tipos de documentos según la configuración.
    /// </remarks>
    [HttpPost]
    [Route("print")]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<bool>> PrintBatch([FromBody] PrintBatchCommand command)
    {
        var result = await mediator.Send(command);

        return StatusCode(result.StatusCode, result);
    }
}
