using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Features.Statuses.Commands.Create;
using SGP.Aserradero.Application.Modules.Features.Statuses.Commands.Delete;
using SGP.Aserradero.Application.Modules.Features.Statuses.Commands.Update;
using SGP.Aserradero.Application.Modules.Features.Statuses.Queries.GetAll;
using SGP.Aserradero.Application.Modules.Features.Statuses.Queries.GetById;
using SGP.Aserradero.Domain.Entities.Features.Statuses;

namespace SGP_Aserradero.Controllers.Features;

/// <summary>
/// Controlador para la gestión de estados del sistema de aserradero.
/// Proporciona operaciones CRUD completas para el mantenimiento de estados.
/// </summary>
/// <remarks>
/// Este controlador maneja todas las operaciones relacionadas con los estados,
/// incluyendo consultas, búsquedas por ID, creación, actualización y eliminación.
/// Utiliza el patrón CQRS a través de MediatR para separar comandos y consultas.
/// </remarks>
[ApiController]
[Route("features/[controller]")]
[Tags("Características | Estados")]
public class StatusController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Crea un nuevo estado en el sistema.
    /// </summary>
    /// <param name="command">Comando que contiene los datos necesarios para crear el nuevo estado.</param>
    /// <returns>
    /// El ID del estado recién creado.
    /// </returns>
    /// <response code="201">El estado fue creado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o incompletos.</response>
    /// <response code="409">Ya existe un estado con el mismo nombre en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite registrar un nuevo estado en el sistema.
    /// Se validan todos los campos requeridos incluyendo nombre, descripción y orden de secuencia.
    /// Se verifica que no exista otro estado con el mismo nombre para evitar duplicados.
    /// La respuesta incluye el ID del estado creado para futuras referencias.
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(typeof(Result<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<int>>> Create([FromBody] CreateStatusCommand command)
    {
        var result = await mediator.Send(command);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Actualiza un estado existente en el sistema.
    /// </summary>
    /// <param name="id">Identificador único del estado a actualizar.</param>
    /// <param name="command">Comando que contiene los nuevos datos para actualizar el estado.</param>
    /// <returns>
    /// Resultado booleano indicando si la actualización fue exitosa.
    /// </returns>
    /// <response code="200">El estado fue actualizado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o el ID de la URL no coincide con el del comando.</response>
    /// <response code="404">No se encontró un estado con el ID especificado.</response>
    /// <response code="409">Ya existe otro estado con el mismo nombre en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite modificar los datos de un estado existente.
    /// Se requiere que el ID de la URL coincida con el ID del comando para mayor seguridad.
    /// Se validan todos los campos incluyendo nombre, descripción y orden de secuencia.
    /// Se verifica que no exista otro estado con el mismo nombre (excluyendo el estado que se está actualizando).
    /// </remarks>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<bool>>> Update(int id, [FromBody] UpdateStatusCommand command)
    {
        if (id != command.StatusId)
        {
            return BadRequest();
        }

        var result = await mediator.Send(command);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Elimina un estado existente del sistema.
    /// </summary>
    /// <param name="id">Identificador único del estado a eliminar.</param>
    /// <returns>
    /// Resultado booleano indicando si la eliminación fue exitosa.
    /// </returns>
    /// <response code="200">El estado fue eliminado exitosamente.</response>
    /// <response code="400">La solicitud es inválida o contiene datos incorrectos.</response>
    /// <response code="404">No se encontró un estado con el ID especificado.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite eliminar permanentemente un estado del sistema.
    /// La operación es irreversible, por lo que se debe usar con precaución.
    /// Se verifica la existencia del estado antes de proceder con la eliminación.
    /// IMPORTANTE: Si el estado está siendo utilizado en procesos activos, órdenes
    /// de producción o tiene historial de transiciones asociadas, la eliminación
    /// podría fallar para mantener la integridad del flujo de trabajo y la trazabilidad.
    /// </remarks>
    [HttpDelete("{id}")]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<bool>>> Delete(int id)
    {
        var command = new DeleteStatusCommand { StatusId = id };
        var result = await mediator.Send(command);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Obtiene todos los estados disponibles en el sistema.
    /// </summary>
    /// <returns>
    /// Una lista completa de todos los estados registrados en el sistema.
    /// </returns>
    /// <response code="200">Devuelve la lista de estados exitosamente.</response>
    /// <response code="404">No se encontraron estados en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite obtener todos los estados registrados en el sistema.
    /// Los estados representan las diferentes fases del proceso productivo y se
    /// devuelven ordenados según su secuencia lógica en el flujo de trabajo.
    /// </remarks>
    [HttpGet]
    [ProducesResponseType(typeof(Result<IEnumerable<Status>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<IEnumerable<Status>>>> GetAll()
    {
        var query = new GetAllStatusesQuery();
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Obtiene un estado específico por su identificador único.
    /// </summary>
    /// <param name="id">Identificador único del estado a consultar.</param>
    /// <returns>
    /// La información completa del estado solicitado.
    /// </returns>
    /// <response code="200">Devuelve el estado encontrado exitosamente.</response>
    /// <response code="404">No se encontró un estado con el ID especificado.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite obtener los detalles completos de un estado específico
    /// utilizando su identificador único.
    /// </remarks>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Result<Status>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<Status>>> GetById(int id)
    {
        var query = new GetStatusByIdQuery { StatusId = id };
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }
}
