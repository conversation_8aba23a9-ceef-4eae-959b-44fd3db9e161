using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Movements.DTOs;
using SGP.Aserradero.Application.Modules.Movements.Queries;

namespace SGP_Aserradero.Controllers.Movements;

/// <summary>
/// Controlador para la gestión de trazabilidad de movimientos en el sistema de aserradero.
/// Proporciona operaciones especializadas para el seguimiento y consulta de movimientos por diferentes criterios.
/// </summary>
/// <remarks>
/// Este controlador maneja todas las operaciones relacionadas con la trazabilidad de movimientos,
/// permitiendo el seguimiento completo de lotes y documentos a través de todo el proceso productivo.
/// La trazabilidad es fundamental para el control de calidad, auditorías, cumplimiento normativo
/// y la identificación rápida de problemas en la cadena de producción.
/// Utiliza el patrón CQRS a través de MediatR para consultas optimizadas,
/// garantizando respuestas rápidas y precisas para el seguimiento de materiales.
/// </remarks>
[ApiController]
[Route("movement/[controller]")]
[Tags("Movimientos | Trazabilidad")]
public class MovementTraceabilityController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Busca movimientos para trazabilidad utilizando criterios flexibles de búsqueda.
    /// </summary>
    /// <param name="request">
    /// Objeto de consulta que contiene los criterios de búsqueda.
    /// Puede incluir DocumentId para búsqueda por documento o BatchCodes para búsqueda por lotes.
    /// </param>
    /// <returns>
    /// Lista completa de movimientos con información detallada de trazabilidad para los criterios especificados.
    /// </returns>
    /// <response code="200">Los movimientos fueron encontrados y devueltos exitosamente.</response>
    /// <response code="400">Los criterios de búsqueda proporcionados son inválidos o incompletos.</response>
    /// <response code="404">No se encontraron movimientos que coincidan con los criterios especificados.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint proporciona una interfaz flexible para consultar movimientos de trazabilidad.
    /// Permite búsquedas por documento específico o por múltiples códigos de lote simultáneamente.
    /// La información devuelta incluye detalles completos de cada movimiento, fechas, estados,
    /// ubicaciones, operadores responsables y cualquier observación relevante.
    /// Es especialmente útil para investigaciones de calidad y auditorías de proceso.
    /// </remarks>
    [HttpPost("search")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<MovementTraceabilityDto>>> SearchMovementsTraceability(
        [FromBody] GetMovementTraceabilityQuery request
    )
    {
        var result = await mediator.Send(request);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Busca todos los movimientos asociados a un documento específico para trazabilidad completa.
    /// </summary>
    /// <param name="documentId">Identificador único del documento a consultar para trazabilidad.</param>
    /// <returns>
    /// Lista completa de movimientos asociados al documento especificado con información detallada de trazabilidad.
    /// </returns>
    /// <response code="200">Los movimientos del documento fueron encontrados y devueltos exitosamente.</response>
    /// <response code="400">El identificador de documento proporcionado es inválido.</response>
    /// <response code="404">No se encontró el documento especificado o no tiene movimientos asociados.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite consultar todos los movimientos relacionados con un documento específico,
    /// proporcionando una vista completa de la trazabilidad desde el origen hasta el destino final.
    /// La información incluye cronología de movimientos, cambios de estado, ubicaciones,
    /// operadores involucrados y cualquier incidencia registrada durante el proceso.
    /// Es fundamental para auditorías documentales y seguimiento de órdenes de producción.
    /// </remarks>
    [HttpGet("by-document/{documentId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<MovementTraceabilityDto>>> SearchByDocument(int documentId)
    {
        var query = new GetMovementTraceabilityQuery { DocumentId = documentId };
        var result = await mediator.Send(query);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Busca movimientos para trazabilidad utilizando múltiples códigos de lote como criterio de búsqueda.
    /// </summary>
    /// <param name="batchCodes">
    /// Cadena con códigos de lote separados por comas para consultar su trazabilidad.
    /// Ejemplo: "L20250714A,L20250714B,L20250715C"
    /// </param>
    /// <returns>
    /// Lista completa de movimientos asociados a los lotes especificados con información detallada de trazabilidad.
    /// </returns>
    /// <response code="200">Los movimientos de los lotes fueron encontrados y devueltos exitosamente.</response>
    /// <response code="400">Los códigos de lote proporcionados son inválidos, están mal formateados o faltantes.</response>
    /// <response code="404">No se encontraron movimientos para los códigos de lote especificados.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite consultar la trazabilidad de múltiples lotes simultáneamente,
    /// facilitando el seguimiento de grupos de producción o investigaciones de calidad extensas.
    /// Los códigos deben estar separados por comas y se eliminan automáticamente espacios en blanco.
    /// La respuesta incluye el historial completo de cada lote: ubicaciones, fechas, operadores,
    /// transformaciones aplicadas, controles de calidad y estados a lo largo del proceso.
    /// Es especialmente útil para análisis de lotes relacionados o investigaciones de incidencias.
    /// </remarks>
    [HttpGet("by-batches")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<MovementTraceabilityDto>>> SearchByBatches([FromQuery] string batchCodes)
    {
        if (string.IsNullOrWhiteSpace(batchCodes))
        {
            return BadRequest("Los códigos de lote son requeridos");
        }

        var codes = batchCodes
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(c => c.Trim())
            .Where(c => !string.IsNullOrWhiteSpace(c))
            .ToList();

        if (!codes.Any())
        {
            return BadRequest("Debe proporcionar al menos un código de lote válido");
        }

        var query = new GetMovementTraceabilityQuery { BatchCodes = codes };
        var result = await mediator.Send(query);
        return StatusCode(result.StatusCode, result);
    }
}
