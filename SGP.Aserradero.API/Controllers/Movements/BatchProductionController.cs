using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Main.Batchs.Queries.GetByCode;
using SGP.Aserradero.Application.Modules.Movements.Productions;

namespace SGP_Aserradero.Controllers.Movements;

/// <summary>
/// Controlador para la gestión de producción por lotes del sistema de aserradero.
/// Proporciona operaciones para consultar y crear registros de producción asociados a lotes específicos.
/// </summary>
/// <remarks>
/// Este controlador maneja las operaciones relacionadas con la producción por lotes,
/// incluyendo consultas por código de lote y creación de nuevos registros de producción.
/// Utiliza el patrón CQRS a través de MediatR para separar comandos y consultas.
/// </remarks>
[Route("movements/[controller]")]
[ApiController]
[Tags("Movimientos | Producción por Lotes")]
public class BatchProductionController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Obtiene información de un lote específico por su código.
    /// </summary>
    /// <param name="batch">Código único del lote a consultar.</param>
    /// <returns>
    /// La información completa del lote solicitado incluyendo datos de producción.
    /// </returns>
    /// <response code="200">Devuelve la información del lote encontrado exitosamente.</response>
    /// <response code="404">No se encontró un lote con el código especificado.</response>
    /// <response code="400">El código de lote proporcionado es inválido o está vacío.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite obtener los detalles completos de un lote específico
    /// utilizando su código único. Los lotes son unidades fundamentales de producción
    /// que agrupan productos con características y procesos similares.
    /// </remarks>
    [HttpGet("byBatch")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetByBatch([FromQuery] string batch)
    {
        var query = new GetBatchByCodeQuery { BatchCode = batch };
        var result = await mediator.Send(query);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Crea un nuevo registro de producción por lotes en el sistema.
    /// </summary>
    /// <param name="command">Comando que contiene los datos necesarios para crear el registro de producción por lotes.</param>
    /// <returns>
    /// Resultado indicando si la creación del registro de producción fue exitosa.
    /// </returns>
    /// <response code="201">El registro de producción por lotes fue creado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o incompletos.</response>
    /// <response code="409">Ya existe un registro de producción para el lote especificado.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite registrar nueva información de producción asociada a un lote específico.
    /// Se validan todos los campos requeridos incluyendo código de lote, datos de producción,
    /// cantidades, fechas y operadores responsables. Los registros de producción por lotes
    /// son fundamentales para el seguimiento y trazabilidad de los procesos productivos.
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Create([FromBody] BatchProductionCommand command)
    {
        var result = await mediator.Send(command);
        return StatusCode(result.StatusCode, result);
    }
}
