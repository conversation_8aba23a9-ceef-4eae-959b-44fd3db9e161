using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Material.MaterialLarges.Commands.Create;
using SGP.Aserradero.Application.Modules.Material.MaterialLarges.Commands.Delete;
using SGP.Aserradero.Application.Modules.Material.MaterialLarges.Commands.Update;
using SGP.Aserradero.Application.Modules.Material.MaterialLarges.Queries.GetAll;
using SGP.Aserradero.Application.Modules.Material.MaterialLarges.Queries.Search;
using SGP.Aserradero.Domain.Entities.Material.MaterialLarges;

namespace SGP_Aserradero.Controllers.Material;

/// <summary>
/// Controlador para la gestión de largos de material del sistema de aserradero.
/// Proporciona operaciones CRUD completas y funcionalidades de búsqueda para largos de materiales.
/// </summary>
/// <remarks>
/// Este controlador maneja todas las operaciones relacionadas con los largos de material,
/// incluyendo consultas paginadas, búsquedas por términos, creación, actualización y eliminación de registros.
/// Utiliza el patrón CQRS a través de MediatR para separar comandos y consultas.
/// </remarks>
[ApiController]
[Route("[controller]")]
[Tags("Material | Largo")]
public class MaterialLargeController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Obtiene todos los largos de material con paginación.
    /// </summary>
    /// <param name="page">Número de página (opcional, por defecto 1).</param>
    /// <param name="pageSize">Tamaño de página (opcional, por defecto 10).</param>
    /// <returns>
    /// Una lista paginada de largos de material del sistema.
    /// </returns>
    /// <response code="200">Devuelve la lista paginada de largos de material exitosamente.</response>
    /// <response code="404">No se encontraron largos de material en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite obtener largos de material con paginación.
    /// </remarks>
    [HttpGet]
    [ProducesResponseType(typeof(Result<PagedResult<MaterialLarge>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<PagedResult<MaterialLarge>>>> GetAll(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10
    )
    {
        var query = new GetAllMaterialLargeQuery { Page = page, PageSize = pageSize };
        var result = await mediator.Send(query);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Busca largos de material por nombre o descripción.
    /// </summary>
    /// <param name="searchTerm">Término de búsqueda para filtrar por nombre o descripción (opcional).</param>
    /// <returns>
    /// Una lista de largos de material que coinciden con los criterios de búsqueda.
    /// </returns>
    /// <response code="200">Devuelve la lista de largos de material que coinciden con la búsqueda.</response>
    /// <response code="404">No se encontraron largos de material que coincidan con los criterios de búsqueda.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite buscar largos de material utilizando un término de búsqueda.
    /// La búsqueda se realiza en los campos de nombre y descripción de los largos.
    /// </remarks>
    [HttpGet]
    [Route("search")]
    [ProducesResponseType(typeof(Result<List<MaterialLarge>>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<List<MaterialLarge>>>> Search([FromQuery] string searchTerm = "")
    {
        var query = new SearchMaterialLargeQuery { SearchTerm = searchTerm };
        var result = await mediator.Send(query);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Crea un nuevo largo de material en el sistema.
    /// </summary>
    /// <param name="command">Comando que contiene los datos necesarios para crear el nuevo largo de material.</param>
    /// <returns>
    /// Los datos del largo de material creado exitosamente.
    /// </returns>
    /// <response code="201">El largo de material fue creado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o incompletos.</response>
    /// <response code="409">Ya existe un largo de material con el mismo valor en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite registrar un nuevo largo de material en el sistema.
    /// Se validan todos los campos requeridos incluyendo valor y descripción.
    /// Se verifica que no exista otro largo con el mismo valor para evitar duplicados.
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(typeof(Result<MaterialLarge>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<MaterialLarge>>> Create([FromBody] CreateMaterialLargeCommand command)
    {
        var result = await mediator.Send(command);
        return CreatedAtAction(nameof(GetAll), result);
    }

    /// <summary>
    /// Actualiza un largo de material existente en el sistema.
    /// </summary>
    /// <param name="id">Identificador único del largo de material a actualizar.</param>
    /// <param name="command">Comando que contiene los nuevos datos para actualizar el largo de material.</param>
    /// <returns>
    /// Resultado booleano indicando si la actualización fue exitosa.
    /// </returns>
    /// <response code="200">El largo de material fue actualizado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o el ID de la URL no coincide con el del comando.</response>
    /// <response code="404">No se encontró un largo de material con el ID especificado.</response>
    /// <response code="409">Ya existe otro largo de material con el mismo valor en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite modificar los datos de un largo de material existente.
    /// Se requiere que el ID de la URL coincida con el ID del comando para mayor seguridad.
    /// Se validan todos los campos incluyendo valor y descripción.
    /// Se verifica que no exista otro largo con el mismo valor (excluyendo el que se está actualizando).
    /// </remarks>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<bool>>> Update(int id, [FromBody] UpdateMaterialLargeCommand command)
    {
        if (id != command.Id)
        {
            return BadRequest();
        }

        var result = await mediator.Send(command);
        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Elimina un largo de material existente del sistema.
    /// </summary>
    /// <param name="id">Identificador único del largo de material a eliminar.</param>
    /// <returns>
    /// Resultado booleano indicando si la eliminación fue exitosa.
    /// </returns>
    /// <response code="200">El largo de material fue eliminado exitosamente.</response>
    /// <response code="400">La solicitud es inválida o contiene datos incorrectos.</response>
    /// <response code="404">No se encontró un largo de material con el ID especificado.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite eliminar permanentemente un largo de material del sistema.
    /// La operación es irreversible, por lo que se debe usar con precaución.
    /// Se verifica la existencia del largo antes de proceder con la eliminación.
    /// Si el largo está siendo utilizado en productos activos o tiene dependencias,
    /// la eliminación podría fallar para mantener la integridad referencial del sistema.
    /// </remarks>
    [HttpDelete]
    [Route("{id}")]
    [ProducesResponseType(typeof(Result<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Result<bool>>> Delete(int id)
    {
        var result = await mediator.Send(new DeleteMaterialLargeCommand { Id = id });
        return StatusCode(result.StatusCode, result);
    }
}
