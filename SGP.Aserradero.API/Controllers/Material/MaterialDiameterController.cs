using Leonera_API.Common.Core.Domain;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Commands.Create;
using SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Commands.Delete;
using SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Commands.Update;
using SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Queries.GetAll;
using SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Queries.Search;
using SGP.Aserradero.Domain.Entities.Material.MaterialDiameters;

namespace SGP_Aserradero.Controllers.Material;

/// <summary>
/// Controlador para la gestión de diámetros de material del sistema de aserradero.
/// Proporciona operaciones CRUD completas y funcionalidades de búsqueda para diámetros de materiales.
/// </summary>
/// <remarks>
/// Este controlador maneja todas las operaciones relacionadas con los diámetros de material,
/// incluyendo consultas paginadas, búsquedas por términos, creación, actualización y eliminación.
/// Los diámetros de material son medidas estándar utilizadas para clasificar
/// y categorizar las dimensiones de los productos madereros según su grosor o diámetro.
/// Estas medidas son fundamentales para el control de calidad y la trazabilidad del producto.
/// Utiliza el patrón CQRS a través de MediatR para separar comandos y consultas.
/// </remarks>
[ApiController]
[Route("[controller]")]
[Tags("Material | Diámetro")]
public class MaterialDiameterController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Obtiene todos los diámetros de material con paginación.
    /// </summary>
    /// <param name="page">Número de página (por defecto 1).</param>
    /// <param name="pageSize">Tamaño de página (por defecto 10).</param>
    /// <returns>
    /// Una lista paginada de todos los diámetros de material registrados en el sistema.
    /// </returns>
    /// <response code="200">Devuelve la lista paginada de diámetros de material exitosamente.</response>
    /// <response code="404">No se encontraron diámetros de material en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite obtener diámetros de material con paginación para optimizar el rendimiento.
    /// Los diámetros son medidas estándar utilizadas para clasificar productos madereros
    /// según sus dimensiones de grosor. La paginación facilita el manejo de grandes volúmenes de datos.
    /// </remarks>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<MaterialDiameter>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<PagedResult<MaterialDiameter>>> GetAll(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10
    )
    {
        var query = new GetAllMaterialDiameterQuery { Page = page, PageSize = pageSize };
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Busca diámetros de material por nombre o descripción.
    /// </summary>
    /// <param name="searchTerm">Término de búsqueda para filtrar por nombre o descripción (opcional).</param>
    /// <returns>
    /// Una lista de diámetros de material que coinciden con los criterios de búsqueda.
    /// </returns>
    /// <response code="200">Devuelve la lista de diámetros de material que coinciden con la búsqueda.</response>
    /// <response code="404">No se encontraron diámetros de material que coincidan con los criterios de búsqueda.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite buscar diámetros de material utilizando un término de búsqueda.
    /// La búsqueda se realiza en los campos de nombre y descripción de los diámetros.
    /// Si no se proporciona un término de búsqueda, se devuelven todos los registros disponibles.
    /// </remarks>
    [HttpGet]
    [Route("search")]
    [ProducesResponseType(typeof(List<MaterialDiameter>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<MaterialDiameter>>> Search([FromQuery] string searchTerm = "")
    {
        var query = new SearchMaterialDiameterQuery { SearchTerm = searchTerm };
        var result = await mediator.Send(query);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Crea un nuevo diámetro de material en el sistema.
    /// </summary>
    /// <param name="command">Comando que contiene los datos necesarios para crear el nuevo diámetro de material.</param>
    /// <returns>
    /// Resultado booleano indicando si la creación fue exitosa.
    /// </returns>
    /// <response code="201">El diámetro de material fue creado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o incompletos.</response>
    /// <response code="409">Ya existe un diámetro de material con el mismo valor en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite registrar un nuevo diámetro de material en el sistema.
    /// Se validan todos los campos requeridos incluyendo valor, unidad de medida y descripción.
    /// Se verifica que no exista otro diámetro con el mismo valor para evitar duplicados.
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(typeof(bool), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<bool>> Create([FromBody] CreateMaterialDiameterCommand command)
    {
        var result = await mediator.Send(command);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Actualiza un diámetro de material existente en el sistema.
    /// </summary>
    /// <param name="id">Identificador único del diámetro de material a actualizar.</param>
    /// <param name="command">Comando que contiene los nuevos datos para actualizar el diámetro de material.</param>
    /// <returns>
    /// Resultado booleano indicando si la actualización fue exitosa.
    /// </returns>
    /// <response code="200">El diámetro de material fue actualizado exitosamente.</response>
    /// <response code="400">Los datos proporcionados son inválidos o el ID de la ruta no coincide.</response>
    /// <response code="404">No se encontró un diámetro de material con el ID especificado.</response>
    /// <response code="409">Ya existe otro diámetro de material con el mismo valor en el sistema.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite modificar los datos de un diámetro de material existente.
    /// El ID de la ruta se utiliza para identificar el registro y se asigna automáticamente al comando.
    /// Se validan todos los campos incluyendo valor, unidad de medida y descripción.
    /// Se verifica que no exista otro diámetro con el mismo valor (excluyendo el que se está actualizando).
    /// </remarks>
    [HttpPut]
    [Route("{id}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<bool>> Update([FromRoute] int id, [FromBody] UpdateMaterialDiameterCommand command)
    {
        var updateCommand = command with { Id = id };
        var result = await mediator.Send(updateCommand);

        return StatusCode(result.StatusCode, result);
    }

    /// <summary>
    /// Elimina un diámetro de material existente del sistema.
    /// </summary>
    /// <param name="id">Identificador único del diámetro de material a eliminar.</param>
    /// <returns>
    /// Resultado booleano indicando si la eliminación fue exitosa.
    /// </returns>
    /// <response code="200">El diámetro de material fue eliminado exitosamente.</response>
    /// <response code="400">La solicitud es inválida o contiene datos incorrectos.</response>
    /// <response code="404">No se encontró un diámetro de material con el ID especificado.</response>
    /// <response code="500">Error interno del servidor durante el procesamiento de la solicitud.</response>
    /// <remarks>
    /// Este endpoint permite eliminar permanentemente un diámetro de material del sistema.
    /// La operación es irreversible, por lo que se debe usar con precaución.
    /// Se verifica la existencia del diámetro antes de proceder con la eliminación.
    /// Si el diámetro está siendo utilizado en productos o procesos activos,
    /// la eliminación podría fallar para mantener la integridad referencial del sistema.
    /// </remarks>
    [HttpDelete]
    [Route("{id}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<bool>> Delete([FromRoute] int id)
    {
        var command = new DeleteMaterialDiameterCommand { MaterialDiameterId = id };
        var result = await mediator.Send(command);

        return StatusCode(result.StatusCode, result);
    }
}
