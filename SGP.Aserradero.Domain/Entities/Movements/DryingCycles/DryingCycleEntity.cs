using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Main.DryingCameras;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles.Events;

namespace SGP.Aserradero.Domain.Entities.Movements.DryingCycles;

[Table("DryingCycles", Schema = "movements")]
public class DryingCycleEntity : BaseEntity<int>, IAggregateRoot
{
    public string DryingCycleCode { get; set; } = string.Empty;
    public bool EsHt { get; set; }
    public DateTime EntryDate { get; set; } = DateTime.Now;
    public string EntryTime { get; set; } = string.Empty;
    public DateTime? DepartureDate { get; set; }
    public string? DepartureTime { get; set; }
    public required int OrderClassId { get; set; }
    public required int CamerasId { get; set; }

    [ForeignKey("OrderClassId")]
    public virtual OrderClass? OrderClass { get; set; }

    [ForeignKey("CamerasId")]
    public virtual DryingCamera? Camera { get; set; }

    // Colección de detalles (entradas y salidas)
    public virtual ICollection<DryingCycleDetail> Details { get; set; } = [];

    public static DryingCycleEntity CreateEntrance(
        string dryingCycleCode,
        bool esHt,
        DateTime entryDate,
        string entryTime,
        int orderClassId,
        int camerasId
    )
    {
        var dryingCycle = new DryingCycleEntity
        {
            DryingCycleCode = dryingCycleCode,
            EsHt = esHt,
            EntryDate = entryDate,
            EntryTime = entryTime,
            OrderClassId = orderClassId,
            CamerasId = camerasId,
        };
        dryingCycle.QueueDomainEvent(new CreatedDryingCycleEvent { DryingCycle = dryingCycle });
        return dryingCycle;
    }

    public void SetDepartureInfo(DateTime departureDate, string departureTime)
    {
        DepartureDate = departureDate;
        DepartureTime = departureTime;
        QueueDomainEvent(new UpdatedDryingCycleEvent { DryingCycle = this });
    }

    public void AddEntrance(int movementId, int batchId, DateTime processDate, string? notes = null)
    {
        var detail = DryingCycleDetail.CreateEntrance(Id, movementId, batchId, processDate, notes);
        Details.Add(detail);
    }

    public void AddOutput(int movementId, int batchId, DateTime processDate, string? notes = null)
    {
        var detail = DryingCycleDetail.CreateOutput(Id, movementId, batchId, processDate, notes);
        Details.Add(detail);
    }

    public IEnumerable<DryingCycleDetail> GetEntrances()
    {
        return Details.Where(d => d.MovementType == DryingMovementType.Entrance);
    }

    public IEnumerable<DryingCycleDetail> GetOutputs()
    {
        return Details.Where(d => d.MovementType == DryingMovementType.Output);
    }
}
