using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;

namespace SGP.Aserradero.Domain.Entities.Movements.DryingCycles;

[Table("DryingCycleDetails", Schema = "movements")]
public class DryingCycleDetail : BaseEntity<int>, IAggregateRoot
{
    public required int DryingCycleId { get; set; }
    public required int MovementId { get; set; }
    public required int BatchId { get; set; }
    public required DryingMovementType MovementType { get; set; } // Entrada o Salida
    public DateTime ProcessDate { get; set; } = DateTime.Now;
    public string? Notes { get; set; }

    [ForeignKey("DryingCycleId")]
    public virtual DryingCycleEntity? DryingCycle { get; set; }

    [ForeignKey("MovementId")]
    public virtual BaseMovement? Movement { get; set; }

    [ForeignKey("BatchId")]
    public virtual Batch? Batch { get; set; }

    public static DryingCycleDetail CreateEntrance(
        int dryingCycleId,
        int movementId,
        int batchId,
        DateTime processDate,
        string? notes = null
    )
    {
        return new DryingCycleDetail
        {
            DryingCycleId = dryingCycleId,
            MovementId = movementId,
            BatchId = batchId,
            MovementType = DryingMovementType.Entrance,
            ProcessDate = processDate,
            Notes = notes,
        };
    }

    public static DryingCycleDetail CreateOutput(
        int dryingCycleId,
        int movementId,
        int batchId,
        DateTime processDate,
        string? notes = null
    )
    {
        return new DryingCycleDetail
        {
            DryingCycleId = dryingCycleId,
            MovementId = movementId,
            BatchId = batchId,
            MovementType = DryingMovementType.Output,
            ProcessDate = processDate,
            Notes = notes,
        };
    }

    public void UpdateProcessInfo(DateTime processDate, string? notes)
    {
        ProcessDate = processDate;
        Notes = notes;
    }
}

public enum DryingMovementType
{
    Entrance = 1,
    Output = 2,
}
