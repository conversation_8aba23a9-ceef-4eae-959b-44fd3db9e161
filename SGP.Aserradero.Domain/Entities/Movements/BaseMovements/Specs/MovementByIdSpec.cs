using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Specs;

public class MovementByIdSpec : Specification<BaseMovement>
{
    public MovementByIdSpec(int id)
    {
        Query.Where(x => x.Id == id);

        // Include related entities
        Query.Include(x => x.Capturer);
        Query.Include(x => x.Machine);
        Query.Include(x => x.Warehouse);
        Query.Include(x => x.ManufacturingOrder);
        Query.Include(x => x.Document);
        Query.Include(x => x.MovementType);

        Query.AsNoTracking();
    }
}
