using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using SGP.Aserradero.Domain.Entities.Main.Capturers;
using SGP.Aserradero.Domain.Entities.Main.Documents;
using SGP.Aserradero.Domain.Entities.Main.Machines;
using SGP.Aserradero.Domain.Entities.Main.ManufacturingOrders;
using SGP.Aserradero.Domain.Entities.Main.Warehouses;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;

namespace SGP.Aserradero.Domain.Entities.Movements.BaseMovements;

[Table("BaseMovements", Schema = "movements")]
[Index(nameof(Id))]
public class BaseMovement : AuditableEntity<int>, IAggregateRoot
{
    public string? MovementInfo { get; set; }
    public string? Observation { get; set; }
    public int? CapturerId { get; set; }
    public int? DocumentId { get; set; }
    public int? WarehouseId { get; set; }
    public int? ManufacturingOrderId { get; set; }
    public int? MachineId { get; set; }
    public bool? InSap { get; set; }
    public int MovementTypeId { get; set; }

    [ForeignKey("CapturerId")]
    public virtual Capturer? Capturer { get; set; }

    [ForeignKey("DocumentId")]
    public virtual Document? Document { get; set; }

    [ForeignKey("WarehouseId")]
    public virtual Warehouse? Warehouse { get; set; }

    [ForeignKey("ManufacturingOrderId")]
    public virtual ManufacturingOrder? ManufacturingOrder { get; set; }

    [ForeignKey("MachineId")]
    public virtual Machine? Machine { get; set; }

    [ForeignKey("MovementTypeId")]
    public virtual MovementType? MovementType { get; set; }

    public void Update(
        string movementInfo,
        string observation,
        int? capturerId,
        int? documentId,
        int? warehouseId,
        int? manufacturingOrderId,
        int? machineId,
        int modifiedBy
    )
    {
        MovementInfo = movementInfo;
        Observation = observation;
        CapturerId = capturerId ?? null;
        DocumentId = documentId ?? null;
        WarehouseId = warehouseId ?? null;
        ManufacturingOrderId = manufacturingOrderId ?? null;
        MachineId = machineId ?? null;

        LastModifiedBy = modifiedBy;
        LastModified = DateTime.Now;

        QueueDomainEvent(new MovementUpdatedEvent { BaseMovement = this });
    }

    public void Delete(int deletedBy)
    {
        LastModifiedBy = deletedBy;
        LastModified = DateTime.Now;
        Deleted = DateTime.Now;
        DeletedBy = deletedBy;
        QueueDomainEvent(new MovementDeletedEvent { BaseMovement = this });
    }
}
