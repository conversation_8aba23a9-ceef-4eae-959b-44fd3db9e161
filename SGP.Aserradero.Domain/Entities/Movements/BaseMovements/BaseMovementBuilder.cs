namespace SGP.Aserradero.Domain.Entities.Movements.BaseMovements;

/// <summary>
///     Builder para la entidad BaseMovement que permite crear instancias de manera fluida
/// </summary>
public class BaseMovementBuilder
{
    private int? _capturerId;
    private int _createdBy;
    private int? _documentId;
    private int? _machineId;
    private int? _manufacturingOrderId;
    private string? _movementInfo;
    private int _movementTypeId;
    private string? _observation;
    private int? _warehouseId;

    /// <summary>
    ///     Establece la información del movimiento
    /// </summary>
    /// <param name="movementInfo">Información del movimiento</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithMovementInfo(string? movementInfo)
    {
        _movementInfo = movementInfo;
        return this;
    }

    /// <summary>
    ///     Establece la observación del movimiento
    /// </summary>
    /// <param name="observation">Observación del movimiento</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithObservation(string? observation)
    {
        _observation = observation;
        return this;
    }

    /// <summary>
    ///     Establece el ID del capturador
    /// </summary>
    /// <param name="capturerId">ID del capturador</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithCapturerId(int? capturerId)
    {
        _capturerId = capturerId;
        return this;
    }

    /// <summary>
    ///     Establece el ID del documento
    /// </summary>
    /// <param name="documentId">ID del documento</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithDocumentId(int? documentId)
    {
        _documentId = documentId;
        return this;
    }

    /// <summary>
    ///     Establece el ID del almacén
    /// </summary>
    /// <param name="warehouseId">ID del almacén</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithWarehouseId(int? warehouseId)
    {
        _warehouseId = warehouseId;
        return this;
    }

    /// <summary>
    ///     Establece el ID de la orden de manufactura
    /// </summary>
    /// <param name="manufacturingOrderId">ID de la orden de manufactura</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithManufacturingOrderId(int? manufacturingOrderId)
    {
        _manufacturingOrderId = manufacturingOrderId;
        return this;
    }

    /// <summary>
    ///     Establece el ID de la máquina
    /// </summary>
    /// <param name="machineId">ID de la máquina</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithMachineId(int? machineId)
    {
        _machineId = machineId;
        return this;
    }

    /// <summary>
    ///     Establece el ID del tipo de movimiento
    /// </summary>
    /// <param name="movementTypeId">ID del tipo de movimiento</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithMovementTypeId(int movementTypeId)
    {
        _movementTypeId = movementTypeId;
        return this;
    }

    /// <summary>
    ///     Establece el ID del usuario que crea el movimiento
    /// </summary>
    /// <param name="createdBy">ID del usuario creador</param>
    /// <returns>Instancia del builder</returns>
    public BaseMovementBuilder WithCreatedBy(int createdBy)
    {
        _createdBy = createdBy;
        return this;
    }

    /// <summary>
    ///     Valida que los campos requeridos estén presentes
    /// </summary>
    /// <exception cref="InvalidOperationException">Se lanza cuando faltan valores requeridos</exception>
    private void ValidateRequiredFields()
    {
        if (_movementTypeId <= 0)
        {
            throw new InvalidOperationException("El ID del tipo de movimiento es requerido y debe ser mayor a 0");
        }

        if (_createdBy <= 0)
        {
            throw new InvalidOperationException("El ID del usuario creador es requerido y debe ser mayor a 0");
        }
    }

    /// <summary>
    ///     Crea una nueva instancia del builder
    /// </summary>
    /// <returns>Nueva instancia del BaseMovementBuilder</returns>
    public static BaseMovementBuilder Create()
    {
        return new BaseMovementBuilder();
    }

    /// <summary>
    ///     Crea una nueva instancia del builder con valores por defecto
    /// </summary>
    /// <param name="movementTypeId">ID del tipo de movimiento</param>
    /// <param name="createdBy">ID del usuario creador</param>
    /// <returns>Nueva instancia del BaseMovementBuilder</returns>
    public static BaseMovementBuilder Create(int movementTypeId, int createdBy)
    {
        return new BaseMovementBuilder().WithMovementTypeId(movementTypeId).WithCreatedBy(createdBy);
    }
}
