using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Movements.ChemicalBaths.Specs;

public class ChemicalBathMovementsByBatchIdsSpec : Specification<ChemicalBathMovement>
{
    public ChemicalBathMovementsByBatchIdsSpec(List<int> batchIds)
    {
        Query.Where(x => batchIds.Contains(x.BatchId));
        Query.Include(x => x.Batch);
        Query.Include(x => x.Bath);
        Query.OrderByDescending(x => x.EntryDate);
        Query.AsNoTracking();
    }
}
