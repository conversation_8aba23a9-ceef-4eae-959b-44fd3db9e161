using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Movements.BatchReclassifications.Events;

namespace SGP.Aserradero.Domain.Entities.Movements.BatchReclassifications;

[Table("BatchReclassification", Schema = "movements")]
public class Reclassification : BaseEntity<int>, IAggregateRoot
{
    public required int UserId { get; set; }
    public required string Comments { get; set; }
    public string Status { get; set; }
    public int CompanyId { get; set; }
    public string? SapDocNum { get; set; }
    public string? SapDocEntry { get; set; }
    public string? SapUploadDocNum { get; set; }
    public string? SapUploadDocEntry { get; set; }
    public double? ReductionQuantity { get; set; }
    public double? UploadQuantity { get; set; }
    public double? ReductionVolume { get; set; }
    public double? UploadVolume { get; set; }

    public static Reclassification Create(int userId, string comments, string status, int companyId)
    {
        var batchReclassification = new Reclassification
        {
            UserId = userId,
            Comments = comments,
            Status = status,
            CompanyId = companyId,
        };
        batchReclassification.QueueDomainEvent(
            new CreatedBatchReclassificationEvent { Reclassification = batchReclassification }
        );
        return batchReclassification;
    }

    public void UpdateReduction(string sapDocNum, string sapDocEntry, double reductionQuantity, double reductionVolume)
    {
        SapDocNum = sapDocNum;
        SapDocEntry = sapDocEntry;
        ReductionQuantity = reductionQuantity;
        ReductionVolume = reductionVolume;
    }

    public void UpdateUpload(
        string sapUploadDocNum,
        string sapUploadDocEntry,
        double uploadQuantity,
        double uploadVolume
    )
    {
        SapUploadDocNum = sapUploadDocNum;
        SapUploadDocEntry = sapUploadDocEntry;
        UploadQuantity = uploadQuantity;
        UploadVolume = uploadVolume;
    }

    public void Update(
        string status,
        string? sapDocNum,
        string? sapDocEntry,
        string? sapUploadDocNum,
        string? sapUploadDocEntry,
        double? reductionQuantity,
        double? uploadQuantity,
        double? reductionVolume,
        double? uploadVolume
    )
    {
        Status = status;
        if (sapDocNum != null)
        {
            SapDocNum = sapDocNum;
        }

        if (sapDocEntry != null)
        {
            SapDocEntry = sapDocEntry;
        }

        if (sapUploadDocNum != null)
        {
            SapUploadDocNum = sapUploadDocNum;
        }

        if (sapUploadDocEntry != null)
        {
            SapUploadDocEntry = sapUploadDocEntry;
        }

        if (reductionQuantity != null)
        {
            ReductionQuantity = reductionQuantity;
        }

        if (uploadQuantity != null)
        {
            UploadQuantity = uploadQuantity;
        }

        if (reductionVolume != null)
        {
            ReductionVolume = reductionVolume;
        }

        if (uploadVolume != null)
        {
            UploadVolume = uploadVolume;
        }

        QueueDomainEvent(new UpdatedBatchReclassificationEvent { Reclassification = this });
    }
}
