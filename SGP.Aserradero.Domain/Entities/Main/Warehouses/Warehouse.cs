using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Main.Warehouses.Events;
using SGP.Aserradero.Domain.Entities.Main.WarehouseStatusRels;
using SGP.Aserradero.Domain.Entities.Material.MaterialClasses;

namespace SGP.Aserradero.Domain.Entities.Main.Warehouses;

public class Warehouse : AuditableEntity<int>, IAggregateRoot
{
    public string Code { get; set; }
    public string Name { get; set; }
    public int MaterialClassId { get; set; }

    [ForeignKey(nameof(MaterialClassId))]
    public virtual MaterialClass MaterialClass { get; set; }

    public virtual ICollection<WarehouseStatusRel>? WarehouseStatuses { get; set; }

    public static Warehouse Create(string code, string name, int materialClassId, int userId)
    {
        var newWarehouse = new Warehouse
        {
            Code = code,
            Name = name,
            MaterialClassId = materialClassId,
            CreatedBy = userId,
            Created = DateTime.Now,
            LastModified = DateTime.Now,
            LastModifiedBy = userId,
        };
        newWarehouse.QueueDomainEvent(new WarehouseCreatedEvent { Warehouse = newWarehouse });
        return newWarehouse;
    }

    public void Update(string code, string name, int materialClassId, int userId)
    {
        Code = code;
        Name = name;
        MaterialClassId = materialClassId;
        LastModified = DateTime.Now;
        LastModifiedBy = userId;
        QueueDomainEvent(new WarehouseUpdatedEvent { Warehouse = this });
    }
}
