using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;

namespace SGP.Aserradero.Domain.Entities.Main.OrderClasses;

public class OrderClass : AuditableEntity<int>, IAggregateRoot
{
    public string Code { get; set; }
    public string Name { get; set; }
    public int Position { get; set; }
    public string? StatusConsumption { get; set; }
    public string? StatusProduction { get; set; }
    public bool IsActive { get; set; }

    // Navigation property for many-to-many relationship with MovementType through join entity
    public virtual ICollection<MovementTypeOrderClass>? MovementTypeOrderClasses { get; set; }

    public static OrderClass Create(
        string code,
        string name,
        int position,
        string? statusConsumption,
        string? statusProduction,
        int userId
    )
    {
        var newOrderClass = new OrderClass
        {
            Code = code,
            Name = name,
            Position = position,
            StatusConsumption = statusConsumption,
            StatusProduction = statusProduction,
            IsActive = true,
            Created = DateTime.Now,
            CreatedBy = userId,
            LastModified = DateTime.Now,
            LastModifiedBy = userId,
        };
        newOrderClass.QueueDomainEvent(new OrderClassCreatedEvent { OrderClass = newOrderClass });
        return newOrderClass;
    }

    public void Update(
        string code,
        string name,
        int position,
        string? statusConsumption,
        string? statusProduction,
        int userId
    )
    {
        Code = code;
        Name = name;
        Position = position;
        StatusConsumption = statusConsumption;
        StatusProduction = statusProduction;
        LastModified = DateTime.Now;
        LastModifiedBy = userId;
        QueueDomainEvent(new OrderClassUpdatedEvent { OrderClass = this });
    }

    public void Delete(int userId)
    {
        LastModified = DateTime.Now;
        LastModifiedBy = userId;
        Deleted = DateTime.Now;
        DeletedBy = userId;
        QueueDomainEvent(new OrderClassDeletedEvent { OrderClass = this });
    }
}
