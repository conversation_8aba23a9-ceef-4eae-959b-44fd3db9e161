using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Main.Machines.Events;

namespace SGP.Aserradero.Domain.Entities.Main.Machines;

public class Machine : AuditableEntity<int>, IAggregateRoot
{
    public string Code { get; set; }
    public string Name { get; set; }

    public static Machine Create(string code, string name, int userId)
    {
        var instance = new Machine
        {
            Code = code,
            Name = name,
            CreatedBy = userId,
            Created = DateTime.Now,
            LastModified = DateTime.Now,
            LastModifiedBy = userId,
        };
        instance.QueueDomainEvent(new MachineCreatedEvent(instance));
        return instance;
    }

    public void Update(string code, string name, int userId)
    {
        Code = code;
        Name = name;
        LastModified = DateTime.Now;
        LastModifiedBy = userId;
        QueueDomainEvent(new MachineUpdatedEvent(this));
    }
}
