namespace SGP.Aserradero.Domain.Entities.Batches.Requests;

public record CreateBatchRequest(
    string BatchCode,
    int MaterialId,
    int DestinationId,
    int MarketId,
    int DefectId,
    int BathId,
    int StrapId,
    int StatusId,
    bool IsStacking,
    bool IsHt,
    bool IsPainting,
    bool IsSeparating,
    bool IsTrimmed,
    bool IsWoodenBlock,
    int WarehouseId,
    int CreatedBy,
    string? Status = null
);

public record UpdateBatchRequest(
    int? DestinationId = null,
    int? MarketId = null,
    int? DefectId = null,
    int? BathId = null,
    int? StrapId = null,
    int? StatusId = null,
    bool? IsStacking = null,
    bool? IsHt = null,
    bool? IsPainting = null,
    bool? IsSeparating = null,
    bool? IsTrimmed = null,
    bool? IsWoodenBlock = null,
    int? WarehouseId = null,
    int ModifiedBy = 0,
    string? Status = null
);
