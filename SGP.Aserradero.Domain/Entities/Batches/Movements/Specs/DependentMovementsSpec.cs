using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Batches.Movements.Specs;

public class DependentMovementsSpec : Specification<BatchMovement>
{
    public DependentMovementsSpec(int movementId, DateTime movementDate, IReadOnlyCollection<int> batchIds)
    {
        // Only consider movements for the specified batches that occurred after the movement being undone
        Query.Where(x => x.MovementId != movementId && batchIds.Contains(x.BatchId) && x.ProcessDate > movementDate);

        Query.AsNoTracking();
    }
}
