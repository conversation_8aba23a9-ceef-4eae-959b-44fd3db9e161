using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Batches.Base.Specs;

public class BatchsWithDataSpec : Specification<Batch>
{
    public BatchsWithDataSpec(int page, int pageSize)
    {
        // Include all necessary related entities
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialType);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialClass);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialFamily);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialSubFamily);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialFinish);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialStatus);
        Query.Include(x => x.Warehouse);
        Query.Include(x => x.Destination);
        Query.Include(x => x.Market);
        Query.Include(x => x.Defect);
        Query.Include(x => x.Bath);
        Query.Include(x => x.Strap);
        Query.Include(x => x.StatusEntity);

        Query.Skip((page - 1) * pageSize).Take(pageSize);
        Query.OrderByDescending(x => x.CreatedDate);

        Query.AsNoTracking();
    }
}
