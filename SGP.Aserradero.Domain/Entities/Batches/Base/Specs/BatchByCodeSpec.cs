using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Batches.Base.Specs;

public class BatchByCodeSpec : Specification<Batch>
{
    public BatchByCodeSpec(string batchCode)
    {
        Query.Where(x => x.BatchCode.Contains(batchCode));
        Query.Include(x => x.Material);
        Query.Include(x => x.Destination);
        Query.Include(x => x.Market);
        Query.Include(x => x.Defect);
        Query.Include(x => x.Bath);
        Query.Include(x => x.Strap);
        Query.Include(x => x.StatusEntity);
        Query.Include(x => x.Warehouse);
        Query.OrderByDescending(x => x.BatchCode);
        Query.Take(100);
        Query.AsNoTracking();
    }
}
