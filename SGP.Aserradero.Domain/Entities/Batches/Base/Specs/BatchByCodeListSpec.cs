using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Batches.Base.Specs;

public class BatchByCodeListSpec : Specification<Batch>
{
    public BatchByCodeListSpec(IEnumerable<string> batchCodes)
    {
        Query.Where(x => batchCodes.Contains(x.BatchCode));

        // Include all necessary related entities
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialType);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialClass);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialFamily);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialSubFamily);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialFinish);
        Query.Include(x => x.Material).ThenInclude(x => x.MaterialStatus);
        Query.Include(x => x.Warehouse);
        Query.Include(x => x.Destination);
        Query.Include(x => x.Market);
        Query.Include(x => x.Defect);
        Query.Include(x => x.Bath);
        Query.Include(x => x.Strap);
        Query.Include(x => x.StatusEntity);

        // Filter only batches with valid Material.SapCode and Warehouse.Code
        Query.Where(x =>
            x.Material != null
            && !string.IsNullOrEmpty(x.Material.SapCode)
            && x.Warehouse != null
            && !string.IsNullOrEmpty(x.Warehouse.Code)
        );
        Query.OrderByDescending(x => x.BatchCode);
        Query.AsNoTrackingWithIdentityResolution();
    }
}
