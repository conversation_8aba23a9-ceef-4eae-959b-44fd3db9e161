using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Material.MaterialClasses;
using SGP.Aserradero.Domain.Entities.Material.MaterialFamilies.Events;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialFamilies;

[Table("Families", Schema = "material")]
public class MaterialFamily : AuditableEntity<int>, IAggregateRoot
{
    public string Code { get; set; }
    public string Name { get; set; }
    public int MaterialClassId { get; set; }

    [ForeignKey("MaterialClassId")]
    public virtual MaterialClass MaterialClass { get; set; }

    public static MaterialFamily Create(string code, string name, int materialClassId, int userId)
    {
        var instance = new MaterialFamily
        {
            Code = code,
            Name = name,
            MaterialClassId = materialClassId,
            Created = DateTime.Now,
            CreatedBy = userId,
        };
        instance.QueueDomainEvent(new MaterialFamilyCreatedEvent { MaterialFamily = instance });
        return instance;
    }

    public void Update(string code, string name, int materialClassId, int userId)
    {
        Code = code;
        Name = name;
        MaterialClassId = materialClassId;
        LastModifiedBy = userId;
        LastModified = DateTime.Now;
        QueueDomainEvent(new MaterialFamilyUpdatedEvent { MaterialFamily = this });
    }
}
