using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialFamilies.Specs;

public class MaterialFamilySearchSpec : Specification<MaterialFamily>
{
    public MaterialFamilySearchSpec(string? searchText, int? materialClassId)
    {
        Query.AsNoTracking();
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            var query = searchText.ToLower();
            Query.Where(x => x.Name.ToLower().Contains(query) || x.Code.ToLower().Contains(query));
        }

        if (materialClassId is > 0)
        {
            Query.Where(x => x.MaterialClassId == materialClassId.Value);
        }

        Query.OrderBy(x => x.Name);
        Query.Take(100);
    }
}
