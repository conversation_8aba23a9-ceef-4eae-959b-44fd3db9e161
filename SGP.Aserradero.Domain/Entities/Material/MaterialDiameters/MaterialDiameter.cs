using System.ComponentModel.DataAnnotations.Schema;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Domain.Contracts;
using SGP.Aserradero.Domain.Entities.Material.MaterialDiameters.Events;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialDiameters;

[Table("Diameters", Schema = "material")]
public class MaterialDiameter : BaseEntity<int>, IAggregateRoot
{
    private MaterialDiameter() { }

    public double Diameter { get; private set; }

    public static MaterialDiameter Create(double diameter)
    {
        var instance = new MaterialDiameter { Diameter = diameter };
        instance.QueueDomainEvent(new CreatedMaterialDiameterEvent { MaterialDiameter = instance });
        return instance;
    }

    public void Update(double diameter)
    {
        Diameter = diameter;
        QueueDomainEvent(new UpdatedMaterialDiameterEvent { MaterialDiameter = this });
    }

    public void Delete()
    {
        QueueDomainEvent(new DeletedMaterialDiameterEvent { MaterialDiameter = this });
    }
}
