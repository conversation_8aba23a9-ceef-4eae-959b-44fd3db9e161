using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialDatas.Specs;

public class GetPaginatedMaterialDataSpec : Specification<MaterialData>
{
    public GetPaginatedMaterialDataSpec(int page, int pageSize, int materialClassId)
    {
        Query
            .OrderBy(x => x.Description)
            .Where(x => x.MaterialClassId == materialClassId)
            .Include(x => x.MaterialClass)
            .Include(x => x.MaterialFamily)
            .Include(x => x.MaterialSubFamily)
            .Include(x => x.MaterialFinish)
            .Include(x => x.MaterialStatus)
            .Include(x => x.MaterialType)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsTracking();
    }
}
