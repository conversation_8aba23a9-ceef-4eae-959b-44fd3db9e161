using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialDatas.Specs;

public class QuickSearchMaterialDataSpec : Specification<MaterialData>
{
    public QuickSearchMaterialDataSpec(
        string? search = null,
        int? materialClassId = null,
        int? materialTypeId = null,
        int? materialFamilyId = null,
        int? materialSubFamilyId = null,
        int? materialFinishId = null,
        int? materialStatusId = null,
        string? section = null,
        bool? isBlocked = null,
        int maxResults = 100
    )
    {
        Query.AsNoTracking();

        // Aplicar filtros básicos
        ApplyFilters(
            search,
            materialClassId,
            materialTypeId,
            materialFamilyId,
            materialSubFamilyId,
            materialFinishId,
            materialStatusId,
            section,
            isBlocked
        );

        // Ordenar por descripción y código SAP
        Query.OrderBy(x => x.Description).ThenBy(x => x.SapCode);

        // Limitar resultados para optimizar rendimiento
        Query.Take(maxResults);
    }

    private void ApplyFilters(
        string? search,
        int? materialClassId,
        int? materialTypeId,
        int? materialFamilyId,
        int? materialSubFamilyId,
        int? materialFinishId,
        int? materialStatusId,
        string? section,
        bool? isBlocked
    )
    {
        // Búsqueda por texto en código SAP y descripción (búsqueda por defecto)
        if (!string.IsNullOrWhiteSpace(search))
        {
            var searchTerm = search.ToLower();
            Query.Where(x =>
                (x.SapCode != null && x.SapCode.ToLower().Contains(searchTerm))
                || (x.Description != null && x.Description.ToLower().Contains(searchTerm))
            );
        }

        // Filtros por IDs (los más comunes para dropdowns)
        if (materialClassId.HasValue)
        {
            Query.Where(x => x.MaterialClassId == materialClassId.Value);
        }

        if (materialTypeId.HasValue)
        {
            Query.Where(x => x.MaterialTypeId == materialTypeId.Value);
        }

        if (materialFamilyId.HasValue)
        {
            Query.Where(x => x.MaterialFamilyId == materialFamilyId.Value);
        }

        if (materialSubFamilyId.HasValue)
        {
            Query.Where(x => x.MaterialSubFamilyId == materialSubFamilyId.Value);
        }

        if (materialFinishId.HasValue)
        {
            Query.Where(x => x.MaterialFinishId == materialFinishId.Value);
        }

        if (materialStatusId.HasValue)
        {
            Query.Where(x => x.MaterialStatusId == materialStatusId.Value);
        }

        if (!string.IsNullOrWhiteSpace(section))
        {
            // Si contiene la seccion de forma continua, se debe buscar en el nombre de la clase de material
            var sectionTerm = section.ToLower();
            Query.Where(x => x.Description.ToLower().Contains(sectionTerm));
        }

        // Filtro por estado bloqueado
        if (isBlocked.HasValue)
        {
            Query.Where(x => x.IsBlocked == isBlocked.Value);
        }
    }
}
