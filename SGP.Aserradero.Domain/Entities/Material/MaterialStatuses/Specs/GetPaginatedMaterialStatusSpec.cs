using Ardalis.Specification;

namespace SGP.Aserradero.Domain.Entities.Material.MaterialStatuses.Specs;

public class GetPaginatedMaterialStatusSpec : Specification<MaterialStatus>
{
    public GetPaginatedMaterialStatusSpec(int page, int pageSize)
    {
        Query
            .OrderBy(x => x.Code)
            .Where(x => x.Deleted == null)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking();
    }
}
