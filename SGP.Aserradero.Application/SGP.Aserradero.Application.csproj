<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DocumentationFile>bin\Debug\SGP.Aserradero.Application.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DocumentationFile>bin\Release\SGP.Aserradero.Application.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\SGP.Aserradero.Domain\SGP.Aserradero.Domain.csproj"/>
    <ProjectReference Include="..\Submodules\Leonera-API_Common\Leonera-API.Common\Leonera-API.Common.csproj"/>
    <ProjectReference Include="..\Submodules\Leonera-API_ExternalServices\Ext.SAP.Queries\Ext.SAP.Queries.csproj"/>
    <ProjectReference Include="..\Submodules\Leonera-API_ExternalServices\Ext.SAP\Ext.SAP.csproj"/>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0"/>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Modules\Material\MaterialStatuses\Queries\Search\"/>
    <Folder Include="Modules\Material\MaterialTypes\"/>
    <Folder Include="Modules\Movements\Base\"/>
    <Folder Include="Modules\Movements\DryingManager\Commands\"/>
    <Folder Include="Modules\Movements\DryingManager\Queries\"/>
    <Folder Include="Modules\Movements\Productions\"/>
    <Folder Include="Modules\Movements\Stackings\Validate\"/>
  </ItemGroup>
  <ItemGroup>
    <None Remove="Templates\Template100x130.zpl"/>
    <EmbeddedResource Include="Templates\Template100x130.zpl">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <None Remove="Templates\Template100x120.zpl"/>
    <EmbeddedResource Include="Templates\Template100x120.zpl">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="SGP.Aserradero.Domain">
      <HintPath>..\SGP.Aserradero.API\bin\Debug\net9.0\SGP.Aserradero.Domain.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>
