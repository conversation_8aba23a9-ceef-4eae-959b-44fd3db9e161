namespace SGP.Aserradero.Application.Dtos;

/// <summary>
/// Data Transfer Object for MovementType entity to avoid circular reference issues during serialization
/// </summary>
public class MovementTypeDto
{
    /// <summary>
    /// Gets or sets the unique identifier for the movement type
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the movement type
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description of the movement type
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the movement type is active
    /// </summary>
    public bool IsActive { get; set; }
}
