using System.Net;
using Leonera_API_ExternalServices;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using Leonera_API.Common.Domain.Entities.GeneralData;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Base.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.SapData.BatchToSap;

public sealed class BatchToSapCommandHandler : IRequestHandler<BatchToSapCommand, Result<BatchToSapCommandResponse>>
{
    private const string LogPrefix = "[SAP][BatchToSap]";
    private readonly IRepository<BatchAuditLog> _batchAuditLogRepository;
    private readonly IRepository<Batch> _batchRepository;
    private readonly ILogger<BatchToSapCommandHandler> _logger;
    private readonly IRepository<BaseMovement> _movementRepository;
    private readonly IRepository<MovementType> _movementTypeRepository;
    private readonly IRepository<SapDatabases> _sapDatabasesRepository;
    private readonly SapServiceLayerFacade _sapServiceLayerFacade;

    public BatchToSapCommandHandler(IServiceProvider serviceProvider)
    {
        _batchRepository = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchAuditLogRepository = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _sapDatabasesRepository = serviceProvider.GetRequiredService<IRepository<SapDatabases>>();
        _movementRepository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _movementTypeRepository = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _sapServiceLayerFacade = serviceProvider.GetRequiredService<SapServiceLayerFacade>();
        _logger = serviceProvider.GetRequiredService<ILogger<BatchToSapCommandHandler>>();
    }

    public async Task<Result<BatchToSapCommandResponse>> Handle(
        BatchToSapCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Iniciando envío de lotes pre-validados a SAP", LogPrefix);

        try
        {
            var movementTypeByNameSpec = new MovementTypeByNameSpec("Envio a SAP");
            var movementType = await _movementTypeRepository.FirstOrDefaultAsync(
                movementTypeByNameSpec,
                cancellationToken
            );
            if (movementType == null)
            {
                _logger.LogError("{LogPrefix} - No se encontró el tipo de movimiento 'Envio a SAP'", LogPrefix);
                return Result<BatchToSapCommandResponse>.Failure("No se encontró el tipo de movimiento 'Envio a SAP'");
            }
            // Get batches (assuming they are already validated by BatchToSapValidator)
            var batchByCodesSpec = new BatchByCodeListSpec(request.BatchCodes);
            var batches = await _batchRepository.ListAsync(batchByCodesSpec, cancellationToken);

            if (batches.Count == 0)
            {
                _logger.LogWarning("{LogPrefix} - No se encontraron lotes para enviar", LogPrefix);
                return Result<BatchToSapCommandResponse>.Success(
                    new BatchToSapCommandResponse
                    {
                        IsSuccess = false,
                        Message = "No se encontraron lotes para enviar",
                        Results = [],
                    }
                );
            }

            _logger.LogInformation(
                "{LogPrefix} - Enviando {BatchCount} lotes pre-validados a SAP",
                LogPrefix,
                batches.Count
            );

            // Get SAP database configuration
            var sapDatabase = await _sapDatabasesRepository.GetByIdAsync(12, cancellationToken);
            if (sapDatabase == null)
            {
                _logger.LogError("{LogPrefix} - Base de datos de SAP no encontrada", LogPrefix);
                return Result<BatchToSapCommandResponse>.Failure("Base de datos de SAP no encontrada");
            }

            // Create system movement for SAP operation
            var systemMovement = BaseMovement.Create(
                "Envio a SAP",
                $"SGP - Envío de lotes a SAP: {string.Join(", ", request.BatchCodes)}",
                null,
                null,
                null,
                null,
                null,
                request.CreatedBy,
                movementType.Id
            );

            await _movementRepository.AddAsync(systemMovement, cancellationToken);

            var results = new List<BatchSendResult>();
            var documentLines = new List<DocumentLine>();

            // Process each batch to create document lines
            foreach (var batch in batches)
            {
                try
                {
                    _logger.LogInformation("{LogPrefix} - Procesando lote: {BatchCode}", LogPrefix, batch.BatchCode);

                    // Determine warehouse based on material status
                    var warehouseCode =
                        batch.Material?.MaterialStatus?.Name?.ToLower().Contains("verde") == true ? "370" : "371";

                    var documentLine = new DocumentLine
                    {
                        ItemCode = batch.Material?.SapCode ?? string.Empty,
                        WarehouseCode = warehouseCode,
                        Quantity = batch.Material?.Pieces ?? 1,
                        MovementType = "P",
                        CostingCode = "909901",
                        CostingCode2 = "909900",
                        CostingCode3 = "909000",
                        CostingCode4 = "900000",
                        BatchNumbers =
                        [
                            new BatchNumbers
                            {
                                BatchNumber = batch.BatchCode,
                                Quantity = batch.Material?.Pieces ?? 1,
                                ItemCode = batch.Material?.SapCode ?? string.Empty,
                                ManufacturingDate = batch.CreatedDate,
                            },
                        ],
                    };

                    documentLines.Add(documentLine);

                    results.Add(
                        new BatchSendResult
                        {
                            BatchCode = batch.BatchCode,
                            IsSuccess = true,
                            Message = "Preparado para envío",
                        }
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "{LogPrefix} - Error preparando lote {BatchCode}", LogPrefix, batch.BatchCode);
                    results.Add(
                        new BatchSendResult
                        {
                            BatchCode = batch.BatchCode,
                            IsSuccess = false,
                            Message = $"Error preparando lote: {ex.Message}",
                        }
                    );
                }
            }

            // Send to SAP if we have document lines
            if (documentLines.Count > 0)
            {
                var sendResult = await SendToSap(documentLines, sapDatabase, batches);

                // Update results based on SAP response
                if (sendResult.IsSuccess)
                {
                    await UpdateSuccessfulBatches(batches, systemMovement, cancellationToken);

                    foreach (var result in results.Where(r => r.IsSuccess))
                    {
                        result.Message = "Enviado exitosamente a SAP";
                    }
                }
                else
                {
                    foreach (var result in results.Where(r => r.IsSuccess))
                    {
                        result.IsSuccess = false;
                        result.Message = $"Error enviando a SAP: {sendResult.Message}";
                    }
                }
            }

            var successCount = results.Count(r => r.IsSuccess);
            var failCount = results.Count(r => !r.IsSuccess);

            var response = new BatchToSapCommandResponse
            {
                IsSuccess = successCount > 0,
                Message = $"Proceso completado. Exitosos: {successCount}, Fallidos: {failCount}",
                Results = results,
            };

            _logger.LogInformation(
                "{LogPrefix} - Proceso completado: {SuccessCount} exitosos, {FailCount} fallidos",
                LogPrefix,
                successCount,
                failCount
            );

            return Result<BatchToSapCommandResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error durante el envío de lotes a SAP", LogPrefix);
            return Result<BatchToSapCommandResponse>.Failure(ex.Message);
        }
    }

    private async Task<(bool IsSuccess, string Message)> SendToSap(
        List<DocumentLine> documentLines,
        SapDatabases sapDatabase,
        List<Batch> batches
    )
    {
        try
        {
            var inventoryEntry = new InventoryGenEntry
            {
                DocDate = DateTime.Now,
                Comments = $"SGP - Lotes: {string.Join(", ", batches.Select(b => b.BatchCode))}",
                DocumentLines = documentLines,
            };

            _logger.LogInformation(
                "{LogPrefix} - Enviando entry con {LineCount} líneas a SAP",
                LogPrefix,
                documentLines.Count
            );

            var authToken = await _sapServiceLayerFacade.Login.ConnectSl2(sapDatabase.DatabaseName);
            if (authToken == null)
            {
                return (false, "Error al obtener token de autenticación");
            }

            var (statusCode, content) = await _sapServiceLayerFacade.InventoryGenEntries.PostInventoryGenEntry(
                inventoryEntry,
                authToken
            );

            if (statusCode == HttpStatusCode.Created || statusCode == HttpStatusCode.OK)
            {
                _logger.LogInformation("{LogPrefix} - Entry enviado exitosamente a SAP", LogPrefix);
                return (true, "Enviado exitosamente");
            }

            _logger.LogError(
                "{LogPrefix} - Error enviando a SAP. Status: {StatusCode}, Content: {Content}",
                LogPrefix,
                statusCode,
                content
            );

            return (false, $"SAP Error {statusCode}: {content}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Excepción enviando a SAP", LogPrefix);
            return (false, ex.Message);
        }
    }

    private async Task UpdateSuccessfulBatches(
        List<Batch> batches,
        BaseMovement systemBaseMovement,
        CancellationToken cancellationToken
    )
    {
        foreach (var batch in batches)
        {
            try
            {
                // Update batch IsInSap status
                var batchToUpdate = await _batchRepository.GetByIdAsync(batch.Id, cancellationToken);
                if (batchToUpdate != null)
                {
                    batchToUpdate.IsInSap = true;
                    batchToUpdate.LastModified = DateTime.Now;
                    batchToUpdate.LastModifiedBy = systemBaseMovement.CreatedBy ?? 1;
                    await _batchRepository.UpdateAsync(batchToUpdate, cancellationToken);
                }

                // Create audit log
                var auditLog = new BatchAuditLog
                {
                    BatchId = batch.Id,
                    MovementId = systemBaseMovement.Id,
                    FieldName = "IsInSap",
                    OldValue = "false",
                    NewValue = "true",
                    ChangeDate = DateTime.Now,
                    ChangedBy = systemBaseMovement.CreatedBy ?? 1,
                };

                await _batchAuditLogRepository.AddAsync(auditLog, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "{LogPrefix} - Error actualizando lote {BatchCode} después de envío exitoso",
                    LogPrefix,
                    batch.BatchCode
                );
            }
        }
    }
}

// SAP Data Transfer Objects
public class InventoryGenEntry
{
    public string Comments { get; set; } = string.Empty;
    public DateTime DocDate { get; set; }
    public List<DocumentLine> DocumentLines { get; set; } = [];
}

public class DocumentLine
{
    public string ItemCode { get; set; } = string.Empty;
    public string WarehouseCode { get; set; } = string.Empty;
    public double Quantity { get; set; }
    public string? CostingCode { get; set; }
    public string? CostingCode2 { get; set; }
    public string? CostingCode3 { get; set; }
    public string? CostingCode4 { get; set; }
    public string? MovementType { get; set; }
    public List<BatchNumbers> BatchNumbers { get; set; } = [];
}

public class BatchNumbers
{
    public string BatchNumber { get; set; } = string.Empty;
    public double Quantity { get; set; }
    public string ItemCode { get; set; } = string.Empty;
    public DateTime? ManufacturingDate { get; set; }
}
