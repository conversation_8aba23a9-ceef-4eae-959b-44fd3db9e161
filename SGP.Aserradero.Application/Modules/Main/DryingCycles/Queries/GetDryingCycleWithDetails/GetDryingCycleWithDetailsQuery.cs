using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Dtos.DryingCycle;

namespace SGP.Aserradero.Application.Modules.Main.DryingCycles.Queries.GetDryingCycleWithDetails;

public record GetDryingCycleWithDetailsQuery : IRequest<Result<DryingCycleWithDetailsDto>>
{
    /// <summary>
    /// ID del ciclo de secado
    /// </summary>
    public required int DryingCycleId { get; init; }
}
