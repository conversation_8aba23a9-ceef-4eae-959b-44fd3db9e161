using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Main.Shifts.DTOs;

namespace SGP.Aserradero.Application.Modules.Main.Shifts.Commands.Update;

public record UpdateShiftCommand : IRequest<Result<ShiftDto>>
{
    public int Id { get; init; }
    public string Name { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public int ModifiedBy { get; init; }
}
