using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Modules.Main.Shifts.DTOs;
using SGP.Aserradero.Domain.Entities.Main.Shifts;

namespace SGP.Aserradero.Application.Modules.Main.Shifts.Commands.Create;

public class CreateShiftCommandHandler : IRequestHandler<CreateShiftCommand, Result<ShiftDto>>
{
    private const string LogPrefix = "[Turnos][Creación]";
    private readonly ILogger<CreateShiftCommandHandler> _logger;
    private readonly IRepository<Shift> _repository;

    public CreateShiftCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Shift>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateShiftCommandHandler>>();
    }

    public async Task<Result<ShiftDto>> Handle(CreateShiftCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Iniciando creación de turno", LogPrefix);
        try
        {
            var shift = Shift.Create(request.Name, request.CreatedBy, request.StartDate, request.EndDate);

            await _repository.AddAsync(shift, cancellationToken);
            await _repository.SaveChangesAsync(cancellationToken);

            var result = new ShiftDto
            {
                Id = shift.Id,
                Name = shift.Name,
                StartDate = shift.StartDate,
                EndDate = shift.EndDate,
            };

            return Result<ShiftDto>.Success(result, "Turno creado correctamente", 201);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al crear el turno", LogPrefix);
            return Result<ShiftDto>.Failure("Error al crear el turno");
        }
    }
}
