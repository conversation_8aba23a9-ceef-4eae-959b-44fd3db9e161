using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Main.Shifts.DTOs;

namespace SGP.Aserradero.Application.Modules.Main.Shifts.Commands.Create;

public record CreateShiftCommand : IRequest<Result<ShiftDto>>
{
    public string Name { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public int CreatedBy { get; init; }
}
