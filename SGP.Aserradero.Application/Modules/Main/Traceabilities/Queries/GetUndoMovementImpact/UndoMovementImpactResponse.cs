using SGP.Aserradero.Application.Modules.Main.Traceabilities.Commands.UndoBatchMovement;

namespace SGP.Aserradero.Application.Modules.Main.Traceabilities.Queries.GetUndoMovementImpact;

// Re-use UndoValidationInfo from commands

/// <summary>
///     Response for <see cref="GetUndoMovementImpactQuery" />
/// </summary>
public class UndoMovementImpactResponse
{
    /// <summary>
    ///     List of batches that would be affected if the movement is undone.
    /// </summary>
    public List<AffectedBatchInfo> Batches { get; set; } = [];

    /// <summary>
    ///     Validation information about whether the movement can be safely undone.
    /// </summary>
    public UndoValidationInfo ValidationInfo { get; set; } = new();
}

/// <summary>
///     Information about an individual batch that will be impacted by the undo operation.
/// </summary>
public class AffectedBatchInfo
{
    /// <summary>
    ///     Identifier of the batch.
    /// </summary>
    public int BatchId { get; set; }

    /// <summary>
    ///     Code of the batch.
    /// </summary>
    public string BatchCode { get; set; } = string.Empty;

    /// <summary>
    ///     Field changes that would be reverted for this batch. Key: field name, Value: previous value
    ///     that will be restored.
    ///     This dictionary is only populated when the request sets <c>IncludeFieldChanges</c> to true.
    /// </summary>
    public Dictionary<string, string?> FieldChanges { get; set; } = [];
}
