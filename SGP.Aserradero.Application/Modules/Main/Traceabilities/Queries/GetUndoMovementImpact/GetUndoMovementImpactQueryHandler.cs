using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Modules.Main.Traceabilities.Commands;
using SGP.Aserradero.Application.Modules.Main.Traceabilities.Commands.UndoBatchMovement;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Logs.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Movements.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Specs;

namespace SGP.Aserradero.Application.Modules.Main.Traceabilities.Queries.GetUndoMovementImpact;

/// <summary>
///     Handler that calculates the impact of undoing a movement.
/// </summary>
public sealed class GetUndoMovementImpactQueryHandler
    : IRequestHandler<GetUndoMovementImpactQuery, Result<UndoMovementImpactResponse>>
{
    private const string LogPrefix = "[Traceability][UndoImpact]";
    private readonly IRepository<BatchAuditLog> _batchAuditLogRepository;
    private readonly IRepository<BatchMovement> _batchMovementRepository;
    private readonly ILogger<GetUndoMovementImpactQueryHandler> _logger;

    private readonly IRepository<BaseMovement> _movementRepository;

    public GetUndoMovementImpactQueryHandler(IServiceProvider serviceProvider)
    {
        _movementRepository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchMovementRepository = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _batchAuditLogRepository = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetUndoMovementImpactQueryHandler>>();
    }

    public async Task<Result<UndoMovementImpactResponse>> Handle(
        GetUndoMovementImpactQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation(
            "{LogPrefix} - Calculating undo impact for movement {MovementId}",
            LogPrefix,
            request.MovementId
        );

        try
        {
            // 1. Retrieve movement
            var movementSpec = new MovementByIdSpec(request.MovementId);
            var movement = await _movementRepository.FirstOrDefaultAsync(movementSpec, cancellationToken);

            if (movement == null)
            {
                _logger.LogWarning("{LogPrefix} - Movement not found: {MovementId}", LogPrefix, request.MovementId);
                return Result<UndoMovementImpactResponse>.Failure($"Movement with ID {request.MovementId} not found");
            }

            // 2. Retrieve batch movements linked to this movement
            var batchMovementSpec = new BatchMovementByMovementIdSpec(request.MovementId);
            var batchMovements = await _batchMovementRepository.ListAsync(batchMovementSpec, cancellationToken);

            if (!batchMovements.Any())
            {
                _logger.LogInformation(
                    "{LogPrefix} - No batches associated with movement {MovementId}",
                    LogPrefix,
                    request.MovementId
                );

                var emptyResponse = new UndoMovementImpactResponse
                {
                    ValidationInfo = BuildBasicValidationInfo(movement),
                };
                return Result<UndoMovementImpactResponse>.Success(emptyResponse);
            }

            // 3. Build response
            var response = new UndoMovementImpactResponse { ValidationInfo = BuildBasicValidationInfo(movement) };

            foreach (var batchMovement in batchMovements)
            {
                var info = new AffectedBatchInfo
                {
                    BatchId = batchMovement.BatchId,
                    BatchCode = batchMovement.Batch?.BatchCode ?? string.Empty,
                };

                // Optionally include field changes
                if (request.IncludeFieldChanges)
                {
                    var auditSpec = new BatchAuditLogByMovementAndBatchSpec(request.MovementId, batchMovement.BatchId);
                    var auditLogs = await _batchAuditLogRepository.ListAsync(auditSpec, cancellationToken);

                    foreach (var log in auditLogs)
                    {
                        // If the same field was changed multiple times in this movement, keep the oldest oldValue
                        if (!info.FieldChanges.ContainsKey(log.FieldName))
                        {
                            info.FieldChanges[log.FieldName] = log.OldValue;
                        }
                    }
                }

                response.Batches.Add(info);
            }

            return Result<UndoMovementImpactResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error while calculating undo impact", LogPrefix);
            return Result<UndoMovementImpactResponse>.Failure($"Error while calculating undo impact: {ex.Message}");
        }
    }

    /// <summary>
    ///     Builds a minimal <see cref="UndoValidationInfo" /> based on the movement age and SAP status.
    ///     We intentionally skip dependency checks to keep the query lightweight.
    /// </summary>
    private static UndoValidationInfo BuildBasicValidationInfo(BaseMovement baseMovement)
    {
        var info = new UndoValidationInfo
        {
            IsInSap = baseMovement.InSap == true,
            MovementAgeInDays = (int)(DateTime.Now - baseMovement.Created).TotalDays,
        };

        // 30-day rule
        if (info.MovementAgeInDays > 30)
        {
            info.CanBeUndone = false;
            info.RestrictionReason = "Movement is older than 30 days and cannot be undone";
            return info;
        }

        // SAP rule
        if (info.IsInSap)
        {
            info.CanBeUndone = false;
            info.RestrictionReason = "Movement has been sent to SAP and cannot be undone";
            return info;
        }

        info.CanBeUndone = true;
        return info;
    }
}
