using FluentValidation;

namespace SGP.Aserradero.Application.Modules.Main.Traceabilities.Queries.GetUndoMovementImpact.Validators;

/// <summary>
///     Validation rules for <see cref="GetUndoMovementImpactQuery" />.
/// </summary>
public class GetUndoMovementImpactQueryValidator : AbstractValidator<GetUndoMovementImpactQuery>
{
    public GetUndoMovementImpactQueryValidator()
    {
        RuleFor(x => x.MovementId).GreaterThan(0).WithMessage("MovementId must be greater than 0");
    }
}
