using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Base.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Logs.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Movements.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.Productions;
using SGP.Aserradero.Domain.Entities.Movements.Stackings;

namespace SGP.Aserradero.Application.Modules.Main.Traceabilities.Queries.GetBatchMovements;

/// <summary>
///     Handler for getting batch movements with traceability information
/// </summary>
public sealed class GetBatchMovementsQueryHandler
    : IRequestHandler<GetBatchMovementsQuery, Result<List<BatchMovementResponse>>>
{
    private const string LogPrefix = "[Traceability][BatchMovements]";
    private readonly IRepository<BatchAuditLog> _batchAuditLogRepository;
    private readonly IRepository<BatchMovement> _batchMovementRepository;
    private readonly IRepository<BatchProduction> _batchProductionRepository;

    private readonly IRepository<Batch> _batchRepository;
    private readonly ILogger<GetBatchMovementsQueryHandler> _logger;
    private readonly IRepository<BaseMovement> _movementRepository;
    private readonly IRepository<StackingEntity> _stackingRepository;

    public GetBatchMovementsQueryHandler(IServiceProvider serviceProvider)
    {
        _batchRepository = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchMovementRepository = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _batchAuditLogRepository = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _movementRepository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _stackingRepository = serviceProvider.GetRequiredService<IRepository<StackingEntity>>();
        _batchProductionRepository = serviceProvider.GetRequiredService<IRepository<BatchProduction>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetBatchMovementsQueryHandler>>();
    }

    public async Task<Result<List<BatchMovementResponse>>> Handle(
        GetBatchMovementsQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Starting batch movements query", LogPrefix);

        try
        {
            // Validate input
            if (request.BatchIds?.Count == 0 && request.BatchCodes?.Count == 0)
            {
                return Result<List<BatchMovementResponse>>.Failure("Either BatchIds or BatchCodes must be provided");
            }

            // Get batches first to validate they exist
            var batchSpec = new BatchTraceabilitySpec(request.BatchIds, request.BatchCodes);
            var batches = await _batchRepository.ListAsync(batchSpec, cancellationToken);

            if (batches.Count == 0)
            {
                _logger.LogWarning("{LogPrefix} - No batches found", LogPrefix);
                return Result<List<BatchMovementResponse>>.Success([]);
            }

            var batchIds = batches.Select(b => b.Id).ToList();
            _logger.LogInformation("{LogPrefix} - Found {Count} batches", LogPrefix, batches.Count);

            // Get all batch movements for these batches
            var batchMovementSpec = new BatchMovementWithDataSpec(batchIds);
            var batchMovements = await _batchMovementRepository.ListAsync(batchMovementSpec, cancellationToken);

            if (batchMovements.Count == 0)
            {
                _logger.LogWarning("{LogPrefix} - No batch movements found", LogPrefix);
                return Result<List<BatchMovementResponse>>.Success([]);
            }

            // Get audit history if requested
            var auditDictionary = new Dictionary<int, List<BatchAuditLog>>();
            if (request.IncludeAuditHistory)
            {
                var auditTasks = batchIds.Select(async batchId =>
                {
                    var auditSpec = new BatchAuditLogByBatchIdSpec(batchId);
                    var auditLogs = await _batchAuditLogRepository.ListAsync(auditSpec, cancellationToken);
                    return new { BatchId = batchId, AuditLogs = auditLogs };
                });
                var auditResults = await Task.WhenAll(auditTasks);
                auditDictionary = auditResults.ToDictionary(x => x.BatchId, x => x.AuditLogs);
            }

            // Map to response
            var responses = new List<BatchMovementResponse>();

            foreach (var batchMovement in batchMovements)
            {
                var response = MapBatchMovementToResponse(batchMovement, request, auditDictionary);
                responses.Add(response);
            }

            _logger.LogInformation(
                "{LogPrefix} - Successfully processed {Count} batch movements",
                LogPrefix,
                responses.Count
            );

            return Result<List<BatchMovementResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error processing batch movements query", LogPrefix);
            return Result<List<BatchMovementResponse>>.Failure($"Error procesando movimientos de lotes: {ex.Message}");
        }
    }

    private BatchMovementResponse MapBatchMovementToResponse(
        BatchMovement batchMovement,
        GetBatchMovementsQuery request,
        Dictionary<int, List<BatchAuditLog>> auditDictionary
    )
    {
        var response = new BatchMovementResponse
        {
            Id = batchMovement.Id,
            MovementId = batchMovement.MovementId,
            BatchId = batchMovement.BatchId,
            BatchCode = batchMovement.Batch?.BatchCode ?? string.Empty,
            ProcessDate = batchMovement.ProcessDate,
            MovementType = batchMovement.MovementType.Name,
        };

        // Check if movement can be undone
        var (canBeUndone, restrictionReason) = ValidateUndoCapability(batchMovement);
        response.CanBeUndone = canBeUndone;
        response.UndoRestrictionReason = restrictionReason;

        // Include movement details if requested
        if (request.IncludeMovementDetails && batchMovement.Movement != null)
        {
            response.Movement = MapMovementDetails(batchMovement.Movement);
        }

        // Include batch details if requested
        if (request.IncludeBatchDetails && batchMovement.Batch != null)
        {
            response.Batch = MapBatchDetails(batchMovement.Batch);
        }

        // Include audit history if requested
        if (request.IncludeAuditHistory && auditDictionary.TryGetValue(batchMovement.BatchId, out var auditLogs))
        {
            response.AuditHistory = MapAuditHistory(auditLogs);
        }

        return response;
    }

    private (bool CanBeUndone, string? RestrictionReason) ValidateUndoCapability(BatchMovement batchMovement)
    {
        // Check if movement is too old (more than 30 days)
        var daysSinceMovement = (DateTime.Now - batchMovement.ProcessDate).TotalDays;
        if (daysSinceMovement > 30)
        {
            return (false, "Movement is older than 30 days and cannot be undone");
        }

        // Check if movement has been sent to SAP
        if (batchMovement.Movement?.InSap == true)
        {
            return (false, "Movement has been sent to SAP and cannot be undone");
        }

        // Check if there are dependent movements (movements that happened after this one)
        // This would require additional logic to check for subsequent movements
        // For now, we'll assume it can be undone if it's recent and not in SAP

        return (true, null);
    }

    private MovementDetails MapMovementDetails(BaseMovement baseMovement)
    {
        return new MovementDetails
        {
            Id = baseMovement.Id,
            MovementInfo = baseMovement.MovementInfo ?? string.Empty,
            Observation = baseMovement.Observation ?? string.Empty,
            MovementType = baseMovement.MovementType?.Name,
            Created = baseMovement.Created,
            CreatedBy = baseMovement.CreatedBy,
            LastModified = baseMovement.LastModified,
            LastModifiedBy = baseMovement.LastModifiedBy,
            InSap = baseMovement.InSap,
            Capturer =
                baseMovement.Capturer != null
                    ? new CapturerInfo
                    {
                        Id = baseMovement.Capturer.Id,
                        Code = baseMovement.Capturer.Rut,
                        Name = baseMovement.Capturer.Name,
                    }
                    : null,
            Machine =
                baseMovement.Machine != null
                    ? new MachineInfo
                    {
                        Id = baseMovement.Machine.Id,
                        Code = baseMovement.Machine.Code,
                        Name = baseMovement.Machine.Name,
                    }
                    : null,
            Warehouse =
                baseMovement.Warehouse != null
                    ? new WarehouseInfo
                    {
                        Id = baseMovement.Warehouse.Id,
                        Code = baseMovement.Warehouse.Code,
                        Name = baseMovement.Warehouse.Name,
                    }
                    : null,
            ManufacturingOrder =
                baseMovement.ManufacturingOrder != null
                    ? new ManufacturingOrderInfo
                    {
                        Id = baseMovement.ManufacturingOrder.Id,
                        Code = baseMovement.ManufacturingOrder.Code,
                        Description = baseMovement.ManufacturingOrder.Name,
                    }
                    : null,
            Document =
                baseMovement.Document != null
                    ? new DocumentInfo
                    {
                        Id = baseMovement.Document.Id,
                        Code = baseMovement.Document.Code,
                        Date = baseMovement.Document.Date,
                        Shift = new ShiftInfo
                        {
                            Id = baseMovement.Document.Shift?.Id ?? 0,
                            Name = baseMovement.Document.Shift?.Name ?? string.Empty,
                        },
                        OrderClass = new OrderClassInfo
                        {
                            Id = baseMovement.Document.OrderClass?.Id ?? 0,
                            Code = baseMovement.Document.OrderClass?.Code ?? string.Empty,
                            Name = baseMovement.Document.OrderClass?.Name ?? string.Empty,
                        },
                    }
                    : null,
        };
    }

    private BatchDetails MapBatchDetails(Batch batch)
    {
        return new BatchDetails
        {
            Id = batch.Id,
            BatchCode = batch.BatchCode,
            Status = batch.Status ?? string.Empty,
            IsInSap = batch.IsInSap,
            CreatedDate = batch.CreatedDate,
            CreatedBy = batch.CreatedBy,
            LastModified = batch.LastModified,
            LastModifiedBy = batch.LastModifiedBy,
            Material = new MaterialInfo
            {
                Id = batch.MaterialId,
                SapCode = batch.Material?.SapCode ?? string.Empty,
                Description = batch.Material?.Description ?? string.Empty,
                TypeCode = batch.Material?.MaterialType?.Code ?? string.Empty,
                TypeName = batch.Material?.MaterialType?.Name ?? string.Empty,
                ClassCode = batch.Material?.MaterialClass?.Code ?? string.Empty,
                ClassName = batch.Material?.MaterialClass?.Name ?? string.Empty,
                FamilyCode = batch.Material?.MaterialFamily?.Code ?? string.Empty,
                FamilyName = batch.Material?.MaterialFamily?.Name ?? string.Empty,
                SubFamilyCode = batch.Material?.MaterialSubFamily?.Code ?? string.Empty,
                SubFamilyName = batch.Material?.MaterialSubFamily?.Name ?? string.Empty,
                FinishCode = batch.Material?.MaterialFinish?.Code ?? string.Empty,
                FinishName = batch.Material?.MaterialFinish?.Name ?? string.Empty,
                StatusCode = batch.Material?.MaterialStatus?.Code ?? string.Empty,
                StatusName = batch.Material?.MaterialStatus?.Name ?? string.Empty,
            },
            Characteristics = new BatchCharacteristics
            {
                DestinationId = batch.DestinationId,
                DestinationCode = batch.Destination?.Code ?? string.Empty,
                DestinationName = batch.Destination?.Name ?? string.Empty,
                MarketId = batch.MarketId,
                MarketCode = batch.Market?.Code ?? string.Empty,
                MarketName = batch.Market?.Name ?? string.Empty,
                DefectId = batch.DefectId,
                DefectCode = batch.Defect?.Code ?? string.Empty,
                DefectName = batch.Defect?.Name ?? string.Empty,
                BathId = batch.BathId,
                BathCode = batch.Bath?.Code ?? string.Empty,
                BathName = batch.Bath?.Name ?? string.Empty,
                StrapId = batch.StrapId,
                StrapCode = batch.Strap?.Code ?? string.Empty,
                StrapName = batch.Strap?.Name ?? string.Empty,
                StatusId = batch.StatusId,
                StatusCode = batch.StatusEntity?.Code ?? string.Empty,
                StatusName = batch.StatusEntity?.Name ?? string.Empty,
            },
            States = new BatchStates
            {
                IsStacking = batch.IsStacking,
                IsHt = batch.IsHt,
                IsPainting = batch.IsPainting,
                IsSeparating = batch.IsSeparating,
                IsTrimmed = batch.IsTrimmed,
                IsWoodenBlock = batch.IsWoodenBlock,
            },
            Warehouse =
                batch.Warehouse != null
                    ? new WarehouseInfo
                    {
                        Id = batch.Warehouse.Id,
                        Code = batch.Warehouse.Code,
                        Name = batch.Warehouse.Name,
                    }
                    : null,
        };
    }

    private List<AuditInfo> MapAuditHistory(List<BatchAuditLog> auditLogs)
    {
        return auditLogs
            .Select(log => new AuditInfo
            {
                Id = log.Id,
                FieldName = log.FieldName,
                OldValue = log.OldValue,
                NewValue = log.NewValue,
                ChangeDate = log.ChangeDate,
                ChangedBy = log.ChangedBy,
                MovementInfo = log.Movement?.MovementType?.Name,
            })
            .ToList();
    }
}
