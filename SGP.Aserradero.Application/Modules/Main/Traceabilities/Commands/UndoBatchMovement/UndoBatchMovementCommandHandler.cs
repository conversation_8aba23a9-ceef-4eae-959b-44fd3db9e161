using System.Diagnostics;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Logs.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Movements.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Specs;
using SGP.Aserradero.Domain.Entities.Movements.ChemicalBaths;
using SGP.Aserradero.Domain.Entities.Movements.ChemicalBaths.Specs;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles.Specs;
using SGP.Aserradero.Domain.Entities.Movements.LogConsumptions;
using SGP.Aserradero.Domain.Entities.Movements.LogConsumptions.Specs;
using SGP.Aserradero.Domain.Entities.Movements.Productions;
using SGP.Aserradero.Domain.Entities.Movements.Productions.Specs;
using SGP.Aserradero.Domain.Entities.Movements.Stackings;
using SGP.Aserradero.Domain.Entities.Movements.Stackings.Specs;
using SGP.Aserradero.Domain.Entities.Movements.WarehouseChanges;
using SGP.Aserradero.Domain.Entities.Movements.WarehouseChanges.Specs;

namespace SGP.Aserradero.Application.Modules.Main.Traceabilities.Commands.UndoBatchMovement;

/// <summary>
///     Handler for undoing batch movements
/// </summary>
public sealed class UndoBatchMovementCommandHandler
    : IRequestHandler<UndoBatchMovementCommand, Result<UndoBatchMovementResult>>
{
    private const string LogPrefix = "[Traceability][UndoBatchMovement]";
    private readonly IRepository<BatchAuditLog> _batchAuditLogRepository;
    private readonly IRepository<BatchMovement> _batchMovementRepository;
    private readonly IRepository<BatchProduction> _batchProductionRepository;
    private readonly IRepository<Batch> _batchRepository;
    private readonly IRepository<ChemicalBathMovement> _chemicalBathMovementRepository;
    private readonly IRepository<DryingCycleDetail> _dryingCycleDetailsRepository;
    private readonly IRepository<DryingCycleEntity> _dryingCyclesRepository;
    private readonly IRepository<LogConsumption> _logConsumptionRepository;
    private readonly ILogger<UndoBatchMovementCommandHandler> _logger;

    private readonly IRepository<BaseMovement> _movementRepository;
    private readonly IRepository<MovStacking> _movStackingRepository;
    private readonly IRepository<MovWareHouseChange> _movWareHouseChangeRepository;
    private readonly IRepository<StackingEntity> _stackingRepository;

    public UndoBatchMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _movementRepository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchMovementRepository = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _batchRepository = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchAuditLogRepository = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _chemicalBathMovementRepository = serviceProvider.GetRequiredService<IRepository<ChemicalBathMovement>>();
        _batchProductionRepository = serviceProvider.GetRequiredService<IRepository<BatchProduction>>();
        _stackingRepository = serviceProvider.GetRequiredService<IRepository<StackingEntity>>();
        _logConsumptionRepository = serviceProvider.GetRequiredService<IRepository<LogConsumption>>();
        _movWareHouseChangeRepository = serviceProvider.GetRequiredService<IRepository<MovWareHouseChange>>();
        _movStackingRepository = serviceProvider.GetRequiredService<IRepository<MovStacking>>();
        _dryingCyclesRepository = serviceProvider.GetRequiredService<IRepository<DryingCycleEntity>>();
        _dryingCycleDetailsRepository = serviceProvider.GetRequiredService<IRepository<DryingCycleDetail>>();
        _logger = serviceProvider.GetRequiredService<ILogger<UndoBatchMovementCommandHandler>>();
    }

    public async Task<Result<UndoBatchMovementResult>> Handle(
        UndoBatchMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation(
            "{LogPrefix} - Starting undo batch movement process for MovementId: {MovementId}",
            LogPrefix,
            request.MovementId
        );

        try
        {
            // 1. Get the movement with related entities
            var movementSpec = new MovementByIdSpec(request.MovementId);
            var movement = await _movementRepository.FirstOrDefaultAsync(movementSpec, cancellationToken);

            if (movement == null)
            {
                _logger.LogWarning("{LogPrefix} - Movement not found: {MovementId}", LogPrefix, request.MovementId);
                return Result<UndoBatchMovementResult>.NotFound(
                    $"Movimiento con ID {request.MovementId} no encontrado"
                );
            }

            // 2. Get all batches affected by this movement
            var batchMovementSpec = new BatchMovementByMovementIdSpec(request.MovementId);
            var batchMovements = await _batchMovementRepository.ListAsync(batchMovementSpec, cancellationToken);

            if (batchMovements.Count == 0)
            {
                _logger.LogWarning(
                    "{LogPrefix} - No batches found for movement: {MovementId}",
                    LogPrefix,
                    request.MovementId
                );
                return Result<UndoBatchMovementResult>.NotFound(
                    $"No se encontraron lotes afectados por el movimiento {request.MovementId}"
                );
            }

            // 3. Validate undo capability
            var validationInfo = await ValidateUndoCapability(movement, batchMovements, cancellationToken);
            if (!validationInfo.CanBeUndone)
            {
                return Result<UndoBatchMovementResult>.Failure(
                    $"Cannot undo movement: {validationInfo.RestrictionReason}"
                );
            }

            // 4. Initialize result
            var result = new UndoBatchMovementResult
            {
                MovementId = movement.Id,
                MovementInfo = movement.MovementInfo ?? string.Empty,
                MovementType = movement.MovementType.Name,
                UndoReason = request.UndoReason,
                UndoDate = DateTime.Now,
                ExecutedBy = request.ExecutedBy,
                ValidationInfo = validationInfo
            };

            // 5. Gather dependent movements (newer than the selected one)
            var dependentMovements = await GetDependentMovements(movement.Id, batchMovements, cancellationToken);

            // 6. Prepare result collections
            var restoredBatches = new List<BatchRestorationInfo>();
            var failedBatches = new List<BatchRestorationInfo>();
            var deletedEntities = new List<string>();

            // 7. Undo dependent movements first (from newest to oldest)
            foreach (var depMovement in dependentMovements.OrderByDescending(m => m.Created))
            {
                await UndoMovement(depMovement, restoredBatches, failedBatches, deletedEntities, cancellationToken);
            }

            // 8. Determine which batches to restore for the selected movement
            var batchesToRestore = request.RestoreAllBatches
                ? batchMovements
                : batchMovements.Where(bm => request.SpecificBatchIds?.Contains(bm.BatchId) == true).ToList();

            if (batchesToRestore.Count == 0)
            {
                return Result<UndoBatchMovementResult>.Failure("No batches selected for restoration");
            }

            // 9. Restore batches for the selected movement
            foreach (var batchMovement in batchesToRestore)
            {
                var batchRestoration = await RestoreBatchToPreviousState(
                    batchMovement.BatchId,
                    request.MovementId,
                    cancellationToken
                );

                if (batchRestoration.RestorationSuccessful)
                {
                    restoredBatches.Add(batchRestoration);
                }
                else
                {
                    failedBatches.Add(batchRestoration);
                }
            }

            result.RestoredBatches = restoredBatches;
            result.FailedBatches = failedBatches;

            // 10. Delete related entities for the selected movement
            var deletedForSelected = await DeleteRelatedEntities(request.MovementId, cancellationToken);
            deletedEntities.AddRange(deletedForSelected);

            // Also remove the selected movement record itself
            await _movementRepository.DeleteAsync(movement, cancellationToken);

            result.DeletedRelatedEntities = deletedEntities;

            // 11. Mark movement as undone (kept for backward compatibility)
            result.MovementMarkedAsUndone = true;

            // 12. Generate summary
            result.Summary = new UndoSummaryInfo
            {
                TotalBatchesAffected = restoredBatches.Count + failedBatches.Count,
                SuccessfullyRestored = restoredBatches.Count,
                FailedToRestore = failedBatches.Count,
                RelatedEntitiesDeleted = deletedEntities.Count,
                TotalFieldsRestored = restoredBatches.Sum(r => r.RestoredFields.Count),
                OperationDurationMs = stopwatch.ElapsedMilliseconds
            };

            // 13. Generate result message
            var successRate =
                batchesToRestore.Count > 0 ? (double)restoredBatches.Count / batchesToRestore.Count * 100 : 0;

            result.Message =
                $"Movement undone successfully. {restoredBatches.Count} of {batchesToRestore.Count} batches restored ({successRate:F1}% success rate).";

            if (failedBatches.Count > 0)
            {
                result.Warnings.Add(
                    $"{failedBatches.Count} batches failed to restore. Check details for specific errors."
                );
            }

            if (validationInfo.ValidationWarnings.Count > 0)
            {
                result.Warnings.AddRange(validationInfo.ValidationWarnings);
            }

            _logger.LogInformation(
                "{LogPrefix} - Successfully undone movement {MovementId}. {SuccessfulCount}/{TotalCount} batches restored.",
                LogPrefix,
                request.MovementId,
                restoredBatches.Count,
                batchesToRestore.Count
            );

            return Result<UndoBatchMovementResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error undoing movement {MovementId}", LogPrefix, request.MovementId);
            return Result<UndoBatchMovementResult>.Failure($"Error undoing movement: {ex.Message}");
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogInformation("{LogPrefix} Time taken: {Duration}ms", LogPrefix, stopwatch.ElapsedMilliseconds);
        }
    }

    private async Task<UndoValidationInfo> ValidateUndoCapability(
        BaseMovement baseMovement,
        List<BatchMovement> batchMovements,
        CancellationToken cancellationToken
    )
    {
        var validationInfo = new UndoValidationInfo
        {
            IsInSap = baseMovement.InSap == true,
            MovementAgeInDays = (int)(DateTime.Now - baseMovement.Created).TotalDays
        };

        // Check if movement has been sent to SAP
        if (validationInfo.IsInSap)
        {
            validationInfo.CanBeUndone = false;
            validationInfo.RestrictionReason = "Movement has been sent to SAP and cannot be undone";
            return validationInfo;
        }

        // Detect dependent movements (newer movements for the same batches)
        var dependentMovements = await GetDependentMovements(baseMovement.Id, batchMovements, cancellationToken);
        validationInfo.HasDependentMovements = dependentMovements.Count > 0;
        validationInfo.DependentMovementIds = [.. dependentMovements.Select(m => m.Id)];

        // Add warning if any will be removed as part of the rollback
        if (validationInfo.HasDependentMovements)
        {
            validationInfo.ValidationWarnings.Add(
                $"{dependentMovements.Count} dependent movements will be removed as part of the rollback operation."
            );
        }

        // Add warnings for potential issues
        if (validationInfo.MovementAgeInDays > 7)
        {
            validationInfo.ValidationWarnings.Add(
                "Movement is older than 7 days. Consider the impact on data integrity."
            );
        }

        if (batchMovements.Count > 100)
        {
            validationInfo.ValidationWarnings.Add(
                $"Movement affects {batchMovements.Count} batches. This is a large operation."
            );
        }

        validationInfo.CanBeUndone = true;
        return validationInfo;
    }

    private async Task<List<BaseMovement>> GetDependentMovements(
        int movementId,
        List<BatchMovement> batchMovements,
        CancellationToken cancellationToken
    )
    {
        var batchIds = batchMovements.Select(bm => bm.BatchId).ToList();
        var movementDate = batchMovements.FirstOrDefault()?.ProcessDate ?? DateTime.MinValue;

        // Get all movements for these batches that happened after the current movement
        var dependentSpec = new DependentMovementsSpec(movementId, movementDate, batchIds);
        var dependentBatchMovements = await _batchMovementRepository.ListAsync(dependentSpec, cancellationToken);

        if (dependentBatchMovements.Count == 0)
        {
            return [];
        }

        // Get the actual movement entities
        var dependentMovementIds = dependentBatchMovements.Select(bm => bm.MovementId).Distinct().ToList();
        var movements = new List<BaseMovement>();

        foreach (var movementNumber in dependentMovementIds)
        {
            var movement = await _movementRepository.GetByIdAsync(movementNumber, cancellationToken);
            if (movement != null)
            {
                movements.Add(movement);
            }
        }

        return movements;
    }

    private async Task<BatchRestorationInfo> RestoreBatchToPreviousState(
        int batchId,
        int movementId,
        CancellationToken cancellationToken
    )
    {
        var restoration = new BatchRestorationInfo
        {
            BatchId = batchId,
            RestorationSuccessful = true
        };

        try
        {
            // Get the batch
            var batch = await _batchRepository.GetByIdAsync(batchId, cancellationToken);
            if (batch == null)
            {
                restoration.RestorationSuccessful = false;
                restoration.ErrorMessage = "Batch not found";
                return restoration;
            }

            restoration.BatchCode = batch.BatchCode;

            // Get audit history for this movement and batch
            var auditSpec = new BatchAuditLogByMovementAndBatchSpec(movementId, batchId);
            var auditLogs = await _batchAuditLogRepository.ListAsync(auditSpec, cancellationToken);

            if (auditLogs.Count == 0)
            {
                // No previous state exists; delete the batch entirely
                await _batchRepository.DeleteAsync(batch, cancellationToken);
                restoration.BatchCode = batch.BatchCode;
                restoration.RestorationSuccessful = true;
                restoration.ErrorMessage = "Batch deleted - no prior state";
                _logger.LogInformation(
                    "{LogPrefix} - Deleted batch {BatchId} as no audit history was found",
                    LogPrefix,
                    batchId
                );
                return restoration;
            }

            // Restore each field modified by this movement
            foreach (var auditLog in auditLogs.OrderBy(x => x.ChangeDate))
            {
                var restored = await RestoreBatchField(batch, auditLog);
                if (restored)
                {
                    restoration.RestoredFields.Add(auditLog.FieldName);
                    restoration.FieldChanges[auditLog.FieldName] = auditLog.OldValue ?? string.Empty;
                }
            }

            // Save the batch changes
            await _batchRepository.UpdateAsync(batch, cancellationToken);

            _logger.LogInformation(
                "{LogPrefix} - Successfully restored batch {BatchId} with {FieldCount} fields",
                LogPrefix,
                batchId,
                restoration.RestoredFields.Count
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error restoring batch {BatchId}", LogPrefix, batchId);
            restoration.RestorationSuccessful = false;
            restoration.ErrorMessage = ex.Message;
        }

        return restoration;
    }

    private Task<bool> RestoreBatchField(Batch batch, BatchAuditLog auditLog)
    {
        try
        {
            switch (auditLog.FieldName.ToLower())
            {
                case "destinationid":
                    if (int.TryParse(auditLog.OldValue, out var destinationId))
                    {
                        batch.DestinationId = destinationId;
                    }

                    break;

                case "marketid":
                    if (int.TryParse(auditLog.OldValue, out var marketId))
                    {
                        batch.MarketId = marketId;
                    }

                    break;

                case "defectid":
                    if (int.TryParse(auditLog.OldValue, out var defectId))
                    {
                        batch.DefectId = defectId;
                    }

                    break;

                case "bathid":
                    if (int.TryParse(auditLog.OldValue, out var bathId))
                    {
                        batch.BathId = bathId;
                    }

                    break;

                case "strapid":
                    if (int.TryParse(auditLog.OldValue, out var strapId))
                    {
                        batch.StrapId = strapId;
                    }

                    break;

                case "statusid":
                    if (int.TryParse(auditLog.OldValue, out var statusId))
                    {
                        batch.StatusId = statusId;
                    }

                    break;

                case "warehouseid":
                    if (string.IsNullOrEmpty(auditLog.OldValue))
                    {
                        batch.WarehouseId = null;
                    }
                    else if (int.TryParse(auditLog.OldValue, out var warehouseId))
                    {
                        batch.WarehouseId = warehouseId;
                    }

                    break;

                case "isstacking":
                    if (bool.TryParse(auditLog.OldValue, out var isStacking))
                    {
                        batch.IsStacking = isStacking;
                    }

                    break;

                case "isht":
                    if (bool.TryParse(auditLog.OldValue, out var isHt))
                    {
                        batch.IsHt = isHt;
                    }

                    break;

                case "ispainting":
                    if (bool.TryParse(auditLog.OldValue, out var isPainting))
                    {
                        batch.IsPainting = isPainting;
                    }

                    break;

                case "isseparating":
                    if (bool.TryParse(auditLog.OldValue, out var isSeparating))
                    {
                        batch.IsSeparating = isSeparating;
                    }

                    break;

                case "istrimmed":
                    if (bool.TryParse(auditLog.OldValue, out var isTrimmed))
                    {
                        batch.IsTrimmed = isTrimmed;
                    }

                    break;

                case "iswoodenblock":
                    if (bool.TryParse(auditLog.OldValue, out var isWoodenBlock))
                    {
                        batch.IsWoodenBlock = isWoodenBlock;
                    }

                    break;

                case "status":
                    batch.Status = auditLog.OldValue;
                    break;

                default:
                    _logger.LogWarning(
                        "{LogPrefix} - Unknown field for restoration: {FieldName}",
                        LogPrefix,
                        auditLog.FieldName
                    );
                    return Task.FromResult(false);
            }

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{LogPrefix} - Error restoring field {FieldName} for batch {BatchId}",
                LogPrefix,
                auditLog.FieldName,
                batch.Id
            );
            return Task.FromResult(false);
        }
    }

    private async Task<List<string>> DeleteRelatedEntities(int movementId, CancellationToken cancellationToken)
    {
        var deletedEntities = new List<string>();

        try
        {
            // Delete chemical bath movements
            var chemicalBathSpec = new ChemicalBathMovementByMovementIdSpec(movementId);
            var chemicalBathMovements = await _chemicalBathMovementRepository.ListAsync(
                chemicalBathSpec,
                cancellationToken
            );
            foreach (var chemicalBath in chemicalBathMovements)
            {
                await _chemicalBathMovementRepository.DeleteAsync(chemicalBath, cancellationToken);
                deletedEntities.Add($"ChemicalBathMovement-{chemicalBath.Id}");
            }

            // Delete productions
            var productionSpec = new BatchProductionByMovementIdSpec(movementId);
            var productions = await _batchProductionRepository.ListAsync(productionSpec, cancellationToken);
            foreach (var production in productions)
            {
                // Detach navigation to avoid tracking duplicate entities
                production.BatchMovement = null;
                await _batchProductionRepository.DeleteAsync(production, cancellationToken);
                deletedEntities.Add($"BatchProduction-{production.Id}");
            }

            // Delete stackings
            var stackingSpec = new StackingByMovementIdSpec(movementId);
            var stackings = await _stackingRepository.ListAsync(stackingSpec, cancellationToken);
            foreach (var stacking in stackings)
            {
                await _stackingRepository.DeleteAsync(stacking, cancellationToken);
                deletedEntities.Add($"StackingEntity-{stacking.Id}");
            }

            // Delete batch movements
            var batchMovementSpec = new BatchMovementByMovementIdSpec(movementId);
            var batchMovements = await _batchMovementRepository.ListAsync(batchMovementSpec, cancellationToken);
            foreach (var batchMovement in batchMovements)
            {
                // Detach navigation properties to prevent duplicate BatchEntity instances being tracked
                batchMovement.Batch = null;
                batchMovement.Movement = null;
                await _batchMovementRepository.DeleteAsync(batchMovement, cancellationToken);
                deletedEntities.Add($"BatchMovement-{batchMovement.Id}");
            }

            // Delete batch audit logs FIRST to avoid FK conflicts with movements
            var auditSpec = new BatchAuditLogByMovementSpec(movementId);
            var auditLogs = await _batchAuditLogRepository.ListAsync(auditSpec, cancellationToken);
            foreach (var auditLog in auditLogs)
            {
                auditLog.Batch = null; // detach to prevent duplicate BatchEntity tracking
                await _batchAuditLogRepository.DeleteAsync(auditLog, cancellationToken);
                deletedEntities.Add($"BatchAuditLog-{auditLog.Id}");
            }

            // Delete log consumptions
            var logSpec = new LogConsumptionByMovementIdSpec(movementId);
            var logConsumptions = await _logConsumptionRepository.ListAsync(logSpec, cancellationToken);
            foreach (var log in logConsumptions)
            {
                await _logConsumptionRepository.DeleteAsync(log, cancellationToken);
                deletedEntities.Add($"LogConsumption-{log.Id}");
            }

            // Delete warehouse changes
            var whcSpec = new MovWareHouseChangeByMovementIdSpec(movementId);
            var whChanges = await _movWareHouseChangeRepository.ListAsync(whcSpec, cancellationToken);
            foreach (var wh in whChanges)
            {
                await _movWareHouseChangeRepository.DeleteAsync(wh, cancellationToken);
                deletedEntities.Add($"MovWareHouseChange-{wh.Id}");
            }

            // Delete drying-entrance records
            var dryingEntranceSpec = new DryingCycleDetailByMovementIdSpec(movementId);
            var dryingEntrances = await _dryingCycleDetailsRepository.ListAsync(dryingEntranceSpec, cancellationToken);
            foreach (var de in dryingEntrances)
            {
                await _dryingCycleDetailsRepository.DeleteAsync(de, cancellationToken);
                deletedEntities.Add($"DryingCycleDetail-{de.Id}");
            }

            // Delete mov stacking rows
            var movStackingSpec = new MovStackingByMovementIdSpec(movementId);
            var movStackings = await _movStackingRepository.ListAsync(movStackingSpec, cancellationToken);
            foreach (var ms in movStackings)
            {
                await _movStackingRepository.DeleteAsync(ms, cancellationToken);
                deletedEntities.Add($"MovStacking-{ms.Id}");
            }

            _logger.LogInformation(
                "{LogPrefix} - Deleted {Count} related entities for movement {MovementId}",
                LogPrefix,
                deletedEntities.Count,
                movementId
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{LogPrefix} - Error deleting related entities for movement {MovementId}",
                LogPrefix,
                movementId
            );
        }

        return deletedEntities;
    }

    private async Task UndoMovement(
        BaseMovement baseMovement,
        List<BatchRestorationInfo> restoredBatches,
        List<BatchRestorationInfo> failedBatches,
        List<string> deletedEntities,
        CancellationToken cancellationToken
    )
    {
        // Get batch movements for this movement
        var batchMovementSpec = new BatchMovementByMovementIdSpec(baseMovement.Id);
        var batchMovements = await _batchMovementRepository.ListAsync(batchMovementSpec, cancellationToken);

        foreach (var batchMovement in batchMovements)
        {
            var restoration = await RestoreBatchToPreviousState(
                batchMovement.BatchId,
                baseMovement.Id,
                cancellationToken
            );

            if (restoration.RestorationSuccessful)
            {
                restoredBatches.Add(restoration);
            }
            else
            {
                failedBatches.Add(restoration);
            }
        }

        // Delete related entities for this movement
        var deleted = await DeleteRelatedEntities(baseMovement.Id, cancellationToken);
        deletedEntities.AddRange(deleted);

        // Finally delete the movement itself
        await _movementRepository.DeleteAsync(baseMovement, cancellationToken);
    }
}
