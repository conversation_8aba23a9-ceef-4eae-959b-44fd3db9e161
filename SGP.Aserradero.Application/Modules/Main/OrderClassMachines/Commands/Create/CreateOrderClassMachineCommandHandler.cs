using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Main.Machines;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses;
using SGP.Aserradero.Domain.Entities.Main.OrderClassMachines;
using SGP.Aserradero.Domain.Entities.Main.OrderClassMachines.Specs;

namespace SGP.Aserradero.Application.Modules.Main.OrderClassMachines.Commands.Create;

public sealed class CreateOrderClassMachineCommandHandler
    : IRequestHandler<CreateOrderClassMachineCommand, Result<bool>>
{
    private const string LogPrefix = "[Aserradero][OrderClassMachine][Create]";
    private readonly ILogger<CreateOrderClassMachineCommandHandler> _logger;
    private readonly IRepository<Machine> _machineRepo;
    private readonly IRepository<OrderClass> _orderClassRepo;
    private readonly IRepository<OrderClassMachine> _repository;

    public CreateOrderClassMachineCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<OrderClassMachine>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateOrderClassMachineCommandHandler>>();
        _orderClassRepo = serviceProvider.GetRequiredService<IRepository<OrderClass>>();
        _machineRepo = serviceProvider.GetRequiredService<IRepository<Machine>>();
    }

    public async Task<Result<bool>> Handle(CreateOrderClassMachineCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"{LogPrefix} - Executing");
        try
        {
            // Valdidar los datos de entrada
            if (request.OrderId <= 0 || request.MachineId <= 0)
            {
                return Result<bool>.Failure("Error creating relation order class - machine");
            }

            // Validar si la orden existe
            var orderClass = await _orderClassRepo.GetByIdAsync(request.OrderId, cancellationToken);
            if (orderClass == null)
            {
                return Result<bool>.Failure("Order class not found", 404);
            }

            // Validar si la maquina existe
            var machine = await _machineRepo.GetByIdAsync(request.MachineId, cancellationToken);
            if (machine == null)
            {
                return Result<bool>.Failure("Machine not found", 404);
            }

            // Validar si la orden y la maquina no están asociadas a la misma clase orden
            var orderMachineSpec = new OrderClassMachineByBothIdSpec(orderClass.Id, machine.Id);
            if (await _repository.AnyAsync(orderMachineSpec, cancellationToken))
            {
                return Result<bool>.Failure(
                    "The order and machine are already associated with the same order class",
                    409
                );
            }

            var instance = OrderClassMachine.Create(request.OrderId, request.MachineId, request.IsActive);
            await _repository.AddAsync(instance, cancellationToken);
            return Result<bool>.Success(true, "Relation order class - machine created successfully", 201);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "{@LogPrefix} - Error creating relation order class - machine", LogPrefix);
            return Result<bool>.Failure("Error creating relation order class - machine", 500);
        }
    }
}
