using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Base.Specs;

namespace SGP.Aserradero.Application.Modules.Main.Batchs.Queries.GetAll;

public sealed class GetAllBatchQueryHandler
    : IRequestHandler<GetAllBatchQuery, Result<PagedResult<GetAllBatchQueryResponse>>>
{
    private const string LogPrefix = "[Batch][GetAll]";
    private readonly IRepository<Batch> _batchRepository;
    private readonly ILogger<GetAllBatchQueryHandler> _logger;

    public GetAllBatchQueryHandler(IServiceProvider serviceProvider)
    {
        _batchRepository = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetAllBatchQueryHandler>>();
    }

    public async Task<Result<PagedResult<GetAllBatchQueryResponse>>> Handle(
        GetAllBatchQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Obteniendo todos los lotes", LogPrefix);
        try
        {
            var spec = new BatchsWithDataSpec(request.Page, request.PageSize);
            var batches = await _batchRepository.ListAsync(spec, cancellationToken);
            var total = await _batchRepository.CountAsync(cancellationToken);

            var batchDtos = batches
                .Select(batch => new GetAllBatchQueryResponse
                {
                    Id = batch.Id,
                    BatchCode = batch.BatchCode,
                    MaterialId = batch.MaterialId,
                    MaterialSapCode = batch.Material?.SapCode,
                    MaterialDescription = batch.Material?.Description,
                    Status = batch.Status,
                    DestinationId = batch.DestinationId,
                    DestinationCode = batch.Destination?.Code,
                    DestinationName = batch.Destination?.Name,
                    MarketId = batch.MarketId,
                    MarketCode = batch.Market?.Code,
                    MarketName = batch.Market?.Name,
                    DefectId = batch.DefectId,
                    DefectCode = batch.Defect?.Code,
                    DefectName = batch.Defect?.Name,
                    BathId = batch.BathId,
                    BathCode = batch.Bath?.Code,
                    BathName = batch.Bath?.Name,
                    StrapId = batch.StrapId,
                    StrapCode = batch.Strap?.Code,
                    StrapName = batch.Strap?.Name,
                    StatusId = batch.StatusId,
                    StatusName = batch.StatusEntity?.Name,
                    IsStacking = batch.IsStacking,
                    IsHt = batch.IsHt,
                    IsPainting = batch.IsPainting,
                    IsSeparating = batch.IsSeparating,
                    IsTrimmed = batch.IsTrimmed,
                    IsWoodenBlock = batch.IsWoodenBlock,
                    WarehouseId = batch.WarehouseId,
                    WarehouseCode = batch.Warehouse?.Code,
                    WarehouseName = batch.Warehouse?.Name,
                    CreatedDate = batch.CreatedDate,
                    CreatedBy = batch.CreatedBy,
                    LastModified = batch.LastModified,
                    LastModifiedBy = batch.LastModifiedBy,
                })
                .ToList();

            var response = new PagedResult<GetAllBatchQueryResponse>(batchDtos, total, request.Page, request.PageSize);

            return Result<PagedResult<GetAllBatchQueryResponse>>.Success(response, "Lotes obtenidos correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al obtener todos los lotes", LogPrefix);
            return Result<PagedResult<GetAllBatchQueryResponse>>.InternalError(
                $"Ha ocurrido un error interno al obtener todos los lotes\n{ex.Message}"
            );
        }
    }
}
