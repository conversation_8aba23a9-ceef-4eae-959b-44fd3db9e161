using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Main.Printers.DTOs;

namespace SGP.Aserradero.Application.Modules.Main.Printers.Commands.CreatePrinter;

public sealed record CreatePrinterCommand : IRequest<Result<PrinterDto>>
{
    public string Name { get; init; }
    public string Location { get; init; }
    public string PrinterName { get; init; }
    public int PrinterDpi { get; init; }
    public string IpAddress { get; init; }
    public bool IsActive { get; init; }
}
