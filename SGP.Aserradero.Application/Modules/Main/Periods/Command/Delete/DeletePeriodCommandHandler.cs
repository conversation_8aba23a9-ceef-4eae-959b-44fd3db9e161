using Leonera_API.Common.Core.Domain;
using MediatR;
using SGP.Aserradero.Domain.Entities.Main.Periods;

namespace SGP.Aserradero.Application.Modules.Main.Periods.Command.Delete;

public sealed class DeletePeriodCommandHandler(IRepository<Period> repository)
    : IRequestHandler<DeletePeriodCommand, DeletePeriodResponse>
{
    public async Task<DeletePeriodResponse> Handle(DeletePeriodCommand request, CancellationToken cancellationToken)
    {
        var period = await repository.GetByIdAsync(request.PeriodId, cancellationToken);
        if (period is null)
        {
            return new DeletePeriodResponse("error", "Period not found");
        }

        if (period.CreatedBy != request.UserId)
        {
            return new DeletePeriodResponse("error", "You are not authorized to delete this period");
        }

        period.Delete(request.UserId);
        await repository.UpdateAsync(period, cancellationToken);
        return new DeletePeriodResponse("success", "Period deleted successfully");
    }
}
