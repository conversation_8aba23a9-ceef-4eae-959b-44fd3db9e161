using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Main.Periods;

namespace SGP.Aserradero.Application.Modules.Main.Periods.Command.Update;

public sealed class UpdatePeriodCommandHandler(IRepository<Period> repository)
    : IRequestHandler<UpdatePeriodCommand, Result<Period>>
{
    public async Task<Result<Period>> Handle(UpdatePeriodCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var period = await repository.GetByIdAsync(request.PeriodId, cancellationToken);
            if (period is null)
            {
                return Result<Period>.Failure("Period not found", 404);
            }

            period.Update(request.UserId, request.StartDate, request.EndDate, request.IsActive);
            await repository.UpdateAsync(period, cancellationToken);
            return Result<Period>.Success(period, "Period updated successfully");
        }
        catch (Exception e)
        {
            return Result<Period>.Failure($"Period Update Failed: {e.Message}", 500);
        }
    }
}
