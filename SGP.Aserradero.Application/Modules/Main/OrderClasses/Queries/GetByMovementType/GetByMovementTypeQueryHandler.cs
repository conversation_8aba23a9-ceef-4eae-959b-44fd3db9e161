using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Dtos;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses.Specs;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Main.OrderClasses.Queries.GetByMovementType;

public sealed class GetByMovementTypeQueryHandler : IRequestHandler<GetByMovementTypeQuery, Result<List<OrderClassDto>>>
{
    private const string LogPrefix = "[OrderClass][GetByMovementType]";
    private readonly ILogger<GetByMovementTypeQueryHandler> _logger;
    private readonly IRepository<OrderClass> _repository;
    private readonly IRepository<MovementType> _movementTypeRepository;

    public GetByMovementTypeQueryHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<OrderClass>>();
        _movementTypeRepository = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetByMovementTypeQueryHandler>>();
    }

    public async Task<Result<List<OrderClassDto>>> Handle(
        GetByMovementTypeQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Starting Order Class Retrieval by Movement Type", LogPrefix);
        try
        {
            // Obtener tipo de movimiento segun su nombre
            var movementTypeByNameSpec = new MovementTypeByNameSpec(request.MovementType);
            var movementType = await _movementTypeRepository.FirstOrDefaultAsync(
                movementTypeByNameSpec,
                cancellationToken
            );
            if (movementType == null)
            {
                return Result<List<OrderClassDto>>.NotFound($"Movimiento {request.MovementType} no encontrado");
            }

            // Use a specification without includes to avoid circular references
            var spec = new OrderClassByMovementTypeSpec(movementType.Id);
            var orderClasses = await _repository.ListAsync(spec, cancellationToken);

            // Project to DTOs to avoid circular reference issues
            var orderClassDtos = orderClasses
                .Select(oc => new OrderClassDto
                {
                    Id = oc.Id,
                    Code = oc.Code,
                    Name = oc.Name,
                    Position = oc.Position,
                    StatusConsumption = oc.StatusConsumption,
                    StatusProduction = oc.StatusProduction,
                    IsActive = oc.IsActive,
                    Created = oc.Created,
                    LastModified = oc.LastModified,
                    MovementTypes =
                        oc.MovementTypeOrderClasses?.Select(mt => new MovementTypeDto
                            {
                                Id = mt.MovementType?.Id ?? 0,
                                Name = mt.MovementType?.Name ?? string.Empty,
                                Description = mt.MovementType?.Description ?? string.Empty,
                                IsActive = mt.MovementType?.IsActive ?? false,
                            })
                            .ToList() ?? [],
                })
                .ToList();

            return Result<List<OrderClassDto>>.Success(orderClassDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error retrieving Order Class by Movement Type", LogPrefix);
            return Result<List<OrderClassDto>>.InternalError(
                "Ha ocurrido un error interno al obtener las clases de orden por tipo de movimiento"
            );
        }
    }
}
