using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Main.OrderClasses;

namespace SGP.Aserradero.Application.Modules.Main.OrderClasses.Commands.Delete;

public sealed class DeleteOrderClassCommandHandler : IRequestHandler<DeleteOrderClassCommand, Result<bool>>
{
    private const string LogPrefix = "[OrderClass][Delete]";
    private readonly ILogger<DeleteOrderClassCommandHandler> _logger;
    private readonly IRepository<OrderClass> _repository;

    public DeleteOrderClassCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<OrderClass>>();
        _logger = serviceProvider.GetRequiredService<ILogger<DeleteOrderClassCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(DeleteOrderClassCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} - Starting Order Class Deletion", LogPrefix);
        try
        {
            var entity = await _repository.GetByIdAsync(request.OrderId, cancellationToken);
            if (entity == null)
            {
                return Result<bool>.Failure("Order Class Not Found", 404);
            }

            entity.Delete(request.OrderId);
            _ = await _repository.UpdateAsync(entity, cancellationToken);
            return Result<bool>.Success(true, "Order Class Deleted Successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error deleting Order Class", LogPrefix);
            return Result<bool>.Failure($"Order Class Deletion Failed: {ex.Message}", 500);
        }
    }
}
