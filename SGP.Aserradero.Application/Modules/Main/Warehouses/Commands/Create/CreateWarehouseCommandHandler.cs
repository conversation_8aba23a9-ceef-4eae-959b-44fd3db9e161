using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Modules.Main.Warehouses.DTOs;
using SGP.Aserradero.Domain.Entities.Main.Warehouses;
using SGP.Aserradero.Domain.Entities.Main.Warehouses.Specs;
using SGP.Aserradero.Domain.Entities.Main.WarehouseStatuses;
using SGP.Aserradero.Domain.Entities.Main.WarehouseStatusRels;

namespace SGP.Aserradero.Application.Modules.Main.Warehouses.Commands.Create;

public class CreateWarehouseCommandHandler : IRequestHandler<CreateWarehouseCommand, Result<WarehouseResponseDto>>
{
    private const string LogPrefix = "[Parameters][Warehouse][Create]";
    private readonly ILogger<CreateWarehouseCommandHandler> _logger;
    private readonly IRepository<WarehouseStatusRel> _relationRepository;
    private readonly IRepository<Warehouse> _repository;
    private readonly IRepository<WarehouseStatus> _statusRepo;

    public CreateWarehouseCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Warehouse>>();
        _relationRepository = serviceProvider.GetRequiredService<IRepository<WarehouseStatusRel>>();
        _statusRepo = serviceProvider.GetRequiredService<IRepository<WarehouseStatus>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateWarehouseCommandHandler>>();
    }

    public async Task<Result<WarehouseResponseDto>> Handle(
        CreateWarehouseCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation(
            "{LogPrefix} - Handling CreateWarehouseCommand for UserId: {UserId}",
            LogPrefix,
            request.UserId
        );
        try
        {
            var statuses = await _statusRepo.ListAsync(cancellationToken);

            var warehouse = Warehouse.Create(request.Code, request.Name, request.MaterialClassId, request.UserId);

            await _repository.AddAsync(warehouse, cancellationToken);

            // Create Status Relations for Warehouse based on request booleans one boolean at a time
            List<WarehouseStatusRel> relations = [];

            if (request.IsProcess)
            {
                var relation = WarehouseStatusRel.Create(warehouse.Id, statuses.First(x => x.Name == "Proceso").Id);
                relations.Add(relation);
            }

            if (request.IsFinish)
            {
                var relation = WarehouseStatusRel.Create(warehouse.Id, statuses.First(x => x.Name == "Terminado").Id);
                relations.Add(relation);
            }

            if (request.IsReject)
            {
                var relation = WarehouseStatusRel.Create(warehouse.Id, statuses.First(x => x.Name == "Rechazo").Id);
                relations.Add(relation);
            }

            if (request.IsTransit)
            {
                var relation = WarehouseStatusRel.Create(warehouse.Id, statuses.First(x => x.Name == "Transito").Id);
                relations.Add(relation);
            }

            if (request.IsLimbo)
            {
                var relation = WarehouseStatusRel.Create(warehouse.Id, statuses.First(x => x.Name == "Limbo").Id);
                relations.Add(relation);
            }

            // Create relations
            if (relations.Any())
            {
                await _relationRepository.AddRangeAsync(relations, cancellationToken);
            }

            // Get Created Warehouse
            var spec = new WarehouseByIdSpec(warehouse.Id);
            var warehouseDto = await _repository.FirstOrDefaultAsync(spec, cancellationToken);

            return Result<WarehouseResponseDto>.Success(
                new WarehouseResponseDto
                {
                    Id = warehouseDto.Id,
                    Name = warehouseDto.Name,
                    Code = warehouseDto.Code,
                    MaterialClassId = warehouseDto.MaterialClassId,
                    MaterialClassCode = warehouseDto.MaterialClass.Code,
                    MaterialClassName = warehouseDto.MaterialClass.Name,
                    IsProcess = warehouseDto.WarehouseStatuses.Any(y => y.WarehouseStatus.Name == "Proceso"),
                    IsFinish = warehouseDto.WarehouseStatuses.Any(y => y.WarehouseStatus.Name == "Terminado"),
                    IsReject = warehouseDto.WarehouseStatuses.Any(y => y.WarehouseStatus.Name == "Rechazo"),
                    IsTransit = warehouseDto.WarehouseStatuses.Any(y => y.WarehouseStatus.Name == "Transito"),
                    IsLimbo = warehouseDto.WarehouseStatuses.Any(y => y.WarehouseStatus.Name == "Limbo"),
                },
                "Warehouse Created Successfully",
                201
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error creating warehouse: {Message}", LogPrefix, ex.Message);
            return Result<WarehouseResponseDto>.Failure("Error creating warehouse", 500);
        }
    }
}
