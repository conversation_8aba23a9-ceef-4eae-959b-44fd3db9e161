using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Main.Warehouses.DTOs;

namespace SGP.Aserradero.Application.Modules.Main.Warehouses.Queries.GetAll;

public record GetAllWarehousesQuery : IRequest<Result<PagedResult<WarehouseResponseDto>>>
{
    public int? MaterialClassId { get; init; }
    public int Page { get; init; } = 1;
    public int PageSize { get; init; } = 10;
}
