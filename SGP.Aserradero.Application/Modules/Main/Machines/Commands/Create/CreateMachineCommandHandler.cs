using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Main.Machines;
using SGP.Aserradero.Domain.Entities.Main.Machines.Specs;

namespace SGP.Aserradero.Application.Modules.Main.Machines.Commands.Create;

public sealed class CreateMachineCommandHandler : IRequestHandler<CreateMachineCommand, Result<Machine>>
{
    private const string LogPrefix = "[Machine][Create]";
    private readonly ILogger<CreateMachineCommandHandler> _logger;
    private readonly IRepository<Machine> _repository;

    public CreateMachineCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Machine>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMachineCommandHandler>>();
    }

    public async Task<Result<Machine>> Handle(CreateMachineCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Creando maquina con codigo {Code}", LogPrefix, request.Code);
        try
        {
            // Validar si el codigo no existe
            var spec = new MachineByCodeSpec(request.Code);
            if (await _repository.AnyAsync(spec, cancellationToken))
            {
                return Result<Machine>.Conflict("El código de la maquina ya existe");
            }

            // Crear la maquina
            var instance = Machine.Create(request.Code, request.Name, request.CreatedBy);
            await _repository.AddAsync(instance, cancellationToken);

            return Result<Machine>.Success(instance, "Maquina creada correctamente", 201);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "{LogPrefix} Error al crear la maquina", LogPrefix);
            return Result<Machine>.InternalError("Ha ocurrido un error interno al crear la maquina\n" + e.Message);
        }
    }
}
