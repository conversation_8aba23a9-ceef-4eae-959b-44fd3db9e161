using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Main.Documents;

namespace SGP.Aserradero.Application.Modules.Main.Documents.Commands.Delete;

public class DeleteDocumentCommandHandler : IRequestHandler<DeleteDocumentCommand, Result<bool>>
{
    private readonly ILogger<DeleteDocumentCommandHandler> _logger;
    private readonly IRepository<Document> _repository;
    private const string LogPrefix = "[Document][Delete]";

    public DeleteDocumentCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Document>>();
        _logger = serviceProvider.GetRequiredService<ILogger<DeleteDocumentCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(DeleteDocumentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{LogPrefix} - Iniciando la eliminación del documento con ID {Id}",
            LogPrefix,
            request.DocumentId
        );
        try
        {
            var document = await _repository.GetByIdAsync(request.DocumentId, cancellationToken);
            if (document == null)
            {
                return Result<bool>.NotFound($"Documento con ID {request.DocumentId} no encontrado");
            }

            await _repository.DeleteAsync(document, cancellationToken);
            return Result<bool>.Success(true, "Documento eliminado correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{LogPrefix} Error al eliminar el documento con ID {Id}",
                LogPrefix,
                request.DocumentId
            );
            return Result<bool>.InternalError("Ha ocurrido un error interno al eliminar el documento\n" + ex.Message);
        }
    }
}
