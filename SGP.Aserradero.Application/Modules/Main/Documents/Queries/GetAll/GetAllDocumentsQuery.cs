using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Main.Documents.Shared;

namespace SGP.Aserradero.Application.Modules.Main.Documents.Queries.GetAll;

public sealed record GetAllDocumentsQuery : IRequest<Result<PagedResult<GetDocumentResponse>>>
{
    /// <summary>
    /// Número de página
    /// </summary>
    public int Page { get; init; } = 1;

    /// <summary>
    /// Tamaño de la página
    /// </summary>
    public int PageSize { get; init; } = 20;
}
