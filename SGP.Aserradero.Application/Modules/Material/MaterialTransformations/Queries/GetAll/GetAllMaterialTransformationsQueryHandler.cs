using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialTransformations;
using SGP.Aserradero.Domain.Entities.Material.MaterialTransformations.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialTransformations.Queries.GetAll;

/// <summary>
/// Handler para Query GetAll MaterialTransformations.
/// </summary>
public class GetAllMaterialTransformationsQueryHandler
    : IRequestHandler<
        GetAllMaterialTransformationsQuery,
        Result<PagedResult<GetAllMaterialTransformationsQueryResponse>>
    >
{
    private const string LogPrefix = "[Material][Transformation][GetAll]";
    private readonly ILogger<GetAllMaterialTransformationsQueryHandler> _logger;
    private readonly IRepository<MaterialTransformation> _repository;

    public GetAllMaterialTransformationsQueryHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialTransformation>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetAllMaterialTransformationsQueryHandler>>();
    }

    public async Task<Result<PagedResult<GetAllMaterialTransformationsQueryResponse>>> Handle(
        GetAllMaterialTransformationsQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Obteniendo transformaciones de material", LogPrefix);
        try
        {
            var pagedDataSpec = new MaterialTransformationPaginatedSpec(request.Page, request.PageSize);
            var materialTransformations = await _repository.ListAsync(pagedDataSpec, cancellationToken);
            var materialTransformationsCount = await _repository.CountAsync(pagedDataSpec, cancellationToken);

            var response = new PagedResult<GetAllMaterialTransformationsQueryResponse>(
                [.. materialTransformations.Select(x => new GetAllMaterialTransformationsQueryResponse(x))],
                materialTransformationsCount,
                request.Page,
                request.PageSize
            );
            return Result<PagedResult<GetAllMaterialTransformationsQueryResponse>>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al obtener transformaciones de material", LogPrefix);
            return Result<PagedResult<GetAllMaterialTransformationsQueryResponse>>.InternalError(
                "Ha ocurrido un error interno al obtener transformaciones de material"
            );
        }
    }
}
