using Leonera_API.Common.Core.Models;
using MediatR;

namespace SGP.Aserradero.Application.Modules.Material.MaterialTransformations.Queries.GetByFilter;

/// <summary>
/// Query para GetByFilter MaterialTransformations.
/// </summary>
public class GetByFilterMaterialTransformationsQuery : IRequest<Result<GetByFilterMaterialTransformationsQueryResponse>>
{
    // Agrega aquí las propiedades necesarias
}
