using FluentValidation;

namespace SGP.Aserradero.Application.Modules.Material.MaterialTransformations.Commands.Delete;

/// <summary>
/// Validador para Command Delete MaterialTransformations.
/// </summary>
public class DeleteMaterialTransformationsCommandValidator : AbstractValidator<DeleteMaterialTransformationsCommand>
{
    public DeleteMaterialTransformationsCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty().WithMessage("El Id es obligatorio");
        RuleFor(x => x.Id).GreaterThan(0).WithMessage("El Id debe ser mayor a 0");
    }
}
