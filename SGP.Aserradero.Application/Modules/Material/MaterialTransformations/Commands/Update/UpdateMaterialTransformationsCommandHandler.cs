using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialTransformations;

namespace SGP.Aserradero.Application.Modules.Material.MaterialTransformations.Commands.Update;

/// <summary>
/// Handler para Command Update MaterialTransformations.
/// </summary>
public class UpdateMaterialTransformationsCommandHandler
    : IRequestHandler<UpdateMaterialTransformationsCommand, Result<bool>>
{
    private const string LogPrefix = "[Material][Transformation][Update]";
    private readonly ILogger<UpdateMaterialTransformationsCommandHandler> _logger;
    private readonly IRepository<MaterialTransformation> _repository;

    public UpdateMaterialTransformationsCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialTransformation>>();
        _logger = serviceProvider.GetRequiredService<ILogger<UpdateMaterialTransformationsCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(
        UpdateMaterialTransformationsCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Actualizando transformacion de material", LogPrefix);
        try
        {
            var materialTransformation = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (materialTransformation == null)
            {
                return Result<bool>.NotFound("Transformacion de material no encontrada");
            }
            materialTransformation.Update(
                request.MaterialId,
                request.MaterialTransformationTypeId,
                request.OrderClassId
            );
            await _repository.UpdateAsync(materialTransformation, cancellationToken);
            return Result<bool>.Success(true, "Transformacion de material actualizada exitosamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al actualizar transformacion de material", LogPrefix);
            return Result<bool>.InternalError("Ha ocurrido un error interno al actualizar transformacion de material");
        }
    }
}
