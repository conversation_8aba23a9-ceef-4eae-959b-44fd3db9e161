using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialClasses;
using SGP.Aserradero.Domain.Entities.Material.MaterialClasses.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialClasses.Commands.Create;

public sealed class CreateMaterialClassCommandHandler
    : IRequestHandler<CreateMaterialClassCommand, Result<CreateMaterialClassResponse>>
{
    private const string LogPrefix = "[Material][Class][Create]";
    private readonly ILogger<CreateMaterialClassCommandHandler> _logger;
    private readonly IRepository<MaterialClass> _repository;

    public CreateMaterialClassCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialClass>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMaterialClassCommandHandler>>();
    }

    public async Task<Result<CreateMaterialClassResponse>> Handle(
        CreateMaterialClassCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Start creating material class", LogPrefix);
        try
        {
            // Validate if MaterialClass already exists
            var materialClassSpec = new MaterialClassByNameSpec(request.Name);
            var existingMaterialClass = await _repository.FirstOrDefaultAsync(materialClassSpec, cancellationToken);
            if (existingMaterialClass != null)
            {
                _logger.LogWarning("{LogPrefix} MaterialClass already exists", LogPrefix);
                return Result<CreateMaterialClassResponse>.Failure("La clase de material ya existe");
            }

            var materialClassSpecByCode = new MaterialClassByCodeSpec(request.Code);
            var existingMaterialClassByCode = await _repository.FirstOrDefaultAsync(
                materialClassSpecByCode,
                cancellationToken
            );
            if (existingMaterialClassByCode != null)
            {
                _logger.LogWarning("{LogPrefix} MaterialClass already exists", LogPrefix);
                return Result<CreateMaterialClassResponse>.Failure("La clase de material ya existe");
            }

            var newClass = MaterialClass.Create(request.Code, request.Name);
            await _repository.AddAsync(newClass, cancellationToken);
            return Result<CreateMaterialClassResponse>.Success(
                new CreateMaterialClassResponse("OK", "Clase de material creada correctamente")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error creating material class", LogPrefix);
            return Result<CreateMaterialClassResponse>.Failure($"Error al crear la clase de material {ex.Message}");
        }
    }
}
