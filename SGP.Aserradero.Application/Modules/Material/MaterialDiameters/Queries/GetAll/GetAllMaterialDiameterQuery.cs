using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialDiameters;

namespace SGP.Aserradero.Application.Modules.Material.MaterialDiameters.Queries.GetAll;

public sealed record GetAllMaterialDiameterQuery : IRequest<Result<PagedResult<MaterialDiameter>>>
{
    public int Page { get; init; }
    public int PageSize { get; init; }
}
