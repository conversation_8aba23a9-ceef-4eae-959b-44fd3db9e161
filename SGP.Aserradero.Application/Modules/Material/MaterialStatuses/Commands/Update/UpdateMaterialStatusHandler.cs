using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialStatuses;

namespace SGP.Aserradero.Application.Modules.Material.MaterialStatuses.Commands.Update;

public class UpdateMaterialStatusHandler(IRepository<MaterialStatus> repository)
    : IRequestHandler<UpdateMaterialStatusCommand, Result<bool>>
{
    public async Task<Result<bool>> Handle(UpdateMaterialStatusCommand request, CancellationToken cancellationToken)
    {
        var materialStatus = await repository.GetByIdAsync(request.Id, cancellationToken);
        if (materialStatus == null)
        {
            return Result<bool>.Failure($"MaterialStatus with ID {request.Id} not found", 404);
        }

        materialStatus.Update(request.Name, request.Code);
        await repository.UpdateAsync(materialStatus, cancellationToken);
        return Result<bool>.Success(true);
    }
}
