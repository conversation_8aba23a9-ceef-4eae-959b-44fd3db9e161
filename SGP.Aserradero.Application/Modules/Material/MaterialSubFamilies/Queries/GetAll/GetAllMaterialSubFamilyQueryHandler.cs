using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialSubFamilies;
using SGP.Aserradero.Domain.Entities.Material.MaterialSubFamilies.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialSubFamilies.Queries.GetAll;

public sealed class GetAllMaterialSubFamilyQueryHandler(IRepository<MaterialSubFamily> repository)
    : IRequestHandler<GetAllMaterialSubFamilyQuery, Result<PagedResult<MaterialSubFamily>>>
{
    public async Task<Result<PagedResult<MaterialSubFamily>>> Handle(
        GetAllMaterialSubFamilyQuery request,
        CancellationToken cancellationToken
    )
    {
        var spec = new MaterialSubFamilyPaginatedSpec(
            request.Page,
            request.PageSize,
            request.MaterialFamilyId,
            request.MaterialClassId
        );
        var items = await repository.ListAsync(spec, cancellationToken);
        var countSpec = new MaterialSubFamilyCountSpec(request.MaterialFamilyId, request.MaterialClassId);
        var total = await repository.CountAsync(countSpec, cancellationToken);

        var response = new PagedResult<MaterialSubFamily>(items, total, request.Page, request.PageSize);

        return Result<PagedResult<MaterialSubFamily>>.Success(response, "Material Sub Family Retrieved Successfully");
    }
}
