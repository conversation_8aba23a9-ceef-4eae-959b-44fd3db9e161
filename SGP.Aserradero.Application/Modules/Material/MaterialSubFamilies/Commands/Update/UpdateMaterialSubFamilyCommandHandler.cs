using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialSubFamilies;

namespace SGP.Aserradero.Application.Modules.Material.MaterialSubFamilies.Commands.Update;

public sealed class UpdateMaterialSubFamilyCommandHandler
    : IRequestHandler<UpdateMaterialSubFamilyCommand, Result<bool>>
{
    private const string LogPrefix = "[Material][SubFamily][Update]";
    private readonly ILogger<UpdateMaterialSubFamilyCommandHandler> _logger;
    private readonly IRepository<MaterialSubFamily> _repository;

    public UpdateMaterialSubFamilyCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialSubFamily>>();
        _logger = serviceProvider.GetRequiredService<ILogger<UpdateMaterialSubFamilyCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(UpdateMaterialSubFamilyCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{@LogPrefix} - Handling command", LogPrefix);
        try
        {
            var materialSubFamily = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (materialSubFamily == null)
            {
                return Result<bool>.Failure("Material Sub Family not found", 404);
            }

            materialSubFamily.Update(
                request.Code,
                request.Name,
                request.AlternativeName,
                request.IsFinish,
                request.MaterialClassId,
                request.MaterialFamilyId,
                request.UserId
            );
            await _repository.UpdateAsync(materialSubFamily, cancellationToken);

            return Result<bool>.Success(true, "Material Sub Family updated", 204);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{@LogPrefix} - Error updating material sub-family", LogPrefix);
            return Result<bool>.Failure(ex.Message);
        }
    }
}
