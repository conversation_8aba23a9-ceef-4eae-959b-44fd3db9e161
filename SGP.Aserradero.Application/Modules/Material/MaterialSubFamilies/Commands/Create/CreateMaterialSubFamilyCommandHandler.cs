using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialSubFamilies;
using SGP.Aserradero.Domain.Entities.Material.MaterialSubFamilies.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialSubFamilies.Commands.Create;

public sealed class CreateMaterialSubFamilyCommandHandler
    : IRequestHandler<CreateMaterialSubFamilyCommand, Result<bool>>
{
    private const string LogPrefix = "[Material][SubFamily][Create]";
    private readonly ILogger<CreateMaterialSubFamilyCommandHandler> _logger;
    private readonly IRepository<MaterialSubFamily> _repository;

    public CreateMaterialSubFamilyCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialSubFamily>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMaterialSubFamilyCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(CreateMaterialSubFamilyCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} - Creating material subfamily", LogPrefix);
        try
        {
            // Validate if MaterialSubFamily already exists by code
            var existingByCode = await _repository.FirstOrDefaultAsync(
                new MaterialSubFamilyByCodeSpec(request.Code, request.MaterialClassId, request.MaterialFamilyId),
                cancellationToken
            );
            if (existingByCode != null)
            {
                _logger.LogWarning(
                    "{LogPrefix} - MaterialSubFamily with code {Code} already exists",
                    LogPrefix,
                    request.Code
                );
                return Result<bool>.Failure("Ya existe una subfamilia de material con este código");
            }

            // Validate if MaterialSubFamily already exists by name
            var existingByName = await _repository.FirstOrDefaultAsync(
                new MaterialSubFamilyByNameSpec(request.Name, request.MaterialClassId, request.MaterialFamilyId),
                cancellationToken
            );
            if (existingByName != null)
            {
                _logger.LogWarning(
                    "{LogPrefix} - MaterialSubFamily with name {Name} already exists",
                    LogPrefix,
                    request.Name
                );
                return Result<bool>.Failure("Ya existe una subfamilia de material con este nombre");
            }

            var materialSubFamily = MaterialSubFamily.Create(
                request.Code,
                request.Name,
                request.AlternativeName,
                request.IsFinish,
                request.MaterialClassId,
                request.MaterialFamilyId,
                request.UserId
            );
            await _repository.AddAsync(materialSubFamily, cancellationToken);
            return Result<bool>.Success(true, "Subfamilia de material creada correctamente", 201);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error creating material subfamily", LogPrefix);
            return Result<bool>.Failure($"Error al crear la subfamilia de material: {ex.Message}", 500);
        }
    }
}
