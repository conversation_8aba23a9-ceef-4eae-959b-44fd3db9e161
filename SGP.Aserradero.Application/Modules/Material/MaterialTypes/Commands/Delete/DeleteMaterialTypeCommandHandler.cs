using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialTypes;

namespace SGP.Aserradero.Application.Modules.Material.MaterialTypes.Commands.Delete;

public class DeleteMaterialTypeCommandHandler : IRequestHandler<DeleteMaterialTypeCommand, Result<bool>>
{
    private const string LogPrefix = "[Material][Type][Delete]";
    private readonly ILogger<DeleteMaterialTypeCommandHandler> _logger;
    private readonly IRepository<MaterialType> _repository;

    public DeleteMaterialTypeCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<DeleteMaterialTypeCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(DeleteMaterialTypeCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{@LogPrefix} Deleting material type with id {@Id}", LogPrefix, request.Id);
        try
        {
            var materialType = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (materialType is null)
            {
                _logger.LogWarning("{@LogPrefix} Material type with id {@Id} not found", LogPrefix, request.Id);
                return Result<bool>.Failure("Material type not found", 404);
            }

            await _repository.DeleteAsync(materialType, cancellationToken);
            _logger.LogInformation("{@LogPrefix} Material type with id {@Id} deleted", LogPrefix, request.Id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{@LogPrefix} Error deleting material type with id {@Id}", LogPrefix, request.Id);
            return Result<bool>.Failure(ex.Message, 500);
        }
    }
}
