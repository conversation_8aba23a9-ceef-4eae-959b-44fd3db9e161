using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialQualities;

namespace SGP.Aserradero.Application.Modules.Material.MaterialQualities.Commands.Create;

public class CreateMaterialQualityCommand : IRequest<Result<MaterialQuality>>
{
    public string Code { get; set; }
    public string Name { get; set; }
    public int MaterialClassId { get; set; }
    public int UserId { get; set; }
}
