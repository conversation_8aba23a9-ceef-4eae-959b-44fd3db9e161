using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialQualities;

namespace SGP.Aserradero.Application.Modules.Material.MaterialQualities.Queries.GetAll;

public class GetAllMaterialQualitiesQuery : IRequest<Result<PagedResult<MaterialQuality>>>
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int MaterialClassId { get; set; }
}
