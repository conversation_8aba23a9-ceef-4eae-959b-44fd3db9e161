using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialGrades;
using SGP.Aserradero.Domain.Entities.Material.MaterialGrades.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialGrades.Commands.Create;

public class CreateMaterialGradeCommandHandler : IRequestHandler<CreateMaterialGradeCommand, Result<bool>>
{
    private const string LogPrefix = "[Material][Grade][Create]";
    private readonly ILogger<CreateMaterialGradeCommandHandler> _logger;
    private readonly IRepository<MaterialGrade> _repository;

    public CreateMaterialGradeCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialGrade>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMaterialGradeCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(CreateMaterialGradeCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} - Creating material grade", LogPrefix);
        try
        {
            // Validate if MaterialGrade already exists by code
            var existingByCode = await _repository.FirstOrDefaultAsync(
                new MaterialGradeByCodeSpec(request.Code),
                cancellationToken
            );
            if (existingByCode != null)
            {
                _logger.LogWarning(
                    "{LogPrefix} - MaterialGrade with code {Code} already exists",
                    LogPrefix,
                    request.Code
                );
                return Result<bool>.Failure("Ya existe un grado de material con este código");
            }

            // Validate if MaterialGrade already exists by name
            var existingByName = await _repository.FirstOrDefaultAsync(
                new MaterialGradeByNameSpec(request.Name),
                cancellationToken
            );
            if (existingByName != null)
            {
                _logger.LogWarning(
                    "{LogPrefix} - MaterialGrade with name {Name} already exists",
                    LogPrefix,
                    request.Name
                );
                return Result<bool>.Failure("Ya existe un grado de material con este nombre");
            }

            var materialGrade = MaterialGrade.Create(request.Code, request.Name);
            await _repository.AddAsync(materialGrade, cancellationToken);
            return Result<bool>.Success(true, "Grado de material creado correctamente", 201);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error creating material grade", LogPrefix);
            return Result<bool>.Failure($"Error al crear el grado de material: {ex.Message}", 500);
        }
    }
}
