using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Domain.Entities.Material.MaterialFinishes;

namespace SGP.Aserradero.Application.Modules.Material.MaterialFinishs.Queries.GetAll;

public sealed record GetMaterialFinishQuery : IRequest<Result<PagedResult<MaterialFinish>>>
{
    public int Page { get; init; } = 1;
    public int PageSize { get; init; } = 20;
    public int MaterialClassId { get; init; }
}
