using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialDatas.Queries.Search;

public sealed class SearchMaterialDataQueryHandler
    : IRequestHandler<SearchMaterialDataQuery, Result<PagedResult<SearchMaterialDataQueryResponse>>>
{
    private const string LogPrefix = "[Material][Data][Search]";
    private readonly ILogger<SearchMaterialDataQueryHandler> _logger;
    private readonly IRepository<MaterialData> _repository;

    public SearchMaterialDataQueryHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialData>>();
        _logger = serviceProvider.GetRequiredService<ILogger<SearchMaterialDataQueryHandler>>();
    }

    public async Task<Result<PagedResult<SearchMaterialDataQueryResponse>>> Handle(
        SearchMaterialDataQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Searching material data", LogPrefix);
        try
        {
            // Especificación para contar resultados filtrados
            var countSpec = new SearchMaterialDataCountSpec(
                request.Search,
                request.MaterialClassId,
                request.MaterialTypeId,
                request.MaterialFamilyId,
                request.MaterialSubFamilyId,
                request.MaterialFinishId,
                request.MaterialStatusId,
                request.SapCode,
                request.Description,
                request.DescriptionAlt,
                request.Dimension,
                request.NameThickness,
                request.NameWidth,
                request.NameLength,
                request.Pieces
            );

            var countResult = await _repository.CountAsync(countSpec, cancellationToken);

            // Especificación para obtener resultados paginados
            var spec = new SearchMaterialDataSpec(
                request.Page,
                request.PageSize,
                request.Search,
                request.MaterialClassId,
                request.MaterialTypeId,
                request.MaterialFamilyId,
                request.MaterialSubFamilyId,
                request.MaterialFinishId,
                request.MaterialStatusId,
                request.SapCode,
                request.Description,
                request.DescriptionAlt,
                request.Dimension,
                request.NameThickness,
                request.NameWidth,
                request.NameLength,
                request.Pieces
            );

            var result = await _repository.ListAsync(spec, cancellationToken);

            // Mapear resultados a DTOs
            var resultDto = new List<SearchMaterialDataQueryResponse>(result.Count);
            foreach (var item in result)
            {
                resultDto.Add(
                    new SearchMaterialDataQueryResponse
                    {
                        Id = item.Id,
                        Description = item.Description,
                        SapCode = item.SapCode,
                    }
                );
            }

            var response = new PagedResult<SearchMaterialDataQueryResponse>(
                resultDto,
                countResult,
                request.Page,
                request.PageSize
            );
            return Result<PagedResult<SearchMaterialDataQueryResponse>>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error searching material data", LogPrefix);
            return Result<PagedResult<SearchMaterialDataQueryResponse>>.Failure(ex.Message, 500);
        }
    }
}
