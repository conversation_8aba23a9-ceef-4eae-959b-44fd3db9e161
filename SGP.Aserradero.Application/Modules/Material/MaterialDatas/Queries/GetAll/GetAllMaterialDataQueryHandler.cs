using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas.Specs;

namespace SGP.Aserradero.Application.Modules.Material.MaterialDatas.Queries.GetAll;

public sealed class GetAllMaterialDataQueryHandler
    : IRequestHandler<GetAllMaterialDataQuery, Result<PagedResult<MaterialData>>>
{
    private const string LogPrefix = "[Material][Data][GetAll]";
    private readonly ILogger<GetAllMaterialDataQueryHandler> _logger;
    private readonly IRepository<MaterialData> _repository;

    public GetAllMaterialDataQueryHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<MaterialData>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetAllMaterialDataQueryHandler>>();
    }

    public async Task<Result<PagedResult<MaterialData>>> Handle(
        GetAllMaterialDataQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Getting all material data", LogPrefix);
        try
        {
            var countSpec = new CountFilteredMaterialDataSpec(request.MaterialClassId);
            var countResult = await _repository.CountAsync(countSpec, cancellationToken);

            var spec = new GetPaginatedMaterialDataSpec(request.Page, request.PageSize, request.MaterialClassId);
            var result = await _repository.ListAsync(spec, cancellationToken);
            var response = new PagedResult<MaterialData>(result, countResult, request.Page, request.PageSize);
            return Result<PagedResult<MaterialData>>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error getting all material data", LogPrefix);
            return Result<PagedResult<MaterialData>>.Failure($"Error al obtener los datos de material: {ex.Message}");
        }
    }
}
