using Leonera_API.Common.Core.Models;
using MediatR;

namespace SGP.Aserradero.Application.Modules.Material.MaterialDatas.Queries.QuickSearch;

public sealed record QuickSearchMaterialDataQuery : IRequest<Result<List<QuickSearchMaterialDataQueryResponse>>>
{
    public string? Search { get; init; }
    public int? MaterialClassId { get; init; }
    public int? MaterialTypeId { get; init; }
    public int? MaterialFamilyId { get; init; }
    public int? MaterialSubFamilyId { get; init; }
    public int? MaterialFinishId { get; init; }
    public int? MaterialStatusId { get; init; }
    public string? Section { get; init; }
    public bool? IsBlocked { get; init; }
    public int MaxResults { get; init; } = 100;
}
