using Leonera_API.Common.Core.Models;
using MediatR;

namespace SGP.Aserradero.Application.Modules.Features.Straps.Commands.Update;

public sealed record UpdateStrapCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Id del zuncho
    /// </summary>
    public int StrapId { get; init; }

    /// <summary>
    /// Código del zuncho
    /// </summary>
    public string Code { get; init; } = string.Empty;

    /// <summary>
    /// Nombre del zuncho
    /// </summary>
    public string Name { get; init; } = string.Empty;
}
