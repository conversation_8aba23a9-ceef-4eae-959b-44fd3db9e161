using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Features.Defects;

namespace SGP.Aserradero.Application.Modules.Features.Defects.Commands.Update;

public sealed class UpdateDefectCommandHandler : IRequestHandler<UpdateDefectCommand, Result<bool>>
{
    private const string LogPrefix = "[Features][Defect][Update]";
    private readonly ILogger<UpdateDefectCommandHandler> _logger;
    private readonly IRepository<Defect> _repository;

    public UpdateDefectCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Defect>>();
        _logger = serviceProvider.GetRequiredService<ILogger<UpdateDefectCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(UpdateDefectCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Actualizando defecto con ID: {DefectId}", LogPrefix, request.DefectId);
        try
        {
            var defect = await _repository.GetByIdAsync(request.DefectId, cancellationToken);
            if (defect == null)
            {
                _logger.LogWarning("{LogPrefix} Defecto no encontrado con ID: {DefectId}", LogPrefix, request.DefectId);
                return Result<bool>.NotFound("El defecto indicado no existe");
            }

            defect.Update(request.Code, request.Name);
            await _repository.UpdateAsync(defect, cancellationToken);
            return Result<bool>.Success(true, "Defecto actualizado correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al actualizar el defecto", LogPrefix);
            return Result<bool>.InternalError("Ha ocurrido un error interno al actualizar el defecto\n" + ex.Message);
        }
    }
}
