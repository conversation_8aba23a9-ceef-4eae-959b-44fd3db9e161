using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Features.Statuses;

namespace SGP.Aserradero.Application.Modules.Features.Statuses.Queries.GetAll;

public sealed class GetAllStatusesQueryHandler : IRequestHandler<GetAllStatusesQuery, Result<IEnumerable<Status>>>
{
    private const string LogPrefix = "[Features][Status][GetAll]";
    private readonly ILogger<GetAllStatusesQueryHandler> _logger;
    private readonly IRepository<Status> _repository;

    public GetAllStatusesQueryHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Status>>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetAllStatusesQueryHandler>>();
    }

    public async Task<Result<IEnumerable<Status>>> Handle(
        GetAllStatusesQuery request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Obteniendo todos los estados", LogPrefix);
        try
        {
            var statuses = await _repository.ListAsync(cancellationToken);
            return Result<IEnumerable<Status>>.Success(statuses, "Estados obtenidos correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al obtener todos los estados", LogPrefix);
            return Result<IEnumerable<Status>>.InternalError(
                $"Ha ocurrido un error interno al obtener todos los estados\n{ex.Message}"
            );
        }
    }
}
