using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Features.Markets;
using SGP.Aserradero.Domain.Entities.Features.Markets.Specs;

namespace SGP.Aserradero.Application.Modules.Features.Markets.Commands.Create;

public sealed class CreateMarketCommandHandler : IRequestHandler<CreateMarketCommand, Result<int>>
{
    private const string LogPrefix = "[Features][Market][Create]";
    private readonly ILogger<CreateMarketCommandHandler> _logger;
    private readonly IRepository<Market> _repository;

    public CreateMarketCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<Market>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMarketCommandHandler>>();
    }

    public async Task<Result<int>> Handle(CreateMarketCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Creando mercado con código: {Code}", LogPrefix, request.Code);
        try
        {
            // Validate if Market already exists by name
            var marketByNameSpec = new MarketByNameSpec(request.Name);
            var existingMarketByName = await _repository.FirstOrDefaultAsync(marketByNameSpec, cancellationToken);
            if (existingMarketByName != null)
            {
                _logger.LogWarning("{LogPrefix} Mercado ya existe", LogPrefix);
                return Result<int>.Conflict("El mercado indicado ya existe");
            }

            // Validate if Market already exists by code
            var marketByCodeSpec = new MarketByCodeSpec(request.Code);
            var existingMarketByCode = await _repository.FirstOrDefaultAsync(marketByCodeSpec, cancellationToken);
            if (existingMarketByCode != null)
            {
                _logger.LogWarning("{LogPrefix} Código del mercado ya existe", LogPrefix);
                return Result<int>.Conflict("El código del mercado ya existe");
            }

            var market = Market.Create(request.Code, request.Name);
            await _repository.AddAsync(market, cancellationToken);
            return Result<int>.Success(market.Id, "Mercado creado correctamente", 201);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al crear el mercado", LogPrefix);
            return Result<int>.InternalError($"Ha ocurrido un error interno al crear el mercado\n{ex.Message}");
        }
    }
}
