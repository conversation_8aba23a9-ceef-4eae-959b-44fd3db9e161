using Leonera_API.Common.Core.Models;
using MediatR;

namespace SGP.Aserradero.Application.Modules.Features.Markets.Commands.Update;

public sealed record UpdateMarketCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Id del mercado
    /// </summary>
    public int MarketId { get; init; }

    /// <summary>
    /// Código del mercado
    /// </summary>
    public required string Code { get; set; }

    /// <summary>
    /// Nombre del mercado
    /// </summary>
    public required string Name { get; set; }
}
