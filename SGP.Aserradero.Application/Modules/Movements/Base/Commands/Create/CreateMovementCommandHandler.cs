using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Utils;
using SGP.Aserradero.Domain.Entities.Main.UserOperationLog;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.Base.Commands.Create;

public class CreateMovementCommandHandler : IRequestHandler<CreateMovementCommand, Result<bool>>
{
    private const string LogPrefix = "[Movements][Create]";
    private readonly IRepository<BaseMovement> _repository;
    private readonly IRepository<UserOperationLog> _userOperationLogRepository;
    private readonly IRepository<MovementType> _movementTypeRepository;
    private readonly ILogger<CreateMovementCommandHandler> _logger;

    public CreateMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _repository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _userOperationLogRepository = serviceProvider.GetRequiredService<IRepository<UserOperationLog>>();
        _movementTypeRepository = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<CreateMovementCommandHandler>>();
    }

    public async Task<Result<bool>> Handle(CreateMovementCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} - Starting movement creation process", LogPrefix);
        try
        {
            var movementTypeByName = new MovementTypeByNameSpec(request.MovementType);
            var movementType = await _movementTypeRepository.FirstOrDefaultAsync(movementTypeByName, cancellationToken);

            var movement = BaseMovementBuilder
                .Create(movementType.Id, request.CreatedBy)
                .WithMovementInfo(request.MovementInfo)
                .WithObservation(request.Observation)
                .WithCapturerId(request.CapturerId)
                .WithDocumentId(request.DocumentId)
                .WithWarehouseId(request.WarehouseId)
                .WithManufacturingOrderId(request.ManufacturingOrderId)
                .WithMachineId(request.MachineId)
                .Build();

            movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });

            await _repository.AddAsync(movement, cancellationToken);

            // Registrar log de operacion
            await UserOperationLogger.LogAsync(
                _userOperationLogRepository,
                userRut: "Rut",
                userName: "User",
                module: "Movimientos",
                action: "Crear movimiento",
                details: "Se ha creado un movimiento",
                cancellationToken
            );

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error creating movement", LogPrefix);
            return Result<bool>.Failure($"Error al crear el movimiento: {ex.Message}");
        }
    }
}
