using Leonera_API.Common.Core.Domain;
using Microsoft.Extensions.DependencyInjection;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Movements.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;

namespace SGP.Aserradero.Application.Modules.Movements.Base.Queries.GetById.DataGetters;

public class GetProductionDataService
{
    private readonly IRepository<BatchMovement> _batchMovementRepo;

    public GetProductionDataService(IServiceProvider serviceProvider)
    {
        _batchMovementRepo = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
    }

    public async Task<List<object>> GetDataAsync(BaseMovement baseMovement, CancellationToken cancellationToken)
    {
        var spec = new BatchMovementByMovementIdSpec(baseMovement.Id);
        var batchMovements = await _batchMovementRepo.ListAsync(spec, cancellationToken);
        var data = new List<object>();
        foreach (var item in batchMovements)
        {
            data.Add(
                new ProductionDataDto
                {
                    Id = item.Id,
                    BatchCode = item.Batch.BatchCode,
                    SapCode = item.Batch.Material.SapCode,
                    Description = item.Batch.Material.Description,
                    DestinationCode = item.Batch.Destination.Code,
                    DestinationName = item.Batch.Destination.Name,
                    MarketCode = item.Batch.Market.Code,
                    MarketName = item.Batch.Market.Name,
                    DefectCode = item.Batch.Defect.Code,
                    DefectName = item.Batch.Defect.Name,
                    BathCode = item.Batch.Bath.Code,
                    BathName = item.Batch.Bath.Name,
                    StrapCode = item.Batch.Strap.Code,
                    StrapName = item.Batch.Strap.Name,
                    StatusCode = item.Batch.StatusEntity.Code,
                    StatusName = item.Batch.StatusEntity.Name,
                    IsStacking = item.Batch.IsStacking,
                    IsHt = item.Batch.IsHt,
                    IsPainting = item.Batch.IsPainting,
                    IsSeparating = item.Batch.IsSeparating,
                    IsTrimmed = item.Batch.IsTrimmed,
                    IsWoodenBlock = item.Batch.IsWoodenBlock,
                    CreatedDate = item.Batch.CreatedDate,
                    CreatedBy = item.Batch.CreatedBy,
                    LastModified = item.Batch.LastModified,
                    LastModifiedBy = item.Batch.LastModifiedBy,
                }
            );
        }

        return data;
    }

    private sealed record ProductionDataDto
    {
        public int Id { get; set; }
        public string BatchCode { get; set; }
        public string SapCode { get; set; }
        public string Description { get; set; }
        public string DestinationCode { get; set; }
        public string DestinationName { get; set; }
        public string MarketCode { get; set; }
        public string MarketName { get; set; }
        public string DefectCode { get; set; }
        public string DefectName { get; set; }
        public string BathCode { get; set; }
        public string BathName { get; set; }
        public string StrapCode { get; set; }
        public string StrapName { get; set; }
        public string StatusCode { get; set; }
        public string StatusName { get; set; }
        public bool IsStacking { get; set; }
        public bool IsHt { get; set; }
        public bool IsPainting { get; set; }
        public bool IsSeparating { get; set; }
        public bool IsTrimmed { get; set; }
        public bool IsWoodenBlock { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime LastModified { get; set; }
        public int LastModifiedBy { get; set; }
    }
}
