using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Modules.Movements.Base.Queries.GetById.DataGetters;
using SGP.Aserradero.Application.Modules.Movements.DTOs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.Base.Queries.GetById;

public class GetMovementByIdQueryHandler : IRequestHandler<GetMovementByIdQuery, Result<BaseMovementDto>>
{
    private const string LogPrefix = "[Movimientos][Obtener por ID]";
    private readonly GetChemicalBathDataService _chemicalBathDataService;
    private readonly GetDryingEntranceDataService _dryingEntranceDataService;

    // DataGetters
    private readonly GetLogConsumptionDataService _logConsumptionDataService;
    private readonly ILogger<GetMovementByIdQueryHandler> _logger;
    private readonly GetProductionDataService _productionDataService;
    private readonly IRepository<BaseMovement> _repo;
    private readonly GetStackingDataService _stackingDataService;
    private readonly GetWarehouseChangeDataService _warehouseChangeDataService;

    public GetMovementByIdQueryHandler(IServiceProvider serviceProvider)
    {
        _repo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _logConsumptionDataService = serviceProvider.GetRequiredService<GetLogConsumptionDataService>();
        _productionDataService = serviceProvider.GetRequiredService<GetProductionDataService>();
        _chemicalBathDataService = serviceProvider.GetRequiredService<GetChemicalBathDataService>();
        _dryingEntranceDataService = serviceProvider.GetRequiredService<GetDryingEntranceDataService>();
        _stackingDataService = serviceProvider.GetRequiredService<GetStackingDataService>();
        _warehouseChangeDataService = serviceProvider.GetRequiredService<GetWarehouseChangeDataService>();
        _logger = serviceProvider.GetRequiredService<ILogger<GetMovementByIdQueryHandler>>();
    }

    public async Task<Result<BaseMovementDto>> Handle(GetMovementByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Iniciando consulta de movimiento por ID", LogPrefix);
        try
        {
            var spec = new MovementByIdSpec(request.MovementId);
            var movement = await _repo.FirstOrDefaultAsync(spec, cancellationToken);
            if (movement == null)
            {
                _logger.LogError("Movimiento con ID {Id} no encontrado", request.MovementId);
                throw new KeyNotFoundException($"Movement con ID {request.MovementId} no encontrado");
            }

            // Buscar los datos necesarios segun el tipo de movimiento
            var data = movement.MovementType!.Name switch
            {
                "Consumo Rollizos" => await _logConsumptionDataService.GetDataAsync(movement, cancellationToken),
                "Produccion" => await _productionDataService.GetDataAsync(movement, cancellationToken),
                "Empalillado" => await _stackingDataService.GetDataAsync(movement, cancellationToken),
                "Baño Quimico" => await _chemicalBathDataService.GetDataAsync(movement, cancellationToken),
                "Secado Entrada" => await _dryingEntranceDataService.GetDataAsync(movement, cancellationToken),
                "Secado Salida" => await _stackingDataService.GetDataAsync(movement, cancellationToken),
                "Cambio Almacen" => await _warehouseChangeDataService.GetDataAsync(movement, cancellationToken),
                _ => []
            };

            var response = new BaseMovementDto
            {
                Id = movement.Id,
                MovementInfo = movement.MovementInfo,
                MovementType = movement.MovementType!.Name,
                Observation = movement.Observation,
                CapturerId = movement.CapturerId,
                CapturerName = movement.Capturer?.Name ?? string.Empty,
                DocumentId = movement.DocumentId,
                WarehouseId = movement.WarehouseId,
                WarehouseName = movement.Warehouse?.Name ?? string.Empty,
                ManufacturingOrderId = movement.ManufacturingOrderId,
                ManufacturingOrderName = movement.ManufacturingOrder?.Name ?? string.Empty,
                MachineId = movement.MachineId,
                MachineName = movement.Machine?.Name ?? string.Empty,
                Created = movement.Created,
                CreatedBy = movement.CreatedBy,
                LastModified = movement.LastModified,
                LastModifiedBy = movement.LastModifiedBy,
                InSap = movement.InSap,
                Data = data
            };
            return Result<BaseMovementDto>.Success(response, "Movimiento obtenido correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{LogPrefix} Error al obtener el movimiento con ID: {Id}",
                LogPrefix,
                request.MovementId
            );
            return Result<BaseMovementDto>.Failure($"Error al obtener el movimiento con ID: {request.MovementId}");
        }
    }
}
