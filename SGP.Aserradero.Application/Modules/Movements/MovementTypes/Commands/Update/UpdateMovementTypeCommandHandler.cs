using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.MovementTypes.Commands.Update;

public sealed class UpdateMovementTypeCommandHandler : IRequestHandler<UpdateMovementTypeCommand, Result<bool>>
{
    private const string LogPrefix = "[Movements][Types][Update]";
    private readonly ILogger<UpdateMovementTypeCommandHandler> _logger;
    private readonly IRepository<MovementType> _repository;
    private readonly IRepository<MovementTypeOrderClass> _movementTypeOrderClassRepository;

    public UpdateMovementTypeCommandHandler(IServiceProvider serviceProvider)
    {
        _logger = serviceProvider.GetRequiredService<ILogger<UpdateMovementTypeCommandHandler>>();
        _repository = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _movementTypeOrderClassRepository = serviceProvider.GetRequiredService<IRepository<MovementTypeOrderClass>>();
    }

    public async Task<Result<bool>> Handle(UpdateMovementTypeCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} - Starting to update movement type", LogPrefix);
        try
        {
            var movementType = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (movementType == null)
            {
                return Result<bool>.NotFound("Tipo de movimiento no encontrado");
            }
            movementType.Update(request.Name, request.Description, request.IsActive);
            await _repository.UpdateAsync(movementType, cancellationToken);
            // Eliminar todas las clases de orden relacionadas con el tipo de movimiento
            var existingRelations = await _movementTypeOrderClassRepository.ListAsync(
                new MovementTypeOrderClassByTypeIdSpec(request.Id),
                cancellationToken
            );
            await _movementTypeOrderClassRepository.DeleteRangeAsync(existingRelations, cancellationToken);

            List<MovementTypeOrderClass> newRelations = [];

            // Agregar las nuevas clases de orden relacionadas con el tipo de movimiento
            foreach (var orderClassId in request.OrderClassIds)
            {
                var movementTypeOrderClass = new MovementTypeOrderClass
                {
                    MovementTypeId = movementType.Id,
                    OrderClassId = orderClassId,
                };
                newRelations.Add(movementTypeOrderClass);
            }

            await _movementTypeOrderClassRepository.AddRangeAsync(newRelations, cancellationToken);

            return Result<bool>.Success(true, "Tipo de movimiento actualizado correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "{LogPrefix} - An error occurred while updating movement type: {Message}",
                LogPrefix,
                ex.Message
            );
            return Result<bool>.InternalError("Ha ocurrido un error interno al actualizar el tipo de movimiento");
        }
    }
}
