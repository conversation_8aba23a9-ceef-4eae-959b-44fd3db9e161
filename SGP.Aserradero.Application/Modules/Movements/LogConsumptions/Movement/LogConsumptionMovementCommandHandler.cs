using System.Net;
using Leonera_API_ExternalServices;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using Leonera_API.Common.Domain.Entities.GeneralData;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.LogConsumptions;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.LogConsumptions.Movement;

public class LogConsumptionMovementCommandHandler : IRequestHandler<LogConsumptionMovementCommand, Result<bool>>
{
    private const string LogPrefix = "[LogConsumption]";
    private const string MovementType = "Consumo Rollizos";
    private readonly IRepository<LogConsumption> _logConsumptionRepo;
    private readonly ILogger<LogConsumptionMovementCommandHandler> _logger;
    private readonly IRepository<MovementType> _movementTypeRepo;
    private readonly IRepository<BaseMovement> _repo;
    private readonly IRepository<SapDatabases> _sapDatabasesRepo;
    private readonly SapServiceLayerFacade _sapService;

    public LogConsumptionMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _repo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _logConsumptionRepo = serviceProvider.GetRequiredService<IRepository<LogConsumption>>();
        _logger = serviceProvider.GetRequiredService<ILogger<LogConsumptionMovementCommandHandler>>();
        _sapService = serviceProvider.GetRequiredService<SapServiceLayerFacade>();
        _sapDatabasesRepo = serviceProvider.GetRequiredService<IRepository<SapDatabases>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
    }

    public async Task<Result<bool>> Handle(LogConsumptionMovementCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"{LogPrefix} - Iniciando creación del movimiento");
        try
        {
            // Buscar tipo de movimiento por nombre
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(
                new MovementTypeByNameSpec(MovementType),
                cancellationToken
            );
            if (movementType == null)
            {
                _logger.LogError(
                    "{LogPrefix} - Tipo de movimiento '{MovementType}' no encontrado",
                    LogPrefix,
                    MovementType
                );
                return Result<bool>.NotFound($"Tipo de movimiento '{MovementType}' no encontrado");
            }

            // Crear el movimiento
            var movement = BaseMovementBuilder
                .Create(movementType.Id, request.CreatedBy)
                .WithMovementInfo(request.MovementInfo)
                .WithObservation(request.Observation)
                .WithCapturerId(request.CapturerId)
                .WithDocumentId(request.DocumentId)
                .WithWarehouseId(request.WarehouseId)
                .WithManufacturingOrderId(request.ManufacturingOrderId)
                .WithMachineId(request.MachineId)
                .Build();

            movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });
            await _repo.AddAsync(movement, cancellationToken);

            var logConsumptionList = new List<LogConsumption>();

            // Crear los logs de consumo
            foreach (var log in request.ConsumptionData)
            {
                var logConsumption = LogConsumption.Create(
                    movement.Id,
                    log.ItemCode,
                    log.ItemName,
                    log.Quantity,
                    log.CubicFactor
                );
                logConsumptionList.Add(logConsumption);
            }

            await _logConsumptionRepo.AddRangeAsync(logConsumptionList, cancellationToken);

            // Realizar consumo en SAP
            // Generar payload
            var payload = new LogConsumptionSapDto
            {
                DocDate = DateTime.Now,
                Comments = "SGP - " + request.Observation,
                DocumentLines = logConsumptionList.ConvertAll(log => new LogConsumptionSapDto.LogsData
                {
                    ItemCode = log.ItemCode,
                    Quantity = log.Quantity,
                    WarehouseCode = "211",
                    CostingCode = "901104",
                    CostingCode2 = "901100",
                    CostingCode3 = "901000",
                    CostingCode4 = "900000"
                })
            };

            // Movimiento recien creado asi que no puede ser null
            var movementToUpdate = await _repo.GetByIdAsync(movement.Id, cancellationToken);
            // TODO: Cambiar base en productivo
            var sapDatabase = await _sapDatabasesRepo.GetByIdAsync(12, cancellationToken);
            var sapConnection = await _sapService.Login.ConnectSl2(sapDatabase.DatabaseName);
            if (sapConnection == null)
            {
                movementToUpdate!.InSap = false;
                await _repo.UpdateAsync(movementToUpdate, cancellationToken);
                _logger.LogError("{LogPrefix} - No se pudo conectar a SAP", LogPrefix);
                return Result<bool>.Failure("No se pudo conectar a SAP", 503);
            }

            var sapResponse = await _sapService.InventoryGenExits.MakeInventoryExit(payload, sapConnection);
            if (sapResponse.StatusCode != HttpStatusCode.Created)
            {
                movementToUpdate!.InSap = false;
                await _repo.UpdateAsync(movementToUpdate, cancellationToken);
                _logger.LogError(
                    "{LogPrefix} - No se pudo realizar el consumo en SAP: {Error}",
                    LogPrefix,
                    sapResponse.Content
                );
                return Result<bool>.Failure($"No se pudo realizar el consumo en SAP\n{sapResponse.Content}", 424);
            }

            movementToUpdate!.InSap = true;
            await _repo.UpdateAsync(movementToUpdate, cancellationToken);
            return Result<bool>.Success(true, "Consumo de rollizos creado correctamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al crear el movimiento", LogPrefix);
            return Result<bool>.InternalError($"{LogPrefix} Error interno al crear el movimiento");
        }
    }
}
