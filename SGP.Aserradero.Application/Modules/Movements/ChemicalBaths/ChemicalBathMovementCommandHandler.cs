using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Requests;
using SGP.Aserradero.Domain.Entities.Features.Baths;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.ChemicalBaths;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.ChemicalBaths;

public class ChemicalBathMovementCommandHandler
    : IRequestHandler<ChemicalBathMovementCommand, Result<ChemicalBathMovementResult>>
{
    private const string LogPrefix = "[ChemicalBath][Movement]";
    private const string MovementType = "Baño Quimico";
    private readonly IRepository<BatchAuditLog> _auditLogRepo;
    private readonly IRepository<BatchMovement> _batchMovementRepo;
    private readonly IRepository<Batch> _batchRepo;
    private readonly IRepository<Bath> _bathRepo;
    private readonly IRepository<ChemicalBathMovement> _chemicalBathMovementRepo;
    private readonly ILogger<ChemicalBathMovementCommandHandler> _logger;
    private readonly IRepository<BaseMovement> _movementRepo;
    private readonly IRepository<MovementType> _movementTypeRepo;

    public ChemicalBathMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _movementRepo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchMovementRepo = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _chemicalBathMovementRepo = serviceProvider.GetRequiredService<IRepository<ChemicalBathMovement>>();
        _auditLogRepo = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _bathRepo = serviceProvider.GetRequiredService<IRepository<Bath>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<ChemicalBathMovementCommandHandler>>();
    }

    public async Task<Result<ChemicalBathMovementResult>> Handle(
        ChemicalBathMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Iniciando movimiento de baño químico", LogPrefix);

        var batchIds = request.Batches.Select(b => b.BatchId).ToList();
        var invalidBatches = new List<InvalidBathBatchInfo>();
        var validBatches = new List<Batch>();

        try
        {
            var movementTypeSpec = new MovementTypeByNameSpec(MovementType);
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(movementTypeSpec, cancellationToken);
            if (movementType == null)
            {
                return Result<ChemicalBathMovementResult>.NotFound("Tipo de movimiento no encontrado");
            }

            // Validar que el baño químico existe
            var bathsList = await _bathRepo.ListAsync(cancellationToken);
            var chemicalBath = bathsList.FirstOrDefault(b => b.Id == request.ChemicalBathId);
            if (chemicalBath == null)
            {
                _logger.LogError(
                    "{LogPrefix} Baño químico con ID {ChemicalBathId} no encontrado",
                    LogPrefix,
                    request.ChemicalBathId
                );
                return Result<ChemicalBathMovementResult>.NotFound("Baño químico no encontrado");
            }

            // Buscar todos los lotes solicitados
            var batches = await _batchRepo.ListAsync(cancellationToken);
            var foundBatches = batches.Where(b => batchIds.Contains(b.Id)).ToList();
            var foundBatchIds = foundBatches.Select(b => b.Id).ToHashSet();
            var notFoundBatchIds = batchIds.Except(foundBatchIds).ToList();

            // Agregar lotes no encontrados
            foreach (var batchId in notFoundBatchIds)
            {
                invalidBatches.Add(new InvalidBathBatchInfo
                {
                    BatchId = batchId,
                    Reason = "Lote no encontrado"
                });
                _logger.LogWarning("{LogPrefix} - Lote con ID {BatchId} no encontrado", LogPrefix, batchId);
            }

            // Validar que los lotes encontrados sean de tipo "verde"
            foreach (var batch in foundBatches)
            {
                // Verificar si el estado permite el baño químico (tipo "verde")
                // Se considera "verde" si el Status string contiene "verde" o si no tiene baño asignado
                var isGreenType =
                    batch.Status?.ToLower().Contains("verde") == true
                    || batch.Status?.ToLower() == "production"
                    || string.IsNullOrEmpty(batch.Status);

                if (!isGreenType)
                {
                    invalidBatches.Add(
                        new InvalidBathBatchInfo
                        {
                            BatchId = batch.Id,
                            BatchCode = batch.BatchCode,
                            Reason = $"El lote no es de tipo verde. Estado actual: {batch.Status}"
                        }
                    );
                    _logger.LogWarning(
                        "{LogPrefix} - Lote {BatchCode} no es de tipo verde. Estado actual: {Status}",
                        LogPrefix,
                        batch.BatchCode,
                        batch.Status
                    );
                    continue;
                }

                validBatches.Add(batch);
            }

            if (validBatches.Count != 0)
            {
                // Crear el movimiento principal
                var movement = BaseMovementBuilder
                    .Create(movementType.Id, request.CreatedBy)
                    .WithMovementInfo(request.MovementInfo)
                    .WithObservation(request.Observation)
                    .WithCapturerId(request.CapturerId)
                    .WithDocumentId(request.DocumentId)
                    .WithWarehouseId(request.WarehouseId)
                    .WithManufacturingOrderId(request.ManufacturingOrderId)
                    .WithMachineId(request.MachineId)
                    .Build();

                movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });

                await _movementRepo.AddAsync(movement, cancellationToken);

                // Procesar cada lote válido
                foreach (var batch in validBatches)
                {
                    var previousBathId = batch.BathId;

                    // Actualizar BathId del lote
                    batch.UpdateCharacteristics(
                        new UpdateBatchRequest(BathId: request.ChemicalBathId, ModifiedBy: request.CreatedBy)
                    );
                    await _batchRepo.UpdateAsync(batch, cancellationToken);

                    // Crear BatchMovement
                    var batchMovement = BatchMovement.Create(batch.Id, movement.Id, movementType.Id);
                    await _batchMovementRepo.AddAsync(batchMovement, cancellationToken);

                    // Crear ChemicalBathMovement relacionado con el lote y el movimiento
                    var chemicalBathMovement = ChemicalBathMovement.Create(
                        batch.Id,
                        request.ChemicalBathId,
                        movement.Id
                    );
                    await _chemicalBathMovementRepo.AddAsync(chemicalBathMovement, cancellationToken);

                    // Obtener el nombre del baño químico
                    var chemicalBathName = chemicalBath.Name;
                    var oldBathName = bathsList.FirstOrDefault(b => b.Id == previousBathId)?.Name;

                    // Registrar auditoría del cambio de BathId
                    var auditLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "Baño químico",
                        oldBathName,
                        chemicalBathName,
                        request.CreatedBy
                    );
                    await _auditLogRepo.AddAsync(auditLog, cancellationToken);

                    _logger.LogInformation(
                        "{LogPrefix} - Lote {BatchCode} asignado al baño químico {ChemicalBathId}",
                        LogPrefix,
                        batch.BatchCode,
                        request.ChemicalBathId
                    );
                }
            }

            _logger.LogInformation(
                "{LogPrefix} - Movimiento de baño químico completado. Procesados: {ProcessedCount}, Inválidos: {InvalidCount}",
                LogPrefix,
                validBatches.Count,
                invalidBatches.Count
            );

            var result = new ChemicalBathMovementResult
            {
                ProcessedBatches = validBatches.Count,
                InvalidBatches = invalidBatches,
                Success = validBatches.Count != 0
            };

            return Result<ChemicalBathMovementResult>.Success(result, "Movimiento de baño químico completado");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error al procesar el movimiento de baño químico", LogPrefix);
            return Result<ChemicalBathMovementResult>.Failure("Error al procesar el movimiento de baño químico", 500);
        }
    }
}
