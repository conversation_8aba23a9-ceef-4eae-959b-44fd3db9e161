using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Base.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Requests;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles.Specs;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.DryingManager.Commands.Entrance;

/// <summary>
///     Manejador de comandos para el movimiento de entrada a secado.
///     Crea el ciclo de secado de entrada y también registra los lotes en el sistema.
///     Gestiona todo el proceso de entrada de lotes al proceso de secado, incluyendo la creación del ciclo,
///     actualización del estado de los lotes, registro de movimientos y auditoría de cambios.
/// </summary>
public class DryingEntranceMovementCommandHandler
    : IRequestHandler<DryingEntranceMovementCommand, Result<DryingEntranceMovementCommandResponse>>
{
    private const string LogPrefix = "[DryingEntrance][Movement]";
    private readonly IRepository<BatchAuditLog> _auditLogRepo;
    private readonly IRepository<BatchMovement> _batchMovementRepo;
    private readonly IRepository<Batch> _batchRepo;
    private readonly IRepository<DryingCycleEntity> _dryingCycleRepo;
    private readonly ILogger<DryingEntranceMovementCommandHandler> _logger;

    private readonly IRepository<BaseMovement> _movementRepo;
    private readonly IRepository<MovementType> _movementTypeRepo;

    /// <summary>
    ///     Constructor del manejador de comandos para el movimiento de entrada a secado.
    ///     Inicializa todos los repositorios necesarios para la gestión del proceso de entrada a secado.
    /// </summary>
    /// <param name="serviceProvider">Proveedor de servicios para la inyección de dependencias</param>
    public DryingEntranceMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _movementRepo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchMovementRepo = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _dryingCycleRepo = serviceProvider.GetRequiredService<IRepository<DryingCycleEntity>>();
        _auditLogRepo = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<DryingEntranceMovementCommandHandler>>();
    }

    /// <summary>
    ///     Maneja la ejecución del comando de entrada a secado.
    ///     Este método orquesta todo el proceso de entrada de lotes al ciclo de secado:
    ///     1. Crea un nuevo ciclo de secado
    ///     2. Busca los lotes solicitados por código
    ///     3. Crea el movimiento principal
    ///     4. Procesa cada lote actualizando su estado
    ///     5. Crea los movimientos de lote
    ///     6. Agregar detalle de entrada al ciclo de secado usando el nuevo modelo unificado
    ///     7. Registra la auditoría de cambios de estado
    /// </summary>
    /// <param name="request">Comando con la información necesaria para el proceso de entrada a secado</param>
    /// <param name="cancellationToken">Token de cancelación para operaciones asíncronas</param>
    /// <returns>Resultado con la respuesta del comando que incluye información sobre los lotes procesados</returns>
    public async Task<Result<DryingEntranceMovementCommandResponse>> Handle(
        DryingEntranceMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        // Registra el inicio del proceso de entrada a secado
        _logger.LogInformation("{LogPrefix} - Iniciando movimiento de entrada a secado", LogPrefix);

        // Extrae los códigos de lote de la solicitud y prepara la lista para almacenar los IDs de movimientos creados
        var movDryingEntranceIds = new List<int>();

        try
        {
            // Busca el tipo de movimiento "Secado Entrada" que se utilizará para los registros
            var movementTypeSpec = new MovementTypeByNameSpec("Secado Entrada");
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(movementTypeSpec, cancellationToken);
            if (movementType == null)
            {
                // Si no se encuentra el tipo de movimiento, devuelve un error
                return Result<DryingEntranceMovementCommandResponse>.NotFound("Tipo de movimiento no encontrado");
            }

            // 1. Crear el ciclo de secado para agrupar los lotes que entran al proceso
            var dryingCycle = await CreateDryingCycle(request, cancellationToken);

            // 2. Buscar los lotes solicitados por código utilizando una especificación
            var batchesByCodesSpec = new BatchByCodeListSpec(request.Batches);
            var batches = await _batchRepo.ListAsync(batchesByCodesSpec, cancellationToken);

            // Verifica que se hayan encontrado lotes para procesar
            if (batches.Count != 0)
            {
                // 3. Crear el movimiento principal que agrupa todos los movimientos de lotes
                var movement = BaseMovement.Create(
                    request.BaseMovement.MovementInfo,
                    request.BaseMovement.Observation,
                    request.BaseMovement.CapturerId,
                    request.BaseMovement.DocumentId,
                    request.BaseMovement.WarehouseId,
                    request.BaseMovement.ManufacturingOrderId,
                    request.BaseMovement.MachineId,
                    request.BaseMovement.CreatedBy,
                    movementType.Id
                );
                await _movementRepo.AddAsync(movement, cancellationToken);

                // 4. Procesar cada lote válido encontrado en el sistema
                foreach (var batch in batches)
                {
                    // Guarda el estado anterior del lote para el registro de auditoría
                    var previousStatus = batch.Status ?? "unknown";

                    // Obtener una instancia rastreada del lote para poder actualizarla
                    var trackedBatch = await _batchRepo.GetByIdAsync(batch.Id, cancellationToken);
                    if (trackedBatch == null)
                    {
                        continue; // Saltar si el lote no existe
                    }

                    // Actualizar el estado del lote a "Secado" para reflejar su nueva ubicación en el proceso
                    trackedBatch.UpdateCharacteristics(
                        new UpdateBatchRequest(ModifiedBy: request.BaseMovement.CreatedBy, Status: "Secado")
                    );
                    await _batchRepo.UpdateAsync(trackedBatch, cancellationToken);

                    // 5. Crear Movimiento de lote para registrar la relación entre el lote y el movimiento principal
                    var batchMovement = BatchMovement.Create(batch.Id, movement.Id, movementType.Id);
                    await _batchMovementRepo.AddAsync(batchMovement, cancellationToken);

                    // 6. Agregar detalle de entrada al ciclo de secado
                    dryingCycle.AddEntrance(movement.Id, batch.Id, request.EntryDate, request.BaseMovement.Observation);

                    // 7. Registrar auditoría del cambio de estado para mantener la trazabilidad del lote
                    var auditLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "Status", // Campo que cambió
                        previousStatus, // Valor anterior
                        "En Secado", // Nuevo valor
                        request.BaseMovement.CreatedBy // Usuario que realizó el cambio
                    );
                    await _auditLogRepo.AddAsync(auditLog, cancellationToken);
                }

                // Persistir el ciclo de secado actualizado con los detalles de entrada
                await _dryingCycleRepo.UpdateAsync(dryingCycle, cancellationToken);

                // Obtener los IDs de los detalles de entrada creados
                var dryingEntranceDetailIds = dryingCycle.GetEntrances().Select(x => x.Id).ToList();

                // Devolver la respuesta con los IDs de los detalles de entrada y la información del proceso
                return Result<DryingEntranceMovementCommandResponse>.Success(
                    new DryingEntranceMovementCommandResponse
                    {
                        ProcessedBatches = batches.Count, // Cantidad de lotes procesados
                        DryingCycleId = dryingCycle.Id, // ID del ciclo de secado creado
                        DryingEntranceDetailIds = dryingEntranceDetailIds, // IDs de los detalles de entrada
                        Message = "Lotes ingresados a secado exitosamente" // Mensaje de éxito
                    },
                    "Lotes ingresados a secado exitosamente"
                );
            }

            // Si no se encontraron lotes, devuelve un mensaje de error
            return Result<DryingEntranceMovementCommandResponse>.Failure("Lotes no encontrados", 404);
        }
        catch (Exception ex)
        {
            // Registra cualquier excepción que ocurra durante el proceso
            _logger.LogError(ex, "{LogPrefix} - Error al procesar el movimiento de entrada a secado", LogPrefix);
            return Result<DryingEntranceMovementCommandResponse>.Failure(
                "Error al procesar el movimiento de entrada a secado",
                500
            );
        }
    }

    /// <summary>
    ///     Crea un nuevo ciclo de secado en el sistema basado en la información proporcionada en el comando.
    ///     Utiliza valores predeterminados para orderClassId y cameraId si no se proporcionan en la solicitud.
    /// </summary>
    /// <param name="request">Comando con la información necesaria para crear el ciclo de secado</param>
    /// <param name="cancellationToken">Token de cancelación para operaciones asíncronas</param>
    /// <returns>Entidad del ciclo de secado creada y persistida en la base de datos</returns>
    private async Task<DryingCycleEntity> CreateDryingCycle(
        DryingEntranceMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        // Validar que el DryingCicleCode no exista
        var dryingCycleSpec = new DryingCycleByCodeSpec(request.DryingCycleCode);
        var existingDryingCycle = await _dryingCycleRepo.FirstOrDefaultAsync(dryingCycleSpec, cancellationToken);
        if (existingDryingCycle != null)
        {
            throw new Exception("El ciclo de secado ya existe");
        }

        // Usar valores por defecto si no se proporcionan en la solicitud
        var orderClassId = request.OrderClassId;
        var cameraId = request.DryingCameraId;

        // Crea una nueva entidad de ciclo de secado con los parámetros proporcionados
        var dryingCycle = DryingCycleEntity.CreateEntrance(
            request.DryingCycleCode,
            request.EsHt,
            request.EntryDate,
            request.EntryTime,
            orderClassId,
            cameraId
        );

        // Persiste el ciclo de secado en la base de datos
        await _dryingCycleRepo.AddAsync(dryingCycle, cancellationToken);
        return dryingCycle;
    }
}
