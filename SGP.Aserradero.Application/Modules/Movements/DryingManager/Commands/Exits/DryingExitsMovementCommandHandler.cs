using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Material.MaterialTransformations;
using SGP.Aserradero.Domain.Entities.Material.MaterialTransformations.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles;
using SGP.Aserradero.Domain.Entities.Movements.DryingCycles.Specs;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;

namespace SGP.Aserradero.Application.Modules.Movements.DryingManager.Commands.Exits;

/// <summary>
///     Manejador de comandos para el movimiento de salida de secado.
///     Busca el movimiento de entrada de secado segun el codigo indicado y tambien los lotes asociados.
///     Luego crea el movimiento de salida de secado con los lotes asociados a la entrada.
/// </summary>
public sealed class DryingExitsMovementCommandHandler
    : IRequestHandler<DryingExitsMovementCommand, Result<DryingExitsMovementCommandResponse>>
{
    private const string LogPrefix = "[Movimientos][Secado][Salida]";
    private readonly IRepository<BatchAuditLog> _auditLogRepo;
    private readonly IRepository<Batch> _batchRepo;
    private readonly IRepository<DryingCycleDetail> _dryingCycleDetailRepo;
    private readonly IRepository<DryingCycleEntity> _dryingCycleRepo;

    private readonly ILogger<DryingExitsMovementCommandHandler> _logger;
    private readonly IRepository<MaterialTransformation> _materialTransformationRepo;
    private readonly IRepository<BaseMovement> _movementRepo;
    private readonly IRepository<MovementType> _movementTypeRepo;

    public DryingExitsMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _dryingCycleRepo = serviceProvider.GetRequiredService<IRepository<DryingCycleEntity>>();
        _dryingCycleDetailRepo = serviceProvider.GetRequiredService<IRepository<DryingCycleDetail>>();
        _logger = serviceProvider.GetRequiredService<ILogger<DryingExitsMovementCommandHandler>>();
        _movementRepo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _auditLogRepo = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _materialTransformationRepo = serviceProvider.GetRequiredService<IRepository<MaterialTransformation>>();
    }

    public async Task<Result<DryingExitsMovementCommandResponse>> Handle(
        DryingExitsMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Iniciando movimiento de salida de secado", LogPrefix);
        try
        {
            // Buscar el ciclo de secado
            var dryingCycleSpec = new DryingCycleByCodeSpec(request.DryingCycleCode);
            var dryingCycle = await _dryingCycleRepo.FirstOrDefaultAsync(dryingCycleSpec, cancellationToken);
            if (dryingCycle == null)
            {
                return Result<DryingExitsMovementCommandResponse>.NotFound("Ciclo de secado no encontrado");
            }

            // Obtener los detalles de entrada del ciclo (lotes asociados al ciclo)
            var dryingCycleDetailSpec = new DryingCycleDetailByCycleIdSpec(dryingCycle.Id);
            var dryingCycleDetails = await _dryingCycleDetailRepo.ListAsync(dryingCycleDetailSpec, cancellationToken);
            if (dryingCycleDetails == null || dryingCycleDetails.Count == 0)
            {
                return Result<DryingExitsMovementCommandResponse>.NotFound(
                    "No se encontraron detalles de entrada asociados al ciclo"
                );
            }

            // Obtener el tipo de movimiento de salida
            var movementTypeSpec = new MovementTypeByNameSpec("Secado Salida");
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(movementTypeSpec, cancellationToken);
            if (movementType == null)
            {
                return Result<DryingExitsMovementCommandResponse>.NotFound(
                    "Tipo de movimiento de salida de secado no encontrado"
                );
            }

            // Buscar las transformaciones de material
            var batchIds = dryingCycleDetails.Select(x => x.Batch.Id).Distinct().ToList();
            var batches = (await _batchRepo.ListAsync(cancellationToken)).Where(b => batchIds.Contains(b.Id)).ToList();
            var materialIds = batches.Select(x => x.MaterialId).Distinct().ToList();
            var materialTransformationSpec = new MaterialTransformationByMaterialIdListSpec(materialIds);
            var materialTransformations = await _materialTransformationRepo.ListAsync(
                materialTransformationSpec,
                cancellationToken
            );

            // Validar que todos los materiales tengan transformación
            if (materialTransformations.Count != materialIds.Count)
            {
                return Result<DryingExitsMovementCommandResponse>.NotFound(
                    "Hay lotes sin transformación asignada, no se puede realizar el movimiento de salida"
                );
            }

            // Crear el movimiento de salida
            var movement = BaseMovement.Create(
                request.BaseMovement.MovementInfo,
                request.BaseMovement.Observation,
                request.BaseMovement.CapturerId,
                request.BaseMovement.DocumentId,
                request.BaseMovement.WarehouseId,
                request.BaseMovement.ManufacturingOrderId,
                request.BaseMovement.MachineId,
                request.BaseMovement.CreatedBy,
                movementType.Id
            );
            await _movementRepo.AddAsync(movement, cancellationToken);

            // Actualizar el ciclo de secado con la salida
            dryingCycle.SetDepartureInfo(request.ExitDate, request.ExitTime);

            // Agregar detalles de salida para cada lote de entrada
            foreach (var dryingCycleDetail in dryingCycleDetails)
            {
                dryingCycle.AddOutput(movement.Id, dryingCycleDetail.BatchId, request.ExitDate);

                // Actualizar estado del lote
                var batch = batches.FirstOrDefault(b => b.Id == dryingCycleDetail.BatchId);
                if (batch != null)
                {
                    var previousStatus = batch.Status;
                    var previousMaterial = batch.MaterialId;
                    batch.Status = "Secado Finalizado";
                    var transformation = materialTransformations.FirstOrDefault(x =>
                        x.MaterialBaseId == batch.MaterialId
                    );
                    if (transformation != null)
                    {
                        batch.MaterialId = transformation.TransformatedMaterialId;
                    }

                    await _batchRepo.UpdateAsync(batch, cancellationToken);

                    // Registrar auditoría del cambio de estado
                    var auditLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "Status",
                        previousStatus,
                        batch.Status,
                        request.BaseMovement.CreatedBy
                    );

                    var materialLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "MaterialId",
                        previousMaterial.ToString(),
                        batch.MaterialId.ToString(),
                        request.BaseMovement.CreatedBy
                    );
                    await _auditLogRepo.AddAsync(auditLog, cancellationToken);
                    await _auditLogRepo.AddAsync(materialLog, cancellationToken);
                }
            }

            await _dryingCycleRepo.UpdateAsync(dryingCycle, cancellationToken);

            return Result<DryingExitsMovementCommandResponse>.Success(
                new DryingExitsMovementCommandResponse
                {
                    DryingCycleId = dryingCycle.Id,
                    MovementId = movement.Id,
                    Message = "Movimiento de salida de secado creado correctamente"
                }
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error al realizar movimiento de salida de secado", LogPrefix);
            return Result<DryingExitsMovementCommandResponse>.InternalError(
                "Ha ocurrido un error interno al realizar movimiento de salida de secado"
            );
        }
    }
}
