using FluentValidation;

namespace SGP.Aserradero.Application.Modules.Movements.Stackings.Validate;

/// <summary>
///     Validador para el comando de validación de lotes para empalillado.
/// </summary>
public class ValidateStackingBatchesCommandValidator : AbstractValidator<ValidateStackingBatchesCommand>
{
    public ValidateStackingBatchesCommandValidator()
    {
        RuleFor(x => x.Batches).NotEmpty().WithMessage("Debe especificar al menos un lote para validar");

        RuleForEach(x => x.Batches)
            .ChildRules(batch =>
            {
                batch
                    .RuleFor(x => x.BatchCode)
                    .NotEmpty()
                    .WithMessage("El código del lote no puede estar vacío")
                    .MaximumLength(50)
                    .WithMessage("El código del lote no puede exceder 50 caracteres");
            });

        RuleFor(x => x.Batches)
            .Must(batches => batches.Count() <= 1000)
            .WithMessage("No puede validar más de 1000 lotes a la vez");
    }
}
