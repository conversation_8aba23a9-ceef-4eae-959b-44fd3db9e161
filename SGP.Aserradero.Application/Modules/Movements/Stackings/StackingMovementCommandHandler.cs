using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Base.Specs;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;
using SGP.Aserradero.Domain.Entities.Movements.Stackings;

namespace SGP.Aserradero.Application.Modules.Movements.Stackings;

/// <summary>
///     Handler responsable de registrar un movimiento de empalillado.
///     Utiliza el patrón Result para manejo robusto de errores y validaciones.
/// </summary>
public sealed class StackingMovementCommandHandler
    : IRequestHandler<StackingMovementCommand, Result<StackingMovementCommandResponse>>
{
    private const string LogPrefix = "[Stacking][Movement]";
    private const string MovementType = "Empalillado";
    private readonly IRepository<BatchAuditLog> _auditLogRepository;
    private readonly IRepository<Batch> _batchRepository;
    private readonly ILogger<StackingMovementCommandHandler> _logger;
    private readonly IRepository<BaseMovement> _movementRepository;
    private readonly IRepository<MovementType> _movementTypeRepository;
    private readonly IRepository<MovStacking> _movStackingRepository;

    public StackingMovementCommandHandler(IServiceProvider serviceProvider)
    {
        _movementTypeRepository = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _batchRepository = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _movementRepository = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _movStackingRepository = serviceProvider.GetRequiredService<IRepository<MovStacking>>();
        _auditLogRepository = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _logger = serviceProvider.GetRequiredService<ILogger<StackingMovementCommandHandler>>();
    }

    /// <summary>
    ///     Maneja el comando de movimiento de empalillado con validaciones robustas.
    /// </summary>
    /// <param name="request">Comando de movimiento de empalillado.</param>
    /// <param name="cancellationToken">Token de cancelación.</param>
    /// <returns>Resultado con el estado de la operación y respuesta.</returns>
    public async Task<Result<StackingMovementCommandResponse>> Handle(
        StackingMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            _logger.LogInformation("{LogPrefix} Iniciando movimiento de empalillado", LogPrefix);

            // Obtener códigos de lotes
            var requestedCodes = request
                .Batches.Select(b => b.BatchCode)
                .Where(code => !string.IsNullOrWhiteSpace(code))
                .ToHashSet();

            if (requestedCodes.Count == 0)
            {
                return Result<StackingMovementCommandResponse>.ValidationFailure(
                    new[] { "Debe especificar al menos un código de lote válido" }
                );
            }

            var batchesByCodesSpec = new BatchByCodeListSpec(requestedCodes);
            var batchesList = await _batchRepository.ListAsync(batchesByCodesSpec, cancellationToken);

            // Si un lote ya esta empalillado, no se puede empalillar nuevamente
            var invalidBatches = batchesList.Where(b => b.IsStacking).ToList();
            if (invalidBatches.Count > 0)
            {
                return Result<StackingMovementCommandResponse>.Failure(
                    "Hay lotes que ya están empalillados",
                    new StackingMovementCommandResponse
                    {
                        Data = [new { Errors = invalidBatches.Select(b => b.BatchCode).ToList() }]
                    }
                );
            }

            // Registrar movimiento
            var movementResult = await RegisterMovementAsync(request, cancellationToken);
            if (!movementResult.IsSuccess)
            {
                return Result<StackingMovementCommandResponse>.Failure(movementResult.Errors, movementResult.Message);
            }

            var movement = movementResult.Value!;

            // Procesar lotes válidos
            var processingResult = await ProcessValidBatchesAsync(batchesList, movement, request, cancellationToken);
            if (!processingResult.IsSuccess)
            {
                return Result<StackingMovementCommandResponse>.Failure(
                    processingResult.Errors,
                    processingResult.Message
                );
            }

            // Construir respuesta exitosa
            var successDto = BuildSuccessDto(batchesList, request);
            _logger.LogInformation("{LogPrefix} Movimiento de empalillado completado exitosamente", LogPrefix);

            return Result<StackingMovementCommandResponse>.Success(
                successDto,
                "Movimiento de empalillado completado exitosamente"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error inesperado durante el movimiento de empalillado", LogPrefix);
            return Result<StackingMovementCommandResponse>.InternalError(
                "Error interno durante el procesamiento del movimiento de empalillado"
            );
        }
    }

    #region Private Helpers

    /// <summary>
    ///     Registra el movimiento base de empalillado.
    /// </summary>
    private async Task<Result<BaseMovement>> RegisterMovementAsync(
        StackingMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var movementType = await _movementTypeRepository.FirstOrDefaultAsync(
                new MovementTypeByNameSpec(MovementType),
                cancellationToken
            );
            if (movementType == null)
            {
                return Result<BaseMovement>.Failure("Tipo de movimiento no encontrado");
            }

            var movement = BaseMovementBuilder
                .Create(movementType.Id, request.CreatedBy)
                .WithMovementInfo(request.StackingData.MovementInfo)
                .WithObservation(request.StackingData.Observation)
                .WithCapturerId(request.StackingData.CapturerId)
                .WithDocumentId(request.StackingData.DocumentId)
                .WithWarehouseId(request.StackingData.WarehouseId)
                .WithManufacturingOrderId(request.StackingData.ManufacturingOrderId)
                .WithMachineId(request.StackingData.MachineId)
                .Build();

            movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });


            await _movementRepository.AddAsync(movement, cancellationToken);
            await _movementRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("{LogPrefix} Movimiento registrado con ID: {MovementId}", LogPrefix, movement.Id);
            return Result<BaseMovement>.Success(movement, "Movimiento registrado exitosamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al registrar movimiento", LogPrefix);
            return Result<BaseMovement>.InternalError("Error al registrar el movimiento");
        }
    }

    /// <summary>
    ///     Procesa los lotes válidos para el movimiento de empalillado.
    /// </summary>
    private async Task<Result> ProcessValidBatchesAsync(
        IEnumerable<Batch> batches,
        BaseMovement movement,
        StackingMovementCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var processingErrors = new List<string>();
            var processedBatches = new List<Batch>();

            foreach (var batch in batches)
            {
                try
                {
                    // Vinculación lote-movimiento
                    var movStacking = MovStacking.Create(movement.Id, batch.Id);
                    await _movStackingRepository.AddAsync(movStacking, cancellationToken);

                    // Actualizar estado del lote
                    batch.IsStacking = true;
                    await _batchRepository.UpdateAsync(batch, cancellationToken);

                    // Auditoría
                    var auditLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "Empalillado",
                        "No empalillado",
                        "Empalillado",
                        request.CreatedBy
                    );
                    await _auditLogRepository.AddAsync(auditLog, cancellationToken);

                    processedBatches.Add(batch);
                    _logger.LogInformation(
                        "{LogPrefix} - Lote {BatchCode} empalillado exitosamente",
                        LogPrefix,
                        batch.BatchCode
                    );
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error al procesar lote {batch.BatchCode}: {ex.Message}";
                    processingErrors.Add(errorMessage);
                    _logger.LogError(ex, "{LogPrefix} - {ErrorMessage}", LogPrefix, errorMessage);
                }
            }

            // Guardar cambios
            await _batchRepository.SaveChangesAsync(cancellationToken);
            await _auditLogRepository.SaveChangesAsync(cancellationToken);

            if (processingErrors.Count > 0)
            {
                return Result.Failure(processingErrors, "Algunos lotes no pudieron ser procesados correctamente");
            }

            _logger.LogInformation(
                "{LogPrefix} - {ProcessedCount} lotes procesados exitosamente",
                LogPrefix,
                processedBatches.Count
            );
            return Result.Success($"Se procesaron {processedBatches.Count} lotes exitosamente");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error al procesar lotes", LogPrefix);
            return Result.InternalError("Error interno al procesar los lotes");
        }
    }

    /// <summary>
    ///     Construye el DTO de respuesta exitosa.
    /// </summary>
    private static StackingMovementCommandResponse BuildSuccessDto(
        List<Batch> batchesList,
        StackingMovementCommand request
    )
    {
        return new StackingMovementCommandResponse
        {
            Data =
            [
                new
                {
                    ProcessedBatches = batchesList.Count,
                    request.StackingData
                }
            ]
        };
    }

    #endregion
}

/// <summary>
///     DTO de lotes inválidos. Se hace record para inmutabilidad y mejor legibilidad.
/// </summary>
public sealed record InvalidStackingBatchInfo(int BatchId, string? BatchCode, string Reason);
