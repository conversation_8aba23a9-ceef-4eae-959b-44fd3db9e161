using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Application.Services;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Movements;
using SGP.Aserradero.Domain.Entities.Batches.Requests;
using SGP.Aserradero.Domain.Entities.Features.Baths;
using SGP.Aserradero.Domain.Entities.Features.Defects;
using SGP.Aserradero.Domain.Entities.Features.Destinations;
using SGP.Aserradero.Domain.Entities.Features.Markets;
using SGP.Aserradero.Domain.Entities.Features.Statuses;
using SGP.Aserradero.Domain.Entities.Features.Straps;
using SGP.Aserradero.Domain.Entities.Main.Documents;
using SGP.Aserradero.Domain.Entities.Main.Documents.Specs;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas;
using SGP.Aserradero.Domain.Entities.Material.MaterialDatas.Specs;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;
using SGP.Aserradero.Domain.Entities.Movements.Productions;

namespace SGP.Aserradero.Application.Modules.Movements.Productions;

public class BatchProductionCommandHandler : IRequestHandler<BatchProductionCommand, Result<bool>>
{
    private const string LogPrefix = "[Production][Create]";
    private const string FixedPrefix = "BA"; // Prefijo fijo al inicio
    private const string MovementType = "Produccion";
    private readonly IRepository<BatchMovement> _batchMovementRepo;
    private readonly IRepository<BatchProduction> _batchProductionRepo;
    private readonly IRepository<Batch> _batchReadRepo;
    private readonly IRepository<Batch> _batchRepo;
    private readonly IRepository<Bath> _bathRepo;
    private readonly IRepository<Defect> _defectRepo;
    private readonly IRepository<Destination> _destinationRepo;
    private readonly IRepository<Document> _documentRepo;
    private readonly ILogger<BatchProductionCommandHandler> _logger;
    private readonly IRepository<Market> _marketRepo;
    private readonly IRepository<MaterialData> _materialDataRepo;

    private readonly IRepository<BaseMovement> _movementRepo;
    private readonly IRepository<MovementType> _movementTypeRepo;
    private readonly PrintService _printService;
    private readonly IRepository<Status> _statusRepo;
    private readonly IRepository<Strap> _strapRepo;

    public BatchProductionCommandHandler(IServiceProvider serviceProvider)
    {
        _movementRepo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchProductionRepo = serviceProvider.GetRequiredService<IRepository<BatchProduction>>();
        _documentRepo = serviceProvider.GetRequiredService<IRepository<Document>>();
        _batchRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _batchMovementRepo = serviceProvider.GetRequiredService<IRepository<BatchMovement>>();
        _batchReadRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _materialDataRepo = serviceProvider.GetRequiredService<IRepository<MaterialData>>();
        _destinationRepo = serviceProvider.GetRequiredService<IRepository<Destination>>();
        _marketRepo = serviceProvider.GetRequiredService<IRepository<Market>>();
        _defectRepo = serviceProvider.GetRequiredService<IRepository<Defect>>();
        _bathRepo = serviceProvider.GetRequiredService<IRepository<Bath>>();
        _strapRepo = serviceProvider.GetRequiredService<IRepository<Strap>>();
        _statusRepo = serviceProvider.GetRequiredService<IRepository<Status>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _logger = serviceProvider.GetRequiredService<ILogger<BatchProductionCommandHandler>>();
        _printService = serviceProvider.GetRequiredService<PrintService>();
    }

    public async Task<Result<bool>> Handle(BatchProductionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{LogPrefix} Iniciando creación del movimiento de producción", LogPrefix);
        try
        {
            // Buscar tipo de movimiento por nombre
            var movementTypeByNameSpec = new MovementTypeByNameSpec(MovementType);
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(movementTypeByNameSpec, cancellationToken);
            if (movementType == null)
            {
                _logger.LogError(
                    "{LogPrefix} Tipo de movimiento '{MovementType}' no encontrado",
                    LogPrefix,
                    MovementType
                );
                return Result<bool>.NotFound($"Tipo de movimiento '{MovementType}' no encontrado");
            }

            // Validar que todos los IDs de referencia existen
            var validationResult = await ValidateForeignKeyReferences(request.ProductionData, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            // Crear material si los valores existentes difieren del nuevo
            var materialIdToUse = request.ProductionData.MaterialId;
            materialIdToUse = await ValidateAndCreateMaterial(request, cancellationToken, materialIdToUse);

            // Crear el movimiento
            var movement = BaseMovementBuilder
                .Create(movementType.Id, request.CreatedBy)
                .WithMovementInfo(request.MovementInfo)
                .WithObservation(request.Observation)
                .WithCapturerId(request.CapturerId)
                .WithDocumentId(request.DocumentId)
                .WithWarehouseId(request.WarehouseId)
                .WithManufacturingOrderId(request.ManufacturingOrderId)
                .WithMachineId(request.MachineId)
                .Build();

            movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });

            await _movementRepo.AddAsync(movement, cancellationToken);

            // Obtener el documento para acceder al código de OrderClass
            var document = await _documentRepo.FirstOrDefaultAsync(
                new DocumentByIdSpec(request.DocumentId.Value),
                cancellationToken
            );

            if (document?.OrderClass == null)
            {
                _logger.LogError("{LogPrefix} Documento no encontrado o sin OrderClass asociado", LogPrefix);
                return Result<bool>.NotFound("Documento no encontrado o sin OrderClass asociado");
            }

            var orderClassCode = document.OrderClass.Code;

            // Generate a single batch code
            var batchCode = await GenerateBatchCode(orderClassCode, cancellationToken);
            // Create and save single batch with production characteristics
            var batch = Batch.Create(
                new CreateBatchRequest(
                    batchCode,
                    materialIdToUse,
                    request.ProductionData.DestinationId,
                    request.ProductionData.MarketId,
                    request.ProductionData.DefectId,
                    request.ProductionData.BathId,
                    request.ProductionData.StrapId,
                    request.ProductionData.StatusId,
                    request.ProductionData.IsStacking,
                    request.ProductionData.IsHt,
                    request.ProductionData.IsPainting,
                    request.ProductionData.IsSeparating,
                    request.ProductionData.IsTrimmed,
                    request.ProductionData.IsWoodenBlock,
                    request.WarehouseId.Value,
                    request.CreatedBy,
                    "Production"
                )
            );
            await _batchRepo.AddAsync(batch, cancellationToken);

            // Create and save BatchMovement
            var batchMovement = BatchMovement.Create(batch.Id, movement.Id, movementType.Id);
            await _batchMovementRepo.AddAsync(batchMovement, cancellationToken);

            // Create and save BatchProduction with production-specific data
            var batchProduction = BatchProduction.Create(batchMovement.Id);
            await _batchProductionRepo.AddAsync(batchProduction, cancellationToken);

            // Try to print batch - if it fails, rollback all created entities
            try
            {
                await _printService.PrintBatch(
                    batchCode,
                    request.ProductionData.SelectedPrinterId,
                    request.ProductionData.SelectedTemplate,
                    request.ProductionData.PrintQuantity
                );

                _logger.LogInformation(
                    "{LogPrefix} - Movimiento de producción creado exitosamente para lote {BatchCode}",
                    LogPrefix,
                    batchCode
                );
                return Result<bool>.Success(true, "Producción creada exitosamente", 201);
            }
            catch (Exception printEx)
            {
                _logger.LogError(
                    printEx,
                    "{LogPrefix} - Error en impresión para lote {BatchCode}",
                    LogPrefix,
                    batchCode
                );

                // Perform rollback of all created entities
                // await PerformRollback(batchProduction, batchMovement, batch, movement, batchCode);

                return Result<bool>.Success(true, "La producción se ha creado exitosamente, pero no se pudo imprimir");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al crear el movimiento de producción", LogPrefix);
            return Result<bool>.InternalError($"{LogPrefix} Error al crear el movimiento de producción");
        }
    }

    private async Task<int> ValidateAndCreateMaterial(
        BatchProductionCommand request,
        CancellationToken cancellationToken,
        int materialIdToUse
    )
    {
        var materialDataByIdSpec = new MaterialDataByIdSpec(request.ProductionData.MaterialId);
        var existingMaterial = await _materialDataRepo.FirstOrDefaultAsync(materialDataByIdSpec, cancellationToken);

        if (existingMaterial != null)
        {
            // Verificar si algún campo difiere del request
            var materialFieldsChanged =
                existingMaterial.NameLength != request.ProductionData.MaterialLarge
                || existingMaterial.Pieces != request.ProductionData.MaterialQuantity;

            if (materialFieldsChanged)
            {
                // Buscar un material existente con los valores similares al request
                var allMaterials = await _materialDataRepo.ListAsync(cancellationToken);
                var similarMaterial = allMaterials.FirstOrDefault(m =>
                    m.MaterialClassId == existingMaterial.MaterialClassId
                    && m.SapCode == existingMaterial.SapCode
                    && m.MaterialTypeId == existingMaterial.MaterialTypeId
                    && m.MaterialFamilyId == existingMaterial.MaterialFamilyId
                    && m.MaterialSubFamilyId == existingMaterial.MaterialSubFamilyId
                    && m.MaterialFinishId == existingMaterial.MaterialFinishId
                    && m.MaterialStatusId == existingMaterial.MaterialStatusId
                    && m.NameThickness == existingMaterial.NameThickness
                    && m.NameWidth == existingMaterial.NameWidth
                    && m.NameLength == request.ProductionData.MaterialLarge
                    && m.Pieces == request.ProductionData.MaterialQuantity
                );

                if (similarMaterial != null)
                {
                    // Usar el material existente similar
                    materialIdToUse = similarMaterial.Id;
                    _logger.LogInformation(
                        "{LogPrefix} - Usando material existente con ID {ExistingMaterialId} que coincide con los valores del request",
                        LogPrefix,
                        similarMaterial.Id
                    );
                }
                else
                {
                    // Crear un nuevo material con los datos del existente y los valores que difieren
                    _logger.LogInformation(
                        "{LogPrefix} Creando material nuevo basado en {MaterialId} debido a diferencias en campos",
                        LogPrefix,
                        request.ProductionData.MaterialId
                    );

                    var newLarge = string.Empty;
                    double? newPieces = null;

                    if (request.ProductionData.MaterialLarge != "0")
                    {
                        newLarge = request.ProductionData.MaterialLarge;
                    }
                    else
                    {
                        newLarge = existingMaterial.NameLength;
                    }

                    if (request.ProductionData.MaterialQuantity != 0)
                    {
                        newPieces = request.ProductionData.MaterialQuantity;
                    }
                    else
                    {
                        newPieces = existingMaterial.Pieces;
                    }

                    // Generar nueva descripcion usando los nuevos valores
                    var newDescription =
                        $"{existingMaterial.MaterialFamily!.Code} "
                        + $"{existingMaterial.MaterialSubFamily!.Code} "
                        + $"{existingMaterial.MaterialFinish!.Code} "
                        + $"{existingMaterial.MaterialStatus!.Code} "
                        + $"{existingMaterial.NameThickness}x"
                        + $"{existingMaterial.NameWidth}x"
                        + $"{newLarge}x"
                        + $"{newPieces}";

                    var newMaterial = new MaterialData
                    {
                        MaterialClassId = existingMaterial.MaterialClassId,
                        SapCode = existingMaterial.SapCode,
                        Description = newDescription,
                        DescriptionAlt = existingMaterial.DescriptionAlt,
                        MaterialTypeId = existingMaterial.MaterialTypeId,
                        Dimension = existingMaterial.Dimension,
                        MaterialFamilyId = existingMaterial.MaterialFamilyId,
                        MaterialSubFamilyId = existingMaterial.MaterialSubFamilyId,
                        MaterialFinishId = existingMaterial.MaterialFinishId,
                        MaterialStatusId = existingMaterial.MaterialStatusId,
                        NameThickness = existingMaterial.NameThickness,
                        NameWidth = existingMaterial.NameWidth,
                        NameLength = newLarge,
                        Pieces = newPieces,
                        Weight = existingMaterial.Weight,
                        Height = existingMaterial.Height,
                        Width = existingMaterial.Width,
                        Price = existingMaterial.Price,
                        CubicFactorMillimeters = existingMaterial.CubicFactorMillimeters,
                        CubicFactorInches = existingMaterial.CubicFactorInches,
                        IsBlocked = existingMaterial.IsBlocked,
                        CreatedBy = request.ProductionData.UserId,
                        Created = DateTime.Now,
                        LastModifiedBy = request.ProductionData.UserId,
                        LastModified = DateTime.Now
                    };

                    await _materialDataRepo.AddAsync(newMaterial, cancellationToken);
                    materialIdToUse = newMaterial.Id;

                    _logger.LogInformation(
                        "{LogPrefix} Nuevo material creado con ID {NewMaterialId}",
                        LogPrefix,
                        newMaterial.Id
                    );
                }
            }
        }

        return materialIdToUse;
    }

    /// <summary>
    ///     Genera un código de lote único
    /// </summary>
    /// <param name="orderClassCode">Código de la clase de orden</param>
    /// <param name="cancellationToken">Token de cancelación</param>
    /// <returns>Código de lote único</returns>
    private async Task<string> GenerateBatchCode(string orderClassCode, CancellationToken cancellationToken)
    {
        var basePattern = $"{FixedPrefix}{orderClassCode}";

        var existingBatches = await _batchReadRepo.ListAsync(cancellationToken);
        var existingCodes = existingBatches
            .Where(x => x.BatchCode.StartsWith(basePattern))
            .Select(x => x.BatchCode)
            .ToList();

        var (prefixCounter, numberIncremental) = GetNextIncrementalValues(basePattern, existingCodes);

        return $"{basePattern}{prefixCounter}{numberIncremental:D4}";
    }

    /// <summary>
    ///     Calcula los siguientes valores incrementales para el prefijo contador y número incremental
    /// </summary>
    /// <param name="basePattern">Patrón base (BA + OrderClassCode)</param>
    /// <param name="existingCodes">Códigos existentes</param>
    /// <returns>Tuple con prefijo contador y número incremental</returns>
    private static (string prefixCounter, int numberIncremental) GetNextIncrementalValues(
        string basePattern,
        List<string> existingCodes
    )
    {
        if (existingCodes.Count == 0)
        {
            // Primer lote: empezar con AA0001
            return ("AA", 1);
        }

        var maxPrefix = "AA";
        var maxNumber = 0;

        foreach (var code in existingCodes)
        {
            if (code.Length >= basePattern.Length + 6) // BasePattern + 2 letras + 4 números
            {
                var suffix = code.Substring(basePattern.Length);
                if (suffix.Length >= 6)
                {
                    var prefix = suffix.Substring(0, 2);
                    if (int.TryParse(suffix.Substring(2, 4), out var number))
                    {
                        // Comparar prefijos alfabéticamente y números
                        var comparison = string.Compare(prefix, maxPrefix, StringComparison.Ordinal);
                        if (comparison > 0 || (comparison == 0 && number > maxNumber))
                        {
                            maxPrefix = prefix;
                            maxNumber = number;
                        }
                    }
                }
            }
        }

        // Incrementar el número
        var nextNumber = maxNumber + 1;
        var nextPrefix = maxPrefix;

        // Si el número llega a 10000, incrementar el prefijo y resetear el número
        if (nextNumber < 10000)
        {
            return (nextPrefix, nextNumber);
        }

        nextPrefix = IncrementPrefix(maxPrefix);
        nextNumber = 1;

        return (nextPrefix, nextNumber);
    }

    /// <summary>
    ///     Incrementa el prefijo de letras (AA -> AB -> ... -> AZ -> BA -> BB -> ... -> ZZ)
    /// </summary>
    /// <param name="prefix">Prefijo actual</param>
    /// <returns>Siguiente prefijo</returns>
    private static string IncrementPrefix(string prefix)
    {
        if (prefix.Length != 2)
        {
            return "AA";
        }

        var chars = prefix.ToCharArray();

        // Incrementar la segunda letra
        if (chars[1] < 'Z')
        {
            chars[1]++;
        }
        else
        {
            // La segunda letra llega a Z, incrementar la primera y resetear la segunda
            chars[1] = 'A';
            if (chars[0] < 'Z')
            {
                chars[0]++;
            }
            else
            {
                // Llegamos a ZZ, reiniciar a AA (aunque esto sería muy raro en la práctica)
                chars[0] = 'A';
                chars[1] = 'A';
            }
        }

        return new string(chars);
    }

    private async Task<Result<bool>> ValidateForeignKeyReferences(
        BatchProductionCommand.ProductionLine productionData,
        CancellationToken cancellationToken
    )
    {
        // Validar MaterialId
        var material = await _materialDataRepo.GetByIdAsync(productionData.MaterialId, cancellationToken);
        if (material == null)
        {
            _logger.LogError(
                "{LogPrefix} - Material con ID {MaterialId} no encontrado",
                LogPrefix,
                productionData.MaterialId
            );
            return Result<bool>.Failure($"Material con ID {productionData.MaterialId} no encontrado");
        }

        // Validar DestinationId
        var destination = await _destinationRepo.GetByIdAsync(productionData.DestinationId, cancellationToken);
        if (destination == null)
        {
            _logger.LogError(
                "{LogPrefix} - Destino con ID {DestinationId} no encontrado",
                LogPrefix,
                productionData.DestinationId
            );
            return Result<bool>.Failure($"Destino con ID {productionData.DestinationId} no encontrado");
        }

        // Validar MarketId
        var market = await _marketRepo.GetByIdAsync(productionData.MarketId, cancellationToken);
        if (market == null)
        {
            _logger.LogError(
                "{LogPrefix} - Mercado con ID {MarketId} no encontrado",
                LogPrefix,
                productionData.MarketId
            );
            return Result<bool>.Failure($"Mercado con ID {productionData.MarketId} no encontrado");
        }

        // Validar DefectId
        var defect = await _defectRepo.GetByIdAsync(productionData.DefectId, cancellationToken);
        if (defect == null)
        {
            _logger.LogError(
                "{LogPrefix} - Defecto con ID {DefectId} no encontrado",
                LogPrefix,
                productionData.DefectId
            );
            return Result<bool>.Failure($"Defecto con ID {productionData.DefectId} no encontrado");
        }

        // Validar BathId
        var bath = await _bathRepo.GetByIdAsync(productionData.BathId, cancellationToken);
        if (bath == null)
        {
            _logger.LogError("{LogPrefix} - Baño con ID {BathId} no encontrado", LogPrefix, productionData.BathId);
            return Result<bool>.Failure($"Baño con ID {productionData.BathId} no encontrado");
        }

        // Validar StrapId
        var strap = await _strapRepo.GetByIdAsync(productionData.StrapId, cancellationToken);
        if (strap == null)
        {
            _logger.LogError("{LogPrefix} - Cinta con ID {StrapId} no encontrado", LogPrefix, productionData.StrapId);
            return Result<bool>.Failure($"Cinta con ID {productionData.StrapId} no encontrado");
        }

        // Validar StatusId
        var status = await _statusRepo.GetByIdAsync(productionData.StatusId, cancellationToken);
        if (status == null)
        {
            _logger.LogError(
                "{LogPrefix} - Estado con ID {StatusId} no encontrado",
                LogPrefix,
                productionData.StatusId
            );
            return Result<bool>.Failure($"Estado con ID {productionData.StatusId} no encontrado");
        }

        return Result<bool>.Success(true);
    }

    /// <summary>
    ///     Realiza rollback eliminando todas las entidades creadas cuando falla la impresión
    /// </summary>
    /// <param name="batchProduction">BatchProduction a eliminar</param>
    /// <param name="batchMovement">BatchMovement a eliminar</param>
    /// <param name="batch">BatchEntity a eliminar</param>
    /// <param name="baseMovement">Movement a eliminar</param>
    /// <param name="batchCode">Código del lote para logging</param>
    private async Task PerformRollback(
        BatchProduction batchProduction,
        BatchMovement batchMovement,
        Batch batch,
        BaseMovement baseMovement,
        string batchCode
    )
    {
        try
        {
            _logger.LogWarning(
                "{LogPrefix} - Iniciando rollback para lote {BatchCode}. Eliminando entidades en orden inverso",
                LogPrefix,
                batchCode
            );

            // Delete in reverse order of creation to maintain referential integrity

            // 1. Delete BatchProduction
            if (batchProduction?.Id > 0)
            {
                await _batchProductionRepo.DeleteAsync(batchProduction);
                _logger.LogDebug("{LogPrefix} - BatchProduction eliminado (ID: {Id})", LogPrefix, batchProduction.Id);
            }

            // 2. Delete BatchMovement
            if (batchMovement?.Id > 0)
            {
                await _batchMovementRepo.DeleteAsync(batchMovement);
                _logger.LogDebug("{LogPrefix} - BatchMovement eliminado (ID: {Id})", LogPrefix, batchMovement.Id);
            }

            // 3. Delete BatchEntity
            if (batch?.Id > 0)
            {
                await _batchRepo.DeleteAsync(batch);
                _logger.LogDebug(
                    "{LogPrefix} - BatchEntity eliminado (ID: {Id}, Code: {Code})",
                    LogPrefix,
                    batch.Id,
                    batch.BatchCode
                );
            }

            // 4. Delete Movement
            if (baseMovement?.Id > 0)
            {
                await _movementRepo.DeleteAsync(baseMovement);
                _logger.LogDebug("{LogPrefix} - Movement eliminado (ID: {Id})", LogPrefix, baseMovement.Id);
            }

            _logger.LogInformation(
                "{LogPrefix} - Rollback completado exitosamente para lote {BatchCode}",
                LogPrefix,
                batchCode
            );
        }
        catch (Exception rollbackEx)
        {
            _logger.LogError(
                rollbackEx,
                "{LogPrefix} - Error crítico durante rollback para lote {BatchCode}. "
                + "Es posible que queden registros huérfanos en la base de datos",
                LogPrefix,
                batchCode
            );

            // Re-throw to indicate critical rollback failure
            throw new InvalidOperationException(
                $"Error crítico durante rollback para lote {batchCode}: {rollbackEx.Message}. "
                + "Contacte al administrador del sistema para verificar la integridad de los datos.",
                rollbackEx
            );
        }
    }
}
