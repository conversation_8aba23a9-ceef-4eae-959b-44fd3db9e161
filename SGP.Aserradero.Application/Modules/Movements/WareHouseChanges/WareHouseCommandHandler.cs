using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SGP.Aserradero.Domain.Entities.Batches.Base;
using SGP.Aserradero.Domain.Entities.Batches.Logs;
using SGP.Aserradero.Domain.Entities.Batches.Requests;
using SGP.Aserradero.Domain.Entities.Main.Warehouses;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements;
using SGP.Aserradero.Domain.Entities.Movements.BaseMovements.Events;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes;
using SGP.Aserradero.Domain.Entities.Movements.MovementTypes.Specs;
using SGP.Aserradero.Domain.Entities.Movements.WarehouseChanges;

namespace SGP.Aserradero.Application.Modules.Movements.WareHouseChanges;

public class WareHouseCommandHandler : IRequestHandler<WareHouseCommand, Result<WareHouseCommandResponse>>
{
    private const string LogPrefix = "[WarehouseChange][Movement]";
    private readonly IRepository<BatchAuditLog> _auditLogRepo;
    private readonly IRepository<Batch> _batchRepo;
    private readonly ILogger<WareHouseCommandHandler> _logger;

    private readonly IRepository<BaseMovement> _movementRepo;
    private readonly IRepository<MovementType> _movementTypeRepo;
    private readonly IRepository<MovWareHouseChange> _movWareHouseChangeRepo;
    private readonly IRepository<Warehouse> _warehouseRepo;

    public WareHouseCommandHandler(IServiceProvider serviceProvider)
    {
        _movementRepo = serviceProvider.GetRequiredService<IRepository<BaseMovement>>();
        _batchRepo = serviceProvider.GetRequiredService<IRepository<Batch>>();
        _movWareHouseChangeRepo = serviceProvider.GetRequiredService<IRepository<MovWareHouseChange>>();
        _movementTypeRepo = serviceProvider.GetRequiredService<IRepository<MovementType>>();
        _auditLogRepo = serviceProvider.GetRequiredService<IRepository<BatchAuditLog>>();
        _warehouseRepo = serviceProvider.GetRequiredService<IRepository<Warehouse>>();
        _logger = serviceProvider.GetRequiredService<ILogger<WareHouseCommandHandler>>();
    }

    public async Task<Result<WareHouseCommandResponse>> Handle(
        WareHouseCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} - Iniciando movimiento de warehouse", LogPrefix);

        var batchCodes = request.Batches.Select(b => b.BatchCode).ToList();
        var invalidBatches = new List<InvalidWarehouseBatchInfo>();
        var validBatches = new List<Batch>();

        try
        {
            var movementType = await _movementTypeRepo.FirstOrDefaultAsync(
                new MovementTypeByNameSpec("Cambio Almacen"),
                cancellationToken
            );
            BaseMovement? movement = null;

            // Validar que el warehouse existe
            var warehousesList = await _warehouseRepo.ListAsync(cancellationToken);
            var targetWarehouse = warehousesList.FirstOrDefault(w => w.Id == request.WareHouseData.WarehouseId);
            if (targetWarehouse == null)
            {
                _logger.LogError(
                    "{LogPrefix} - Warehouse con ID {WarehouseId} no encontrado",
                    LogPrefix,
                    request.WareHouseData.WarehouseId
                );

                return Result<WareHouseCommandResponse>.NotFound("Almacen no encontrado");
            }

            // Buscar todos los lotes solicitados
            var batches = await _batchRepo.ListAsync(cancellationToken);
            var foundBatches = batches.Where(b => batchCodes.Contains(b.BatchCode)).ToList();
            var foundBatchCodes = foundBatches.Select(b => b.BatchCode).ToHashSet();
            var notFoundBatchCodes = batchCodes.Except(foundBatchCodes).ToList();

            // Agregar lotes no encontrados
            foreach (var batchCode in notFoundBatchCodes)
            {
                invalidBatches.Add(
                    new InvalidWarehouseBatchInfo
                    {
                        BatchId = 0,
                        BatchCode = batchCode,
                        Reason = "Lote no encontrado"
                    }
                );
                _logger.LogWarning("{LogPrefix} - Lote con código {BatchCode} no encontrado", LogPrefix, batchCode);
            }

            // Validar que los lotes encontrados pueden ser movidos
            foreach (var batch in foundBatches)
            {
                // Verificar si el lote puede ser movido al warehouse
                // Puedes agregar validaciones específicas aquí según tus reglas de negocio
                var canBeMoved = true; // Por ahora acepta todos los lotes encontrados

                if (!canBeMoved)
                {
                    invalidBatches.Add(
                        new InvalidWarehouseBatchInfo
                        {
                            BatchId = batch.Id,
                            BatchCode = batch.BatchCode,
                            Reason = "El lote no puede ser movido al warehouse en su estado actual"
                        }
                    );
                    _logger.LogWarning(
                        "{LogPrefix} - Lote {BatchCode} no puede ser movido. Estado actual: {Status}",
                        LogPrefix,
                        batch.BatchCode,
                        batch.Status
                    );
                    continue;
                }

                validBatches.Add(batch);
            }

            // Diccionario para guardar información de warehouses anteriores
            var batchWarehouseInfo = new Dictionary<int, (int? PreviousWarehouseId, string? PreviousWarehouseName)>();

            if (validBatches.Count != 0)
            {
                // Crear el movimiento principal
                movement = BaseMovementBuilder
                    .Create(movementType.Id, request.CreatedBy)
                    .WithMovementInfo(request.WareHouseData.MovementInfo)
                    .WithObservation(request.WareHouseData.Observation)
                    .WithCapturerId(request.WareHouseData.CapturerId)
                    .WithDocumentId(request.WareHouseData.DocumentId)
                    .WithWarehouseId(request.WareHouseData.WarehouseId)
                    .WithManufacturingOrderId(request.WareHouseData.ManufacturingOrderId)
                    .WithMachineId(request.WareHouseData.MachineId)
                    .Build();

                movement.QueueDomainEvent(new MovementCreatedEvent { BaseMovement = movement });

                await _movementRepo.AddAsync(movement, cancellationToken);

                // Procesar cada lote válido
                foreach (var batch in validBatches)
                {
                    var previousWarehouseId = batch.WarehouseId;
                    var oldWarehouseName = warehousesList.FirstOrDefault(w => w.Id == previousWarehouseId)?.Name;

                    // Guardar información del warehouse anterior
                    batchWarehouseInfo[batch.Id] = (previousWarehouseId, oldWarehouseName);

                    // Actualizar WarehouseId del lote
                    batch.UpdateCharacteristics(
                        new UpdateBatchRequest(
                            WarehouseId: request.WareHouseData.WarehouseId,
                            ModifiedBy: request.CreatedBy
                        )
                    );
                    await _batchRepo.UpdateAsync(batch, cancellationToken);

                    // Crear MovWareHouseChange (vinculación lote-movimiento específica para warehouse change)
                    var movWareHouseChange = MovWareHouseChange.Create(movement.Id, batch.Id);
                    await _movWareHouseChangeRepo.AddAsync(movWareHouseChange, cancellationToken);

                    // Obtener el nombre del warehouse destino
                    var newWarehouseName = targetWarehouse.Name;

                    // Registrar auditoría del cambio de WarehouseId
                    var auditLog = BatchAuditLog.Create(
                        batch.Id,
                        movement.Id,
                        "WarehouseId",
                        oldWarehouseName,
                        newWarehouseName,
                        request.CreatedBy
                    );
                    await _auditLogRepo.AddAsync(auditLog, cancellationToken);

                    _logger.LogInformation(
                        "{LogPrefix} - Lote {BatchCode} movido al warehouse {WarehouseId} (anterior: {PreviousWarehouse})",
                        LogPrefix,
                        batch.BatchCode,
                        request.WareHouseData.WarehouseId,
                        oldWarehouseName ?? "Sin warehouse"
                    );
                }
            }

            _logger.LogInformation(
                "{LogPrefix} - Movimiento de warehouse completado. Procesados: {ProcessedCount}, Inválidos: {InvalidCount}",
                LogPrefix,
                validBatches.Count,
                invalidBatches.Count
            );

            // Crear el DTO de respuesta personalizado
            var finalResponseDto = new WareHouseCommandResponse
            {
                Data =
                [
                    new
                    {
                        ProcessedBatches = validBatches.Count,
                        InvalidBatches = invalidBatches,
                        Success = validBatches.Count > 0,
                        WarehouseData = request.WareHouseData,
                        ValidBatches = validBatches
                            .Select(b => new
                            {
                                b.Id,
                                b.BatchCode,
                                b.Status,
                                PreviousWarehouseId = batchWarehouseInfo.ContainsKey(b.Id)
                                    ? batchWarehouseInfo[b.Id].PreviousWarehouseId
                                    : null,
                                PreviousWarehouseName = batchWarehouseInfo.ContainsKey(b.Id)
                                    ? batchWarehouseInfo[b.Id].PreviousWarehouseName
                                    : null,
                                NewWarehouseId = request.WareHouseData.WarehouseId,
                                NewWarehouseName = targetWarehouse.Name,
                                CurrentWarehouseId = b.WarehouseId
                            })
                            .ToList()
                    }
                ]
            };

            return Result<WareHouseCommandResponse>.Success(
                finalResponseDto,
                $"Movimiento de warehouse completado. Procesados: {validBatches.Count}, Inválidos: {invalidBatches.Count}"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} - Error al procesar el movimiento de warehouse", LogPrefix);

            return Result<WareHouseCommandResponse>.InternalError(
                $"Error interno al crear movimiento de warehouse: {ex.Message}"
            );
        }
    }
}

public class InvalidWarehouseBatchInfo
{
    public int BatchId { get; set; }
    public string? BatchCode { get; set; }
    public string Reason { get; set; } = string.Empty;
}
