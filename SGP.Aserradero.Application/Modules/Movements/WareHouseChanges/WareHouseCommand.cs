using Leonera_API.Common.Core.Models;
using MediatR;
using SGP.Aserradero.Application.Modules.Movements.Base.Commands.Create;

namespace SGP.Aserradero.Application.Modules.Movements.WareHouseChanges;

public record WareHouseCommand : IRequest<Result<WareHouseCommandResponse>>
{
    public required CreateMovementCommand WareHouseData { get; set; }
    public int CreatedBy { get; set; }
    public int ShiftId { get; set; }
    public DateTime MovementDate { get; set; } = DateTime.Now;

    public IEnumerable<BatchesToWarehouse> Batches { get; init; }

    public class BatchesToWarehouse
    {
        public string BatchCode { get; init; } = string.Empty;
    }
}
