using System.Net;
using Leonera_API_ExternalServices;
using Leonera_API_ExternalServices.Modules;
using Leonera_API_ExternalServices.Modules.InventoryGenExits.Dtos;
using Leonera_API_ExternalServices.Modules.InventoryGenExits.Models;
using Leonera_API.Common.Core.Domain;
using Leonera_API.Common.Core.Exceptions;
using Leonera_API.Common.Core.Models;
using Leonera_API.Common.Domain.Entities.Auth;
using Leonera_API.Common.Domain.Entities.Auth.Specs;
using Leonera_API.Common.Domain.Entities.GeneralData;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SGP.Aserradero.Domain.Entities.Movements.BatchReclassifications;
using SGP.Aserradero.Domain.Entities.Movements.BatchReclassificationsData;

namespace SGP.Aserradero.Application.Modules.Movements.BatchReclasifications.Reduction;

public sealed class ReductionBatchReclassificationCommandHandler
    : IRequestHandler<ReductionBatchReclassificationCommand, Result<ReductionBatchReclassificationCommandResponse>>
{
    private const string LogPrefix = "[Reclassification][Create]";
    private readonly IRepository<ReclassificationData> _batchReclasificationsDataRepository;
    private readonly IRepository<Reclassification> _batchReclasificationsRepository;
    private readonly ILogger<ReductionBatchReclassificationCommandHandler> _logger;
    private readonly IRepository<SapDatabases> _sapDatabasesRepository;
    private readonly SapServiceLayerFacade _sapService;
    private readonly IRepository<UserEntity> _userRepository;

    public ReductionBatchReclassificationCommandHandler(IServiceProvider serviceProvider)
    {
        _batchReclasificationsDataRepository = serviceProvider.GetRequiredService<IRepository<ReclassificationData>>();
        _batchReclasificationsRepository = serviceProvider.GetRequiredService<IRepository<Reclassification>>();
        _sapDatabasesRepository = serviceProvider.GetRequiredService<IRepository<SapDatabases>>();
        _userRepository = serviceProvider.GetRequiredService<IRepository<UserEntity>>();
        _sapService = serviceProvider.GetRequiredService<SapServiceLayerFacade>();
        _logger = serviceProvider.GetRequiredService<ILogger<ReductionBatchReclassificationCommandHandler>>();
    }

    public async Task<Result<ReductionBatchReclassificationCommandResponse>> Handle(
        ReductionBatchReclassificationCommand request,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation("{LogPrefix} Comenzando reclasificacion", LogPrefix);
        try
        {
            var userByRutSpec = new UserByRutSpec(request.UserRut);
            var user = await _userRepository.FirstOrDefaultAsync(userByRutSpec, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("{LogPrefix} Usuario no encontrado", LogPrefix);
                return Result<ReductionBatchReclassificationCommandResponse>.Failure("Usuario no encontrado");
            }

            if (!await ValidateBatchs(request.Data, cancellationToken))
            {
                _logger.LogWarning("{LogPrefix} Ya existe una reclasificacion para alguno de los lotes", LogPrefix);
                return Result<ReductionBatchReclassificationCommandResponse>.Conflict(
                    "Ya existe una reclasificacion para alguno de los lotes"
                );
            }

            // Guardar la reclasificación en la base de datos
            var reclassification = Reclassification.Create(user.Id, request.Comments, "Pendiente", request.CompanyId);
            await _batchReclasificationsRepository.AddAsync(reclassification, cancellationToken);

            // Guardar los datos de la reclasificación en la base de datos
            var reclassificationData = new List<ReclassificationData>();
            foreach (var data in request.Data)
            {
                var reclassificationDataItem = ReclassificationData.Create(
                    reclassification.Id,
                    true,
                    data.BatchNum,
                    data.ItemCode,
                    data.WarehouseCode,
                    data.Quantity,
                    "909901"
                );
                reclassificationData.Add(reclassificationDataItem);
            }

            await _batchReclasificationsDataRepository.AddRangeAsync(reclassificationData, cancellationToken);

            // Enviar la reclasificación a SAP
            var payload = GeneratePayload(request, reclassificationData);
            try
            {
                var inventoryGenExitsResponse = await GenerateInventoryGenExits(request.CompanyId, payload);
                // Recorrer DocumentLines para extraer la cantidad y volumétrica
                var totalQuantity = 0.0;
                var totalVolume = 0.0;
                foreach (var docLine in inventoryGenExitsResponse.DocumentLines)
                {
                    totalQuantity += docLine.UCantidadm3 ?? 0;
                    totalVolume += docLine.UVolumenm3 ?? 0;
                }

                reclassification.UpdateReduction(
                    inventoryGenExitsResponse.DocNum.ToString(),
                    inventoryGenExitsResponse.DocEntry.ToString(),
                    totalQuantity,
                    totalVolume
                );
                await _batchReclasificationsRepository.UpdateAsync(reclassification, cancellationToken);

                var response = new ReductionBatchReclassificationCommandResponse
                {
                    ReclassificationId = reclassification.Id,
                    SapDocNum = inventoryGenExitsResponse.DocNum.ToString(),
                    SapDocEntry = inventoryGenExitsResponse.DocEntry.ToString(),
                };

                return Result<ReductionBatchReclassificationCommandResponse>.Success(
                    response,
                    "Reclasificacion creada correctamente"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{LogPrefix} Error al crear reclasificacion", LogPrefix);
                return Result<ReductionBatchReclassificationCommandResponse>.InternalError(
                    "Error al crear reclasificacion, hubo un error al enviar la reclasificacion a SAP"
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{LogPrefix} Error al crear reclasificacion", LogPrefix);
            return Result<ReductionBatchReclassificationCommandResponse>.InternalError(
                "Error al crear reclasificacion"
            );
        }
    }

    private async Task<bool> ValidateBatchs(
        List<ReductionBatchReclassificationCommand.CreateReclassificationData> requestData,
        CancellationToken cancellationToken
    )
    {
        // Extraer los números de lote de la solicitud
        var batchNums = requestData.Select(x => x.BatchNum).ToList();

        // Obtener todos los datos de reclasificación
        var data = await _batchReclasificationsDataRepository.ListAsync(cancellationToken);

        // Filtrar los datos que contienen los números de lote de la solicitud
        var validationResult = data.Where(x => batchNums.Contains(x.BatchNum)).ToList();

        return validationResult.Count == 0;
    }

    private static InventoryGenExitsDto GeneratePayload(
        ReductionBatchReclassificationCommand request,
        List<ReclassificationData> reclassificationData
    )
    {
        // Get differents itemcode and warehouse combinations
        var itemGroups = reclassificationData.GroupBy(x => new { x.ItemCode, x.Warehouse }).ToList();

        // Header
        var payload = new InventoryGenExitsDto
        {
            DocDate = DateTime.Now,
            Comments = request.Comments,
            DocumentLines = [],
        };

        // Document lines
        var documentLines = new List<InventoryGenExitsDto.ExitsDocumentLine>();

        // For each unique itemcode and warehouse combination
        foreach (var group in itemGroups)
        {
            var totalQuantity = group.Sum(x => x.Quantity);

            var docLine = new InventoryGenExitsDto.ExitsDocumentLine
            {
                ItemCode = group.Key.ItemCode,
                WarehouseCode = group.Key.Warehouse,
                Quantity = totalQuantity,
                BatchNumbers =
                [
                    .. group.Select(x => new InventoryGenExitsDto.BatchNumbersLine
                    {
                        BatchNumber = x.BatchNum,
                        Quantity = x.Quantity,
                    }),
                ],
                CostingCode = "909901",
                CostingCode2 = "909900",
                CostingCode3 = "909000",
                CostingCode4 = "900000",
                Movement = "N",
            };
            documentLines.Add(docLine);
        }

        payload.DocumentLines = documentLines;
        return payload;
    }

    // Generate Inventory Gen Exits
    private async Task<InventoryGenExitsCreatedResponse.Root?> GenerateInventoryGenExits(
        int companyId,
        InventoryGenExitsDto inventoryGenExits
    )
    {
        var sapDatabase = await _sapDatabasesRepository.GetByIdAsync(companyId);
        if (sapDatabase == null)
        {
            throw new ArgumentException("No se ha encontrado la base de datos de SAP");
        }

        var auth = await _sapService.Login.ConnectSl2(sapDatabase.DatabaseName);
        if (auth == null)
        {
            throw new ArgumentException("Ha ocurrido un problema al iniciar sesion en SAP");
        }

        // Create Inventory Gen Entry
        var response = await _sapService.InventoryGenExits.MakeInventoryExit(inventoryGenExits, auth);
        if (response.Content != null && response.StatusCode != HttpStatusCode.Created)
        {
            var error = JsonConvert.DeserializeObject<SapErrorClass.Root>(response.Content);
            throw new SapErrorException(error.Error.Message);
        }

        var deserializedResponse = JsonConvert.DeserializeObject<InventoryGenExitsCreatedResponse.Root>(
            response.Content
        );
        return deserializedResponse;
    }
}
