using FluentValidation;
using Leonera_API.Common.Core.Models;
using MediatR;

namespace SGP.Aserradero.Application.Modules.Movements.BatchReclasifications.Reduction;

public sealed record ReductionBatchReclassificationCommand
    : IRequest<Result<ReductionBatchReclassificationCommandResponse>>
{
    public string UserRut { get; init; }
    public int CompanyId { get; init; }
    public string Comments { get; init; }
    public List<CreateReclassificationData> Data { get; init; }

    public class CreateReclassificationData
    {
        public string BatchNum { get; init; }
        public string ItemCode { get; init; }
        public string WarehouseCode { get; init; }
        public double Quantity { get; init; }
    }
}

// Validador

public sealed class CreateBatchReclassificationCommandValidator
    : AbstractValidator<ReductionBatchReclassificationCommand>
{
    public CreateBatchReclassificationCommandValidator()
    {
        RuleFor(x => x.UserRut).NotEmpty().WithMessage("El rut del usuario es obligatorio");
        RuleFor(x => x.CompanyId).NotEmpty().WithMessage("El id de la empresa es obligatorio");
        RuleFor(x => x.Comments).NotEmpty().WithMessage("El comentario es obligatorio");
        RuleFor(x => x.Data).NotEmpty().WithMessage("El detalle es obligatorio");
        RuleForEach(x => x.Data)
            .ChildRules(data =>
            {
                data.RuleFor(x => x.BatchNum).NotEmpty().WithMessage("El numero de lote es obligatorio");
                data.RuleFor(x => x.ItemCode).NotEmpty().WithMessage("El codigo del item es obligatorio");
                data.RuleFor(x => x.WarehouseCode).NotEmpty().WithMessage("El codigo del almacén es obligatorio");
                data.RuleFor(x => x.Quantity).NotEmpty().WithMessage("La cantidad es obligatoria");
            });
    }
}
